//
//  SuspendAccountAction.swift
//  CUIModule
//
//  Created by <PERSON><PERSON> on 6/3/2567 BE.
//

import UIKit

public enum SuspendAccountAction {
    case closeAccount
    case delete(hasFunds: Bool)

    public var title: String {
        switch self {
        case .closeAccount:
            return "key0771".localized()
        case .delete:
            return "key0605".localized()
        }
    }
    
    public var image: UIImage {
        switch self {
        case .closeAccount: 
            return .image(named: "ic_close")
        case .delete:
            return .image(named: "ic_close")
        }
    }
    
    public var warning: String {
        return "Please ensure to withdraw funds from the wallet and close any active positions to prepare for the account deletion process."
    }
    
    public var subDescription: String {
        switch self {
        case .closeAccount: return "deactivation"
        case .delete: return "deletion"
        }
    }
    
    public var desc: String {
        return "key0975".localized()
    }
    
    public var questions: [String] {
        switch self {
        case .closeAccount:
            return ["key0797".localized(),
                    "key0798".localized(),
                    "key0799".localized(),
                    "key0800".localized(),
                    "key0801".localized()]
            
        case .delete:
            return ["key0798".localized(),
                    "Moving to a different trading platform.",
                    "Permanent removal for decluttering.",
                    "Finding it difficult using this app.",
                    "Persistent problems with app performance.",
                    "Unsatisfactory customer experiences.",
                    "Rather not to say."]
        }
    }
}
