//
//  AppWindow.swift
//  CUIModule
//
//  Created by <PERSON><PERSON> on 11/29/66.
//

import UIKit

public extension UIWindow {
    func set(_ theme: AppTheme) {
        if #available(iOS 13.0, *) {
            self.overrideUserInterfaceStyle = theme.value
        }
    }
}

public var keyWindow: UIWindow? {
    if #available(iOS 13.0, *) {
        return UIApplication.shared.connectedScenes
            .compactMap { $0 as? UIWindowScene }
            .flatMap { $0.windows }
            .last { $0.isKeyWindow }
    } else {
        return UIApplication.shared.keyWindow
    }
}
