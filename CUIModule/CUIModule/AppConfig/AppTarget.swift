//
//  AppTarget.swift
//  CUIModule
//
//  Created by <PERSON><PERSON><PERSON> on 11/29/66.
//

import Foundation

public enum AppTarget: Int {
    case dev = 0
    case uat = 1
    case prod = 2

    public static func get(_ value: Int?) -> AppTarget {
        AppTarget(rawValue: value ?? 1) ?? .uat
    }

    public var testAccounts: [TestAccount] {
        let pass = "Ab@12345"
        switch self {
        case .dev, .uat:
            return [
                .init(title: "Tester 1", username: "<EMAIL>", password: pass),
                .init(title: "Tester 2", username: "<EMAIL>", password: pass),
                .init(title: "Tester 3", username: "<EMAIL>", password: pass),
                .init(title: "Tester 4", username: "<EMAIL>", password: pass),
                .init(title: "Tester 5", username: "<EMAIL>", password: pass),
                .init(title: "Tester 6", username: "<EMAIL>", password: pass),
                .init(title: "Tester 7", username: "<EMAIL>", password: pass),
                .init(title: "Tester 8", username: "<EMAIL>", password: pass),
                .init(title: "Tester 9", username: "<EMAIL>", password: pass),
                .init(title: "Tester 10", username: "<EMAIL>", password: pass)
            ]
        case .prod:
            return [
                .init(title: "Simon Russell", username: "<EMAIL>", password: pass),
                .init(title: "Abigail Bell", username: "<EMAIL>", password: pass),
                .init(title: "Steven Young", username: "<EMAIL>", password: pass),
                .init(title: "Samantha Walsh", username: "<EMAIL>", password: pass),
                .init(title: "Rebecca Nolan", username: "<EMAIL>", password: pass)
            ]
        }
    }

    public struct TestAccount {
        public let title: String
        public let username: String
        public let password: String
    }
}
