//
//  AppTheme.swift
//  CUIModule
//
//  Created by <PERSON><PERSON><PERSON> on 11/29/66.
//

import UIKit
import SharedData

public var currentAppTheme: UIUserInterfaceStyle {

    guard let window = keyWindow else {
        return .light
    }
    return window.traitCollection.userInterfaceStyle
}

public enum AppSetting: Int, CaseIterable {
    case editProfile
    case bankAccountDetails
    case security
    case termsAndConditions
    case privacyPolicy
    case language
    
    public static var allCases: [AppSetting] {
        [.editProfile,
         .bankAccountDetails,
         .security,
         .privacyPolicy,
         .language]
    }
    
    public var title: String {
        switch self {
        case .editProfile: 
            return "key0263".localized()
        case .bankAccountDetails: 
            return "key0264".localized()
        case .security:
            return "key0266".localized()
        case .termsAndConditions:
            return "key0267".localized()
        case .privacyPolicy:
            return "key0268".localized()
        case .language:
            return "key0734".localized()
        }
    }
    
    public var icon: UIImage {
        switch self {
        case .editProfile:
            return .image(named: "merit_ic_editprofile")
        case .bankAccountDetails:
            return .image(named: "merit_ic_bankaccount")
        case .security:
            return .image(named: "merit_ic_security")
        case .termsAndConditions: 
            return .image(named: "merit_ic_terms")
        case .privacyPolicy:
            return .image(named: "merit_ic_privacy")
        case .language:
            return .image(named: "ic_language")
        }
    }
    
    public var detail: String? {
        switch self {
        case .language:
            return LocalizeManager.currentLanguage.title
            
        default:
            return nil
        }
    }
    
    public var htmlFileName: String {
        switch self {
        case .privacyPolicy:
            switch LocalizeManager.currentLanguage {
            case .english:
                return "Merit-Privacy-Policy-html-v2"
            case .hongKong:
                return "Merit-Privacy-Policy-CN-html-v2"
            }
            
        default:
            return ""
        }
    }
}

public enum AppTheme: Int, CaseIterable {
    case dark = 0
    case light = 1
    case device = 2

    public static let `default` = AppTheme.light

    public var value: UIUserInterfaceStyle {
        switch self {
        case .device: return UIScreen.main.traitCollection.userInterfaceStyle
        case .light: return .light
        case .dark: return .dark
        }
    }

    public var title: String {
        switch self {
        case .device: return "SAME AS DEVICE THEME"
        case .light: return "key0831".localized()
        case .dark: return "key0830".localized()
        }
    }

    public var description: String {
        switch self {
        case .device: return "We will adjust appearance based on your device system settings."
        default: return ""
        }
    }

    public var currentThemeTitle: String {
        switch self {
        case .device: return "SAME AS DEVICE THEME"
        case .light: return "LIGHT MODE"
        case .dark: return "DARK MODE"
        }
    }
}
