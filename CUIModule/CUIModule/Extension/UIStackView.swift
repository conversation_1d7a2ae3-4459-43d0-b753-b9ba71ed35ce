//
//  UIStackView.swift
//  CUIModule
//
//  Created by <PERSON> on 21/11/2023.
//

import UIKit

public extension UIStackView {
    
    convenience init(backgroundColor: UIColor? = nil,
                     axis: NSLayoutConstraint.Axis = .horizontal,
                     alignment: UIStackView.Alignment = .fill,
                     distribution: UIStackView.Distribution = .fill,
                     spacing: CGFloat = 0.0,
                     cornerRadius: CGFloat = 0.0,
                     tag: Int = 0) {
        self.init()
        
        if let backgroundColor = backgroundColor {
            self.backgroundColor = backgroundColor
        }
        self.axis = axis
        self.alignment = alignment
        self.distribution = distribution
        self.spacing = spacing
        if cornerRadius > 0 {
            self.roundCorners(radius: cornerRadius)
        }
        self.tag = tag
    }
    
    /// Add a list of arranged subviews
    /// - Parameter subviews: List of arranged subviews to be added
    func addArrangedSubviews(_ subviews: [UIView]) {
        subviews.forEach { self.addArrangedSubview($0) }
    }
}
