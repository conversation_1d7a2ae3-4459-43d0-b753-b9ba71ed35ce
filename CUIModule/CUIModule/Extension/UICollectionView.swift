//
//  UICollectionView.swift
//  CUIModule
//
//  Created by <PERSON> on 21/11/2023.
//

import UIKit

public extension UICollectionView {
    
    convenience init(layout: UICollectionViewLayout,
                     backgroundColor: UIColor? = nil,
                     showsVerticalScrollIndicator: Bool = true,
                     showsHorizontalScrollIndicator: Bool = true,
                     contentInset: UIEdgeInsets = .zero,
                     scrollIndicatorInsets: UIEdgeInsets = .zero,
                     isScrollEnabled: Bool = true,
                     isPagingEnabled: Bool = false,
                     tag: Int = 0) {
        self.init(frame: .zero, collectionViewLayout: layout)
        
        if let backgroundColor = backgroundColor {
            self.backgroundColor = backgroundColor
        }
        self.showsVerticalScrollIndicator = showsVerticalScrollIndicator
        self.showsHorizontalScrollIndicator = showsHorizontalScrollIndicator
        self.contentInset = contentInset
        self.scrollIndicatorInsets = scrollIndicatorInsets
        self.isScrollEnabled = isScrollEnabled
        self.isPagingEnabled = isPagingEnabled
        self.tag = tag
    }
}

// MARK: - UICollectionReusableView
public extension UICollectionReusableView {
    
    static var reuseIdentifier: String {
        String(describing: Self.self)
    }
}
