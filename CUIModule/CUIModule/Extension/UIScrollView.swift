//
//  UIScrollView.swift
//  CUIModule
//
//  Created by <PERSON><PERSON><PERSON> on 11/17/66.
//

import UIKit

public extension UIScrollView {
    
    convenience init(backgroundColor: UIColor? = nil,
                     isScrollEnabled: Bool = true,
                     showsVerticalScrollIndicator: Bool = true,
                     tag: Int = 0) {
        self.init()
        
        if let backgroundColor = backgroundColor {
            self.backgroundColor = backgroundColor
        }
        self.isScrollEnabled = isScrollEnabled
        self.showsVerticalScrollIndicator = showsVerticalScrollIndicator
        self.tag = tag
    }
    /**
     Scroll to the bottom-most content offset.
     
     UIScrollView().scrollToBottom(animated: true)
     - Parameter animated: `true` to animate the transition at a constant
     velocity to the new offset, `false` to make the transition immediate.
     */
    func scrollToBottom(animated: Bool = true) {
        let bottomOffset = CGPoint(x: 0, y: contentSize.height - (bounds.size.height + contentInset.bottom))
        if bottomOffset.y > 0 {
            setContentOffset(bottomOffset, animated: animated)
        }
    }
    
    var minContentOffset: CGPoint {
        return CGPoint(
            x: -self.contentInset.left,
            y: -self.contentInset.top
        )
    }
    
    var maxContentOffset: CGPoint {
        return CGPoint(
            x: self.contentSize.width - self.bounds.width + self.contentInset.right,
            y: self.contentSize.height - self.bounds.height + self.contentInset.bottom
        )
    }
}
