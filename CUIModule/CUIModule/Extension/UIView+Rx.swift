//
//  UIView+Rx.swift
//  CUIModule
//
//  Created by <PERSON><PERSON><PERSON> on 7/5/67.
//
import RxCocoa
import RxSwift

public extension Reactive where Base: UIView {

    var willMoveToWindow: ControlEvent<UIWindow?> {
        let source = self.methodInvoked(#selector(Base.willMove)).map { $0.first as? UIWindow }
        return ControlEvent(events: source)
    }

    var willMoveToSuperview: ControlEvent<UIView?> {
        let source = self.methodInvoked(#selector(Base.willMove)).map { $0.first as? UIView }
        return ControlEvent(events: source)
    }

    var didMoveToWindow: ControlEvent<Void> {
        let source = self.methodInvoked(#selector(Base.didMoveToWindow)).mapToVoid()
        return ControlEvent(events: source)
    }

    var didMoveToSuperview: ControlEvent<Void> {
        let source = self.methodInvoked(#selector(Base.didMoveToSuperview)).mapToVoid()
        return ControlEvent(events: source)
    }

    var layoutSubviews: ControlEvent<Void> {
        let source = self.methodInvoked(#selector(Base.layoutSubviews)).mapToVoid()
        return ControlEvent(events: source)
    }

}
