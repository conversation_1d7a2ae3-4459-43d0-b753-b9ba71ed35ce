//
//  ScrollViewPortAnimatedViewController.swift
//  CUIModule
//
//  Created by <PERSON><PERSON> on 11/20/66.
//

import UIKit
import RxSwift
import RxCocoa

public protocol ScrollViewPortAnimation {
    var scrollAnimateView: UIScrollView? { get set }
    var headerAnimateView: UIView? { get set }
    var stickyAnimateView: UIView? { get set }
    var stickyAdjustPadding: CGFloat { get set }
}

open class ScrollViewPortAnimatedViewController: BaseViewController, ScrollViewPortAnimation {

    public var scrollAnimateView: UIScrollView?
    public var headerAnimateView: UIView?
    public var stickyAnimateView: UIView?
    public var stickyAdjustPadding: CGFloat = 0

    private var viewAppeared: Bool = false
    private var isViewVisible: Bool = false

    private let snapAnimateDuration: TimeInterval = 0.1
    public var lastScrollOffset: CGFloat = 0
    public var originalScrollOffset: CGFloat = 0
    private var headerOffset: CGFloat = 0
    private var stickyOffset: CGFloat = 0
    private var tabbarOffset: (min: CGFloat, max: CGFloat) = (min: 0, max: 0)
    private var isScrollingDown: Bool = false
    private var userEndedDrag: Bool = false

    private var dropShawAlpha: CGFloat {
        return currentAppTheme == .light ? 0.07 : 0.5
    }

    public let onScrollViewPortAnimating = PublishSubject<CGFloat>()
    public let onScrollViewPortSnap = PublishSubject<Bool>()

    public var isViewPortExpanded = false

    open override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        isViewVisible = true
        if viewAppeared { return }
        viewAppeared = true
        addTopSafeAreaOverlay()
        setOffsetValues()
    }

    open override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        isViewVisible = false
    }

    open override func viewDidDisappear(_ animated: Bool) {
        super.viewDidDisappear(animated)
        resetViewPort()
    }

    open override func viewWillLayoutSubviews() {
        super.viewWillLayoutSubviews()
        setDropShadow()
    }

    private func addTopSafeAreaOverlay() {
        let topSafeAreaHeight = view.safeAreaInsets.top
        guard topSafeAreaHeight > 0 else { return }
        let overlayView = UIView()
        overlayView.backgroundColor = view.backgroundColor
        view.addSubview(overlayView)
        overlayView.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.height.equalTo(topSafeAreaHeight)
        }
    }

    public func setUpScrollAnimation(
        scrollView: UIScrollView,
        headerView: UIView?,
        stickyView: UIView?,
        stickyAdjustPadding: CGFloat = 0
    ) {
        self.scrollAnimateView = scrollView
        self.scrollAnimateView?.decelerationRate = .fast
        self.headerAnimateView = headerView
        self.stickyAnimateView = stickyView
        self.stickyAdjustPadding = stickyAdjustPadding

        setDropShadow()
        bindScrollAction(scrollView)
    }

    private func bindScrollAction(_ scrollView: UIScrollView) {

        scrollView.rx.willBeginDragging.subscribe(onNext: { [weak self] in
            self?.userEndedDrag = false
        }).disposed(by: disposeBag)

        scrollView.rx.didScroll.subscribe(onNext: { [weak self] in
            guard let self = self, !self.userEndedDrag else {
                return
            }
            self.animateViewPort(scrollView)
        }).disposed(by: disposeBag)

        scrollView.rx.didEndDragging.subscribe(onNext: { [weak self] willDecelerate in
            guard let self = self else { return }
            self.userEndedDrag = true
            if !willDecelerate {
                self.snapViewPort()
                self.userEndedDrag = false
            }
            self.updateScrollOffset(scrollView)
        }).disposed(by: disposeBag)

        scrollView.rx.didEndDecelerating.subscribe(onNext: { [weak self] in
            guard let self = self else { return }
            self.snapViewPort()
            self.userEndedDrag = false
            self.updateScrollOffset(scrollView)
        }).disposed(by: disposeBag)
    }

    private func setOffsetValues() {
        guard let scrollView = scrollAnimateView else { return }

        originalScrollOffset = scrollView.contentOffset.y
        updateScrollOffset(scrollView)

        let topSafeAera = keyWindow?.safeAreaInsets.top ?? 0
        if let header = headerAnimateView {
            headerOffset = topSafeAera - (header.frame.origin.y + header.frame.height)
        }

        if let sticky = stickyAnimateView {
            stickyOffset = topSafeAera - sticky.frame.origin.y + stickyAdjustPadding
        }

        if let frame = self.tabBarController?.tabBar.frame {
            tabbarOffset = (
                min: self.view.frame.size.height - frame.size.height,
                max: self.view.frame.size.height
            )
        }
    }

    private func setDropShadow() {
        if let headerView = headerAnimateView {
            setUpShadow(for: headerView, opacity: isViewPortExpanded ? 0 : 1)
        }

        if let stickyView = stickyAnimateView {
            setUpShadow(for: stickyView, opacity: isViewPortExpanded ? 1 : 0)
        }
    }

    private func setUpShadow(for view: UIView, opacity: Float) {
        view.dropShadow(
            alpha: dropShawAlpha, opacity: opacity,
            offset: .init(width: 0, height: 1),
            radius: 1
        )
    }

    private func updateShadow(for view: UIView, opacity: Float) {
        view.layer.shadowColor = UIColor.black.withAlphaComponent(dropShawAlpha).cgColor
        view.layer.shadowOpacity = opacity
    }
}
// MARK: - Scrolling Animation
extension ScrollViewPortAnimatedViewController {

    private func animateViewPort(_ scrollView: UIScrollView) {
        guard isViewVisible, scrollAnimateView == scrollView else { return }
        isScrollingDown = lastScrollOffset < scrollView.contentOffset.y
        viewPortAnimation(scrollView)
        updateScrollOffset(scrollView)
    }

    private func viewPortAnimation(_ scrollView: UIScrollView) {

        let maxScrollY = scrollView.contentSize.height - scrollView.frame.height
        guard scrollView.contentOffset.y > originalScrollOffset,
              scrollView.contentOffset.y < maxScrollY else {
            return
        }

        let offset = lastScrollOffset - scrollView.contentOffset.y

        if let header = headerAnimateView {
            let translateY = min(0, max(headerOffset, header.transform.ty + offset))
            header.transform = CGAffineTransform.identity.translatedBy(x: 0, y: translateY)
            updateShadow(for: header, opacity: 1 - Float(abs(translateY/headerOffset)))
        }

        if let sticky = stickyAnimateView {
            let translateY = min(0, max(stickyOffset, sticky.transform.ty + offset))
            sticky.transform = CGAffineTransform.identity.translatedBy(x: 0, y: translateY)
            updateShadow(for: sticky, opacity: Float(abs(translateY/stickyOffset)))
        }

        if let tabbar = self.tabBarController?.tabBar {
            let translateY = max(tabbarOffset.min, min(tabbarOffset.max, tabbar.frame.origin.y - offset))
            tabbar.frame.origin.y = translateY
        }

        onScrollViewPortAnimating.onNext(offset)
    }

    private func updateScrollOffset(_ scrollView: UIScrollView) {
        self.lastScrollOffset = scrollView.contentOffset.y
    }

    private func snapViewPort() {
        guard isViewVisible else { return }
        let contentInset = (scrollAnimateView?.contentInset.top ?? 0) - (scrollAnimateView?.contentInset.bottom ?? 0)
        if scrollAnimateView?.frame.height ?? 0 > (scrollAnimateView?.contentSize.height ?? 0) + contentInset {
            return
        }
        snapTabbar(hide: isScrollingDown)
        snapViews(hide: isScrollingDown)
        onScrollViewPortSnap.onNext(isScrollingDown)
    }

    public func resetViewPort(scrollBackToTop: Bool = false) {
        guard isViewVisible else { return }
        snapTabbar(hide: false)
        snapViews(hide: false)
        if scrollBackToTop {
            scrollAnimateView?.contentOffset = CGPoint(x: 0, y: -(scrollAnimateView?.contentInset.top ?? 0))
        }
    }

    private func snapViews(hide: Bool) {
        guard let header = headerAnimateView else { return }
        isViewPortExpanded = hide
        header.layer.removeAllAnimations()
        UIView.animate(
            withDuration: snapAnimateDuration, delay: 0,
            options: [.curveEaseOut], animations: { [weak self] in
                guard let self = self else { return }
                header.transform = CGAffineTransform.identity.translatedBy(
                    x: 0,
                    y: hide ? self.headerOffset : 0
                )
                self.updateShadow(for: header, opacity: hide ? 0 : 1)

                if let sticky = self.stickyAnimateView {
                    sticky.transform = CGAffineTransform.identity.translatedBy(
                        x: 0,
                        y: hide ? self.stickyOffset : 0
                    )
                    self.updateShadow(for: sticky, opacity: hide ? 1 : 0)
                }
            }
        )
    }

    private func snapTabbar(hide: Bool) {
        if let tabbar = self.tabBarController?.tabBar {
            tabbar.layer.removeAllAnimations()
            UIView.animate(
                withDuration: snapAnimateDuration, delay: 0,
                options: [.curveEaseOut], animations: { [weak self] in
                    guard let self = self else { return }
                    tabbar.frame.origin.y = hide ? self.tabbarOffset.max : self.tabbarOffset.min
                }
            )
        }
    }
}
