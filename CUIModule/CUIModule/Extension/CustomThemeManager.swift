//
//  CustomThemeManager.swift
//  CUIModule
//
//  Created by <PERSON><PERSON><PERSON> on 2/23/67.
//
import UIKit
import Storage

public extension CustomThemeManager {

    func getCurrentTheme() -> ThemeColorPreset? {
        guard let theme = current else { return nil }
        return ThemeColorPreset(model: theme)
    }

    func getButtonThemeColor(intent color: UIColor) -> ThemeColor? {
        guard let currentTheme = getCurrentTheme() else {
            return nil
        }

        if color == Color.bgButtonDefault || ThemeColor.isPrimaryColor(color) {
            return currentTheme.primary
        } else if color == Color.bgButtonNeutral || ThemeColor.isSecondaryColor(color) {
            return currentTheme.secondary
        } else {
            return nil
        }
    }

    func getButtonColor(intent color: UIColor) -> UIColor {
        guard let themeColor = getButtonThemeColor(intent: color) else {
            return color
        }
        return themeColor.color
    }

    func getPrimaryThemeColor() -> ThemeColor? {
        return getCurrentTheme()?.primary
    }

    func getSecondaryThemeColor() -> ThemeColor? {
        return getCurrentTheme()?.secondary
    }

    func getThirdColor() -> UIColor {
        guard let currentTheme = getCurrentTheme() else {
            return UIColor(named: "bgButtonNeutralPale", in: Bundle.this, compatibleWith: nil) ?? .clear
        }

        return currentTheme.third.color
    }
    
    func getTextHighLightColor() -> UIColor {
        guard let currentTheme = getCurrentTheme() else {
            return UIColor(named: "txtLabel", in: Bundle.this, compatibleWith: nil) ?? .clear
        }
        
        return currentTheme.textHighlight.color
    }
    
    func getBgDefaultColor() -> UIColor {
        guard let currentTheme = getCurrentTheme() else {
            return UIColor(named: "bgDefault", in: Bundle.this, compatibleWith: nil) ?? .clear
        }
        
        return currentTheme.textHighlight.bgDefaultColor
    }
    
    func getAccentColor() -> UIColor {
        if getCurrentTheme() == nil {
            return UIColor(named: "bgDefaultAccent", in: Bundle.this, compatibleWith: nil) ?? .clear
        }
        return UIColor(named: "bgdefaultaccentcustom", in: Bundle.this, compatibleWith: nil) ?? .clear
    }
    
    func getToneColor() -> UIColor {
        if getCurrentTheme() == nil {
            return UIColor(named: "bgDefaultTone", in: Bundle.this, compatibleWith: nil) ?? .clear
        }
        return UIColor(named: "bgdefaulttonecustom", in: Bundle.this, compatibleWith: nil) ?? .clear
    }
    
    func getToneDarkerColor() -> UIColor {
        if getCurrentTheme() == nil {
            return UIColor(named: "bgDefaultToneDarker", in: Bundle.this, compatibleWith: nil) ?? .clear
        }
        return UIColor(named: "bgdefaulttonedarkercustom", in: Bundle.this, compatibleWith: nil) ?? .clear
    }
    
    func getLabelNeutralColor() -> UIColor {
        if getCurrentTheme() == nil {
            return UIColor(named: "txtLabelNeutral", in: Bundle.this, compatibleWith: nil) ?? .clear
        }
        return UIColor(named: "txtParagraph", in: Bundle.this, compatibleWith: nil) ?? .clear
    }
    
    func getGraphVolColor() -> UIColor {
        if getCurrentTheme() == nil {
            return UIColor(named: "bgGraphVol", in: Bundle.this, compatibleWith: nil) ?? .clear
        }
        return UIColor(named: "txtDisabled", in: Bundle.this, compatibleWith: nil) ?? .clear
    }
}
// MARK: - Action
public extension CustomThemeManager {

    func enable(_ isEnable: Bool) {
        LocalPreference.enableCustomTheme = isEnable

        NotificationCenter.default.post(
            name: CustomThemeManager.observerName,
            object: nil
        )

        changeAppIcon(textHightLightTheme: isEnable ? CustomThemeManager.shared.getCurrentTheme()?.textHighlight : nil)

        guard isEnable else { return }
        // if there's no activated theme
        // activate the most recent one
        let saveThemes = getSavedThemes()
        if saveThemes.first(where: { $0.isEnable }) == nil,
           let mostRecentTheme = saveThemes.first {
            activateTheme(activate: true, data: mostRecentTheme)
        }
    }
    
    func activateTheme(activate: Bool, data: CustomThemeModel) {

        var savedThemes = LocalPreference.customThemes ?? []
        guard let toUpdateIndex = savedThemes.firstIndex(where: { $0.timeInterval == data.timeInterval }) else {
            return
        }

        // reset activate for saved themes if new one is activate
        if activate {
            for index in 0..<savedThemes.count {
                savedThemes[index].isEnable = false
            }
        }
        savedThemes[toUpdateIndex].isEnable = activate

        LocalPreference.customThemes = savedThemes
        LocalPreference.enableCustomTheme = !(savedThemes.filter { $0.isEnable }.isEmpty)

        NotificationCenter.default.post(
            name: CustomThemeManager.observerName,
            object: nil
        )
        changeAppIcon(textHightLightTheme: CustomThemeManager.shared.getCurrentTheme()?.textHighlight)
    }

    func saveTheme(_ theme: CustomThemeModel) {

        var savedThemes = LocalPreference.customThemes ?? []

        // reset activate for saved themes if new one is activate
        if theme.isEnable {
            for index in 0..<savedThemes.count {
                savedThemes[index].isEnable = false
            }
            LocalPreference.enableCustomTheme = true
        }

        if let firstIndex = savedThemes.firstIndex(where: { $0.timeInterval == theme.timeInterval }) {
            savedThemes[firstIndex] = theme
        } else {
            savedThemes.append(theme)
        }

        LocalPreference.customThemes = savedThemes

        if theme.isEnable {
            NotificationCenter.default.post(
                name: CustomThemeManager.observerName,
                object: nil
            )
            changeAppIcon(textHightLightTheme: CustomThemeManager.shared.getCurrentTheme()?.textHighlight)
        }
    }

    func deleteTheme(_ theme: CustomThemeModel) {
        let savedThemes = LocalPreference.customThemes?.filter {
            $0.timeInterval != theme.timeInterval
        } ?? []

        LocalPreference.customThemes = savedThemes
        LocalPreference.enableCustomTheme = !(savedThemes.filter { $0.isEnable }.isEmpty)

        if theme.isEnable {
            NotificationCenter.default.post(
                name: CustomThemeManager.observerName,
                object: nil
            )
            changeAppIcon(textHightLightTheme: CustomThemeManager.shared.getCurrentTheme()?.textHighlight)
        }
    }

    private func changeAppIcon(textHightLightTheme: ThemeColor?) {

        guard UIApplication.shared.supportsAlternateIcons else {
            return
        }

        let appIconName = textHightLightTheme?.appIconName ?? "AppIconDefault"
        UIApplication.shared.setAlternateIconName(appIconName) { error in
            if let error = error {
                print("Error changing app icon: \(error.localizedDescription)")
            } else {
                print("App icon changed successfully.")
            }
        }
    }
}
