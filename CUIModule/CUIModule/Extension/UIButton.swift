//
//  UIButton.swift
//  CUIModule
//
//  Created by <PERSON> on 21/11/2023.
//

import UIKit
// swiftlint:disable cyclomatic_complexity
public extension UIButton {
    
    convenience init(type: ButtonType = .custom,
                     backgroundColor: UIColor? = nil,
                     tintColor: UIColor? = nil,
                     titleFont: UIFont? = nil,
                     title: String? = nil,
                     normalTitleColor: UIColor? = nil,
                     selectedTitleColor: UIColor? = nil,
                     disabledTitleColor: UIColor? = nil,
                     normalBGImage: UIImage? = nil,
                     selectedBGImage: UIImage? = nil,
                     disabledBGImage: UIImage? = nil,
                     normalImage: UIImage? = nil,
                     selectedImage: UIImage? = nil,
                     disabledImage: UIImage? = nil,
                     semanticContentAttribute: UISemanticContentAttribute? = nil,
                     contentHorizontalAlignment: UIControl.ContentHorizontalAlignment? = nil,
                     contentVerticalAlignment: UIControl.ContentVerticalAlignment? = nil,
                     cornerRadius: CGFloat? = nil,
                     borderColor: UIColor? = nil,
                     borderWidth: CGFloat? = nil,
                     contentEdgeInsets: UIEdgeInsets? = nil,
                     titleInsets: UIEdgeInsets? = nil,
                     imageInsets: UIEdgeInsets? = nil,
                     isUserInteractionEnabled: Bool = true,
                     tag: Int = 0,
                     isEnable: Bool = true) {
        self.init(type: type)

        if let backgroundColor = backgroundColor {
            self.backgroundColor = backgroundColor
        }
        if let tintColor = tintColor {
            self.tintColor = tintColor
        }
        if let titleFont = titleFont {
            self.titleLabel?.font = titleFont
        }
        if let title = title {
            self.setTitle(title, for: .normal)
        }
        if let normalTitleColor = normalTitleColor {
            self.setTitleColor(normalTitleColor, for: .normal)
        }
        if let selectedTitleColor = selectedTitleColor {
            self.setTitleColor(selectedTitleColor, for: .selected)
        }
        if let disabledTitleColor = disabledTitleColor {
            self.setTitleColor(disabledTitleColor, for: .disabled)
        }
        if let normalBGImage = normalBGImage {
            self.setBackgroundImage(normalBGImage, for: .normal)
        }
        if let selectedBGImage = selectedBGImage {
            self.setBackgroundImage(selectedBGImage, for: .selected)
        }
        if let disabledBGImage = disabledBGImage {
            self.setBackgroundImage(disabledBGImage, for: .disabled)
        }
        if let normalImage = normalImage {
            self.setImage(normalImage, for: .normal)
        }
        if let selectedImage = selectedImage {
            self.setImage(selectedImage, for: .selected)
        }
        if let disabledImage = disabledImage {
            self.setImage(disabledImage, for: .disabled)
        }
        if let semanticContentAttribute = semanticContentAttribute {
            self.semanticContentAttribute = semanticContentAttribute
        }
        if let contentHorizontalAlignment = contentHorizontalAlignment {
            self.contentHorizontalAlignment = contentHorizontalAlignment
        }
        if let contentVerticalAlignment = contentVerticalAlignment {
            self.contentVerticalAlignment = contentVerticalAlignment
        }
        if let cornerRadius = cornerRadius {
            self.layer.cornerRadius = cornerRadius
            self.layer.masksToBounds = true
        }
        if let borderColor = borderColor {
            self.layer.borderColor = borderColor.cgColor
        }
        if let borderWidth = borderWidth {
            self.layer.borderWidth = borderWidth
        }
        if let contentEdgeInsets = contentEdgeInsets {
            self.contentEdgeInsets = contentEdgeInsets
        }
        if let titleInsets = titleInsets {
            self.titleEdgeInsets = titleInsets
        }
        if let imageInsets = imageInsets {
            self.imageEdgeInsets = imageInsets
        }
        
        self.isUserInteractionEnabled = isUserInteractionEnabled
        self.tag = tag
        self.isEnabled = isEnable
    }
    
    func rightImage(image: UIImage,
                    padding: CGFloat = 8,
                    width: CGFloat = 12,
                    height: CGFloat = 12) {
        let newRightItemToAdd = UIImageView(image: image)
        addSubview(newRightItemToAdd)
        
        newRightItemToAdd.snp.makeConstraints { make in
            make.width.equalTo(width)
            make.height.equalTo(height)
            make.centerY.equalToSuperview()
            make.right.equalTo(-padding)
        }
    }
    
    func updateRightImage(_ image: UIImage) {
        subviews.compactMap { $0 as? UIImageView }.last?.image = image
    }
    
    func setImageTintColor(_ color: UIColor, state: UIControl.State) {
        setImage(self.image(for: state)?.withRenderingMode(.alwaysTemplate), for: state)
        tintColor = color
    }
    
    func setHighlightText(_ fullText: String, 
                          normalColor: UIColor,
                          normalFont: UIFont,
                          highlightText: [String],
                          highlightColor: UIColor,
                          highlightFont: UIFont,
                          underline: Bool = false,
                          state: UIControl.State) {
        let nsString = fullText as NSString
        let attributedString = NSMutableAttributedString(string: fullText,
                                                         attributes: [.font: normalFont,
                                                                      .foregroundColor: normalColor])
        var highlightRanges: [NSRange] = []
        for highlight in highlightText {
            let range = nsString.range(of: highlight)
            if underline {
                attributedString.addAttributes([.foregroundColor: highlightColor,
                                                .font: highlightFont,
                                                .underlineStyle: NSUnderlineStyle.single.rawValue],
                                               range: range)
            } else {
                attributedString.addAttributes([.foregroundColor: highlightColor,
                                                .font: highlightFont],
                                               range: range)
            }
            highlightRanges.append(range)
        }
        
        setAttributedTitle(attributedString, for: state)
    }
    
    func configTitle(_ config: TitleConfig) {
        setHighlightText(config.title,
                         normalColor: config.color,
                         normalFont: config.font,
                         highlightText: [config.highlightText ?? ""],
                         highlightColor: config.highlightColor ?? config.color,
                         highlightFont: config.highlightFont ?? config.font,
                         underline: config.underline,
                         state: config.state)
    }
    
    // MARK: - Config model
    struct TitleConfig {
        let title: String
        let font: UIFont
        let color: UIColor
        var highlightText: String?
        var highlightFont: UIFont?
        var highlightColor: UIColor?
        var underline: Bool = false
        var state: UIControl.State = .normal
        
        public init(title: String,
                    font: UIFont,
                    color: UIColor,
                    highlightText: String? = nil,
                    highlightFont: UIFont? = nil,
                    highlightColor: UIColor? = nil,
                    underline: Bool = false,
                    state: UIControl.State = .normal) {
            self.title = title
            self.font = font
            self.color = color
            self.highlightText = highlightText
            self.highlightFont = highlightFont
            self.highlightColor = highlightColor
            self.underline = underline
            self.state = state
        }
    }
}
// swiftlint:enable cyclomatic_complexity
