//
//  UIImageView.swift
//  CUIModule
//
//  Created by <PERSON> C<PERSON> on 21/11/2023.
//
import Storage
import UIKit

public extension UIImageView {
    
    convenience init(backgroundColor: UIColor? = nil,
                     tintColor: UIColor? = nil,
                     contentMode: UIView.ContentMode = .scaleToFill,
                     cornerRadius: CGFloat = .zero,
                     borderWidth: CGFloat = .zero,
                     borderColor: CGColor? = nil,
                     image: UIImage? = nil,
                     tag: Int = .zero) {
        self.init()
        
        if let backgroundColor = backgroundColor {
            self.backgroundColor = backgroundColor
        }
        if let tintColor = tintColor {
            self.tintColor = tintColor
        }
        self.contentMode = contentMode
        self.layer.cornerRadius = cornerRadius
        if cornerRadius > .zero {
            self.layer.masksToBounds = true
        }
        self.layer.borderWidth = borderWidth
        if let borderColor = borderColor {
            self.layer.borderColor = borderColor
        }
        if let image = image {
            self.image = image
        }
        self.tag = tag
    }
    
    func setImage(with url: String?,
                  placeholder: UIImage = .image(named: "iconlogodisable")) {
        if let imageUrl = URL(string: url ?? "") {
            kf.setImage(with: .network(imageUrl),
                        placeholder: placeholder
            )
        } else {
            image = placeholder
        }
    }
}
