//
//  PriceColorTheme.swift
//  CUIModule
//
//  Created by <PERSON><PERSON> on 12/21/66.
//

import UIKit
import SharedData

extension PriceColorTheme {
    public var textColor: UIColor {
        switch self {
        case .positive: return Color.txtPositive
        case .negative: return Color.txtNegative
        case .neutral: return Color.txtParagraph
        }
    }

    public var foreground: UIColor {
        switch self {
        case .positive: return Color.txtLabelPositive
        case .negative: return Color.txtLabelNegative
        case .neutral: return Color.txtLabelNeutral
        }
    }

    public var background: UIColor {
        switch self {
        case .positive: return Color.bgButtonPositivePale
        case .negative: return Color.bgButtonNegativePale
        case .neutral: return Color.themeCustomThird1
        }
    }

    public var arrowIcon: UIImage {
        switch self {
        case .positive: return .image(named: "iconarrowupgreen")
        case .negative: return .image(named: "iconarrowdownred")
        case .neutral: return .image(named: "iconarrowrightgray")
        }
    }

}
