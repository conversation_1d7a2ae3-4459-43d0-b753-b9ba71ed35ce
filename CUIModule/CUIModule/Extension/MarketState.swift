//
//  MarketState.swift
//  CUIModule
//
//  Created by <PERSON><PERSON> on 12/21/66.
//
import UIKit
import SharedData

extension MarketState {
    public var textColor: UIColor {
        switch self {
        case .open: return Color.txtLabelPositive
        case .sessionbreak: return Color.bgButtonCaution
        case .close: return Color.txtLabelNeutral
        }
    }

    public var backgroundColor: UIColor {
        switch self {
        case .open: return Color.bgButtonPositivePale
        case .sessionbreak: return Color.bgButtonCautionPale
        case .close: return Color.themeCustomThird1
        }
    }

    public var statusColor: UIColor {
        switch self {
        case .open: return Color.bgButtonPositive
        case .sessionbreak: return Color.bgButtonCaution
        case .close: return Color.txtDisabled
        }
    }

    public var image: UIImage {
        switch self {
        case .open: return UIImage.image(named: "icmarketopen")
        case .sessionbreak: return UIImage.image(named: "icmarketsessionbreak")
        case .close: return UIImage.image(named: "icmarketclose")
        }
    }
}
