//
//  UISwitch.swift
//  CUIModule
//
//  Created by <PERSON><PERSON> on 12/21/66.
//

import UIKit

public extension UISwitch {

    func set(width: CGFloat, height: CGFloat) {
        let standardHeight: CGFloat = 31
        let standardWidth: CGFloat = 51

        let heightRatio = height / standardHeight
        let widthRatio = width / standardWidth

        transform = CGAffineTransform(scaleX: widthRatio, y: heightRatio)
    }
}
