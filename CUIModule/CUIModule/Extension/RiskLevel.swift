//
//  RiskLevel.swift
//  CUIModule
//
//  Created by <PERSON><PERSON> on 2/29/67.
//
import UIKit
import SharedData

public extension RiskLevel {
    
    var color: UIColor {
        switch self {
        case .low: 
            return Color.txtInfo
        case .medium:
            return Color.txtCaution
        case .high:
            return Color.txtNegative
        }
    }

    var bgColor: UIColor {
        switch self {
        case .low: 
            return UIColor(hexString: "#EBF9E7")
        case .medium:
            return UIColor(hexString: "#F0F2FF")
        case .high:
            return UIColor(hexString: "#F7EAFD")
        }
    }
    
    var chartColor: UIColor {
        switch self {
        case .low:
            return UIColor(hexString: "#337A48")
        case .medium:
            return UIColor(hexString: "#2F3F90")
        case .high:
            return UIColor(hexString: "#522283")
        }
    }
    
    var chartTitle: String {
        switch self {
        case .low:
            return "key0454".localized()
        case .medium:
            return "key0453".localized()
        case .high:
            return "key0452".localized()
        }
    }
    
    var badgeTitle: String {
        rawValue
    }
}
