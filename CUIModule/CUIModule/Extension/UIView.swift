//
//  UIView.swift
//  CUIModule
//
//  Created by <PERSON> on 21/11/2023.
//

import UIKit

public extension UIView {
    
    convenience init(backgroundColor: UIColor? = nil,
                     cornerRadius: CGFloat? = nil,
                     borderColor: UIColor? = nil,
                     borderWidth: CGFloat? = nil,
                     tag: Int = 0) {
        self.init()
        
        if let backgroundColor = backgroundColor {
            self.backgroundColor = backgroundColor
        }
        if let cornerRadius = cornerRadius {
            self.layer.cornerRadius = cornerRadius
            self.layer.masksToBounds = cornerRadius > 0.0
        }
        if let borderColor = borderColor {
            self.layer.borderColor = borderColor.cgColor
        }
        if let borderWidth = borderWidth {
            self.layer.borderWidth = borderWidth
        }
        
        self.tag = tag
    }
    
    /// Add a list of subviews
    /// - Parameter subviews: List of subviews
    func addSubviews(_ subviews: [UIView]) {
        subviews.forEach { addSubview($0) }
    }

    var isHiddenInStack: Bool {
        get {
            return isHidden
        }
        set {
            guard isHidden != newValue else { return }
            
            isHidden = newValue
        }
    }
}
