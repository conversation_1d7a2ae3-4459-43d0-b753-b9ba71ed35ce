//
//  UILabel.swift
//  CUIModule
//
//  Created by <PERSON><PERSON><PERSON> on 1/11/2566 BE.
//

import UIKit

public extension UILabel {
    
    convenience init(backgroundColor: UIColor? = nil,
                     font: UIFont? = nil,
                     numberOfLines: Int = 1,
                     text: String? = nil,
                     attributedText: NSAttributedString? = nil,
                     textColor: UIColor = .black,
                     textAlignment: NSTextAlignment? = nil,
                     cornerRadius: CGFloat = 0.0,
                     tag: Int = 0) {
        self.init()
        
        if let backgroundColor = backgroundColor {
            self.backgroundColor = backgroundColor
        }
        if let font = font {
            self.font = font
        }
        self.numberOfLines = numberOfLines
        if let text = text {
            self.text = text
        }
        self.textColor = textColor
        if let textAlignment = textAlignment {
            self.textAlignment = textAlignment
        }
        if let attributedText = attributedText {
            self.attributedText = attributedText
        }
        if cornerRadius > 0 {
            self.roundCorners(radius: cornerRadius)
        }
        self.tag = tag
    }
    
    func setMargins(_ margin: CGFloat = 8) {
        if let textString = self.text {
            let paragraphStyle = NSMutableParagraphStyle()
            paragraphStyle.firstLineHeadIndent = margin
            paragraphStyle.headIndent = margin
            paragraphStyle.tailIndent = -margin
            let attributedString = NSMutableAttributedString(string: textString)
            attributedString.addAttribute(
                .paragraphStyle,
                value: paragraphStyle,
                range: NSRange(location: 0, length: attributedString.length)
            )
            attributedText = attributedString
        }
    }
    
    /// Update displaying of UILabel
    /// - Parameter config: Config for updating display
    func update(with config: TextConfig) {
        if let attributedText = config.attributedText {
            self.attributedText = attributedText
        } else {
            self.text = config.text
        }

        if let font = config.font {
            self.font = font
        }
        
        if let textColor = config.textColor {
            self.textColor = textColor
        }
    }
    
    func setContent(leadingString: String, image: UIImage, trailingString: String) {
        let attributeString = NSMutableAttributedString(string: leadingString)
        
        if let attachmentString = NSTextAttachment.getCenteredImageAttachment(with: image, and: font) {
            attributeString.append(attachmentString)
        }
        
        attributeString.append(NSAttributedString(string: trailingString))
        self.attributedText = attributeString
    }
    
    func highlight(_ highlightText: String, highlightFont: UIFont? = nil, highlightColor: UIColor) {
        let attributedString: NSMutableAttributedString
        if let labelattributedText = self.attributedText {
            attributedString = NSMutableAttributedString(attributedString: labelattributedText)
        } else {
            attributedString = NSMutableAttributedString(string: text ?? "")
        }
        
        let range = attributedString.mutableString.range(of: highlightText)
        
        if let highlightFont = highlightFont {
            attributedString.addAttribute(NSAttributedString.Key.font,
                                          value: highlightFont,
                                          range: range)
        }
        attributedString.addAttribute(NSAttributedString.Key.foregroundColor,
                                      value: highlightColor,
                                      range: range)
        self.attributedText = attributedString
    }

    func setBulletText(
        strings: [String],
        bullet: String,
        spacing: Float = 1
    ) {
        var attributes = [NSAttributedString.Key: Any]()
        attributes[.font] = UIFont.preferredFont(forTextStyle: .body)
        attributes[.foregroundColor] = self.textColor
        attributes[.font] = font
        let paragraphStyle = NSMutableParagraphStyle()
        paragraphStyle.headIndent = (bullet as NSString).size(withAttributes: attributes).width
        paragraphStyle.lineSpacing = CGFloat(spacing)
        attributes[.paragraphStyle] = paragraphStyle

        let string = strings.map { return bullet + $0 }.joined(separator: "\n")
        self.attributedText = NSAttributedString(string: string, attributes: attributes)
    }
}

// MARK: - TextConfig
public struct TextConfig {
    let text: String?
    let localizeKey: String?
    let attributedText: NSAttributedString?
    let font: UIFont?
    let textColor: UIColor?
    let backgroundColor: UIColor?
    let cornerRadius: CGFloat?

    public init(text: String? = nil,
                localizeKey: String? = nil,
                attributedText: NSAttributedString? = nil,
                font: UIFont? = nil,
                textColor: UIColor? = nil,
                backgroudColor: UIColor? = nil,
                cornerRadius: CGFloat? = nil) {
        self.text = text
        self.localizeKey = localizeKey
        self.attributedText = attributedText
        self.font = font
        self.textColor = textColor
        self.backgroundColor = backgroudColor
        self.cornerRadius = cornerRadius
    }
}

// swiftlint:disable function_parameter_count
public extension UILabel {
    func highlight(
        _ fullText: String,
        normalColor: UIColor,
        normalFont: UIFont,
        highlightText: [String],
        highlightColor: UIColor,
        highlightFont: UIFont,
        lineSpacing: CGFloat = 1,
        underline: Bool = false,
        images: [UIImage] = [],
        imageTexts: [String] = [],
        imageWidth: CGFloat = 12,
        imageHeight: CGFloat = 12
    ) {
        self.numberOfLines = 0
        let nsString = fullText as NSString
        let paragraphStyle = NSMutableParagraphStyle()
        paragraphStyle.lineHeightMultiple = lineSpacing
        paragraphStyle.alignment = self.textAlignment
        let attributedString = NSMutableAttributedString(
            string: fullText,
            attributes: [
            .paragraphStyle: paragraphStyle,
            .font: normalFont,
            .foregroundColor: normalColor
        ])
        var highlightRanges: [NSRange] = []
        for highlight in highlightText {
            let range = nsString.range(of: highlight)
            if underline {
                attributedString.addAttributes([
                    .foregroundColor: highlightColor,
                    .font: highlightFont,
                    .underlineStyle: NSUnderlineStyle.single.rawValue
                ], range: range)
            } else {
                attributedString.addAttributes([
                    .foregroundColor: highlightColor,
                    .font: highlightFont
                ], range: range)
            }
            highlightRanges.append(range)
        }
        
        for (index, imageText) in imageTexts.enumerated() {
            let range = nsString.range(of: imageText)
            if range.location != NSNotFound {
                let attachment = NSTextAttachment()
                attachment.image = images[index]
                let mid = font.descender + font.capHeight
                attachment.bounds = CGRect(x: 0, y: font.descender - imageHeight / 2 + mid + 2,
                                           width: imageWidth, height: imageHeight)
                let imageString = NSAttributedString(attachment: attachment)
                attributedString.replaceCharacters(in: range, with: imageString)
            }
        }
        self.attributedText = attributedString
    }
}

// MARK: - NSTextAttachment
extension NSTextAttachment {
    static func getCenteredImageAttachment(with image: UIImage,
                                           and font: UIFont?) -> NSAttributedString? {
        guard let font = font else { return nil }
        
        let imageAttachment = NSTextAttachment()
        imageAttachment.bounds = CGRect(x: 0,
                                        y: (font.capHeight - image.size.height).rounded() / 2,
                                        width: image.size.width,
                                        height: image.size.height)
        imageAttachment.image = image
        
        return NSAttributedString(attachment: imageAttachment)
    }
}

// swiftlint:enable function_parameter_count
