//
//  NumberQuality.swift
//  CUIModule
//
//  Created by <PERSON><PERSON> on 7/8/67.
//

import UIKit
import SharedData

public extension NumberQuality {
    
    var arrowIcon: UIImage {
        switch self {
        case .up:
            return .image(named: "merit_ic_change_up")
        case .still:
            return .image(named: "merit_ic_change_flat")
        case .down:
            return .image(named: "merit_ic_change_down")
        }
    }
    
    var textColor: UIColor {
        switch self {
        case .up: 
            return Color.txtPositive
        case .still:
            return Color.txtParagraph
        case .down: 
            return Color.txtNegative
        }
    }
    
    var bgColor: UIColor {
        switch self {
        case .up: 
            return Color.bgPositive
        case .still: 
            return Color.bgAccent
        case .down: 
            return Color.bgNegative
        }
    }
}
