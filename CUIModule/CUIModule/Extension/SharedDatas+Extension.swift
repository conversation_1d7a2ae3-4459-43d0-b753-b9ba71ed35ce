//
//  SharedDatas+Extension.swift
//  CUIModule
//
//  Created by Chung Cr on 27/12/2023.
//

import UIKit
import SharedData
import Core

// MARK: - extension Currency
public extension Currency {
    /// Market icon
    var marketIcon: UIImage? {
        switch self {
        case .usd:
            return .image(named: "iconusmarket")
        case .thb:
            return .image(named: "iconthaimarket")
        case .hkd:
            return nil
        case .cny:
            return nil
        case .eur:
            return nil
        case .jpy:
            return nil
        }
    }
    
    var icon24: UIImage? {
        switch self {
        case .usd:
            return .image(named: "ic_dollar_24")
        case .thb:
            return .image(named: "ic_baht_24")
        case .hkd:
            return nil
        case .cny:
            return nil
        case .eur:
            return nil
        case .jpy:
            return nil
        }
    }   
    
    var largeIcon: UIImage? {
        switch self {
        case .usd:
            return .image(named: "icdollareven")
        case .thb:
            return .image(named: "icbah<PERSON><PERSON>")
        case .hkd:
            return nil
        case .cny:
            return nil
        case .eur:
            return nil
        case .jpy:
            return nil
        }
    }
    
    var smallIcon: UIImage? {
        switch self {
        case .usd:
            return .image(named: "icondollarcurrency")
        case .thb:
            return .image(named: "iconbahtcurrency")
        case .hkd:
            return nil
        case .cny:
            return nil
        case .eur:
            return nil
        case .jpy:
            return nil
        }
    }    
    
    var errorSmallIcon: UIImage? {
        switch self {
        case .usd:
            return .image(named: "ic_dollar_error")
        case .thb:
            return .image(named: "ic_baht_error")
        case .hkd:
            return nil
        case .cny:
            return nil
        case .eur:
            return nil
        case .jpy:
            return nil
        }
    }
    
    /// Get currency icon at index
    /// - Parameter index: Index
    /// - Returns: UIImage
    func icon(at index: Int) -> UIImage? {
        switch self {
        case .usd:
            return .image(named: index % 2 == 0 ? "icdollareven" : "icdollarodd")
        case .thb:
            return .image(named: index % 2 == 0 ? "icbahteven" : "icbahtodd")
        case .hkd:
            return nil
        case .cny:
            return nil
        case .eur:
            return nil
        case .jpy:
            return nil
        }
    }
}

// MARK: - OrderStatus
public extension OrderStatus {
    
    var color: UIColor {
        switch self {
        case .pending:
            return Color.txtDisabled
        case .matched,
                .approved:
            return Color.bgButtonPositive
        case .matching:
            return Color.bgButtonCaution
        case .canceled,
                .expired:
            return Color.txtNeutral
        case .rejected:
            return Color.txtNegative
            
        default:
            return .clear
        }
    }
}

// MARK: - OrderSide
public extension OrderSide {
    
    var color: UIColor {
        switch self {
        case .buy:
            return Color.txtPositive
        case .sell:
            return Color.txtNegative
        }
    }
    
    var badgeTitleColor: UIColor {
        switch self {
        case .buy:
            return Color.txtLabelPositive
        case .sell:
            return Color.txtLabelNegative
        }
    }
    
    var badgeColor: UIColor {
        switch self {
        case .buy:
            return Color.bgButtonPositivePale
        case .sell:
            return Color.bgButtonNegativePale
        }
    }
}

// MARK: - MarketExchange: OptionSelectItem
extension MarketExchange: OptionSelectItem {
    public var optionTitle: String { title.uppercased() }
    
    public var isDefault: Bool { false }
    
    public var optionImage: UIImage? {
        switch self {
        case .set:
            return .image(named: "iconthaimarket")
            
        case .nasdaq:
            return .image(named: "iconusmarket")
        }
    }
}

// MARK: - extension OrderStatus
extension OrderStatus: OptionSelectItem {
    
    public var id: Int {
        switch self {
        case .pending: return 1
        case .matched: return 2
        case .matching: return 3
        case .partialMatch: return 4
        case .canceled: return 5
        case .rejected: return 6
        case .expired: return 7
        case .approved: return 8
        case .queued: return 9
        case .none: return 0
        }
    }
    
    public var optionTitle: String { title.uppercased() }
    
    public var isDefault: Bool { false }
}

// MARK: - extension OrderSide
extension OrderSide: OptionSelectItem {
    
    public var optionTitle: String { title.uppercased() }
    
    public var isDefault: Bool { false }
}

// MARK: - PriceChangeDirection
public extension PriceChangeDirection {
    
    var textColor: UIColor {
        switch self {
        case .positive:
            return Color.txtPositive
        case .negative:
            return Color.txtNegative
        case .neutral:
            return Color.txtParagraph
        }
    }
        
    var badgeTextColor: UIColor {
        switch self {
        case .positive:
            return Color.txtLabelPositive
        case .negative:
            return Color.txtLabelNegative
        case .neutral:
            return Color.txtLabelNeutral
        }
    }
       
    var badgeBGColor: UIColor {
        switch self {
        case .positive:
            return Color.bgPositive
        case .negative:
            return Color.bgNegative
        case .neutral:
            return Color.bgNeutral
        }
    }
    
    var arrowIcon: UIImage {
        switch self {
        case .positive:
            return .image(named: "merit_ic_change_up")
        case .negative:
            return .image(named: "merit_ic_change_down")
        case .neutral:
            return .image(named: "merit_ic_change_flat")
        }
    }
    
    var signIcon: UIImage {
        switch self {
        case .positive:
            return .image(named: "ic_plus_8_positive")
        case .negative:
            return .image(named: "ic_minus_8")
        case .neutral:
            return .image(named: "ic_plus_8_neutral")
        }
    }
    
    var percentIcon: UIImage {
        switch self {
        case .positive:
            return .image(named: "ic_percent_10_positive")
        case .negative:
            return .image(named: "ic_percent_10_negative")
        case .neutral:
            return .image(named: "ic_percent_10_neutral")
        }
    }
    
    var percentSignIcon: UIImage {
        switch self {
        case .positive:
            return .image(named: "ic_plus_10_positive")
        case .negative:
            return .image(named: "ic_minus_10")
        case .neutral:
            return .image(named: "ic_plus_10_neutral")
        }
    }
}

// MARK: - Conform UIDisplayModel for MarketInstrument
extension MarketInstrument: UIDisplayModel {}

// MARK: - Conform UIDisplayModel for WatchListAsset
extension WatchListAsset: UIDisplayModel {}

// MARK: - AutoOrderTemplateCode
public extension AutoOrderTemplateCode {
    
    func previewChart(for stopPriceOperator: StopPriceOperator? = nil) -> UIImage? {
        switch self {
        case .buyStop:
            switch stopPriceOperator {
            case .moreThanOrEqual:
                return .image(named: "cao_bs_more")
            case .lessThanOrEqual:
                return .image(named: "cao_bs_less")
            case nil:
                return .image(named: "cao_bs_more") // return nil
            }
            
        case .sellStop:
            return .image(named: "cao_ss")
            
        case .buyTrailingStop:
            return .image(named: "cao_bts")
            
        case .sellTrailingStop:
            return .image(named: "cao_sts")
        }
    }
}

// MARK: - UserRiskLevel
public extension UserRiskLevel {
    
    var badgeColor: UIColor {
        switch self {
        case .pro:
            return Color.btn3rdPurple
        case .high:
            return Color.btn3rdRed
        case .medium:
            return Color.bgCaution
        case .low:
            return Color.btn3rdBlue
        case .expired:
            return Color.themeCustomThird1

        default:
            return .clear
        }
    }
        
    var titleColor: UIColor {
        switch self {
        case .pro:
            return Color.txtLabelPurple
        case .high:
            return Color.txtLabelRed
        case .medium:
            return Color.txtCaution
        case .low:
            return Color.txtLabelBlue
        case .expired:
            return Color.txtLabelNeutral
            
        default:
            return .clear
        }
    }
}

// MARK: - BenchmarkItem
extension BenchmarkItem: OptionSelectDropdownItem {
    
    public var title: String { code + " " + name}
    public var attributedTitle: NSAttributedString? {
        (code + "  " + name)
            .hightlight(normalFont: Font.light.of(size: 14),
                        normalColor: Color.txtParagraph,
                        highlightText: code,
                        highlightFont: Font.medium.of(size: 14),
                        highlightColor: Color.txtTitle)
    }
    
    public static var bottomSheetTitleIcon: UIImage? { .image(named: "merit_ic_benchmark") }
    
    public static var bottomSheetTitle: String { "" }
    
    public static var bottomSheetAttributedTitle: NSAttributedString? {
        let attributes: [NSAttributedString.Key: Any] = [
            .font: Font.regular.of(size: 14),
            .foregroundColor: Color.txtParagraph
        ]
        let attributeString = NSMutableAttributedString(string: "\("key0102".localized()) \("key0393".localized())",
                                                        attributes: attributes)
        
        let range: NSRange = attributeString.mutableString.range(of: "key0102".localized(),
                                                                 options: .caseInsensitive)
        attributeString.addAttributes([NSAttributedString.Key.font: Font.semiBold.of(size: 14),
                                       NSAttributedString.Key.foregroundColor: Color.txtTitle],
                                      range: range)
        
        return attributeString
    }
}

// MARK: - ProductCategoryFilterItem
extension ProductCategoryFilterItem: OptionSelectDropdownItem {
    public var id: Int { sort }
    public var title: String { name }
    
    public static var bottomSheetTitle: String { "" }
    
    public static var bottomSheetAttributedTitle: NSAttributedString? {
        let attributes: [NSAttributedString.Key: Any] = [
            .font: Font.regular.of(size: 14),
            .foregroundColor: Color.txtParagraph
        ]
        let attributeString = NSMutableAttributedString(string: "\("key0102".localized()) \("key0476".localized())",
                                                        attributes: attributes)
        
        let range: NSRange = attributeString.mutableString.range(of: "key0102".localized(),
                                                                 options: .caseInsensitive)
        attributeString.addAttributes([NSAttributedString.Key.font: Font.semiBold.of(size: 14),
                                       NSAttributedString.Key.foregroundColor: Color.txtTitle],
                                      range: range)
        
        return attributeString
    }
}

// MARK: - OrderHistorySide
extension OrderHistorySide: OptionSelectItem {
    
    public var id: Int {
        OrderHistorySide.allCases.firstIndex(where: { $0 == self }) ?? 0
    }
    
    public var optionTitle: String { title }
    
    public var isDefault: Bool { self == .all }
}

public extension OrderHistorySide {
    
    var color: UIColor? {
        switch self {
        case .buy:
            return Color.txtPositive
        case .sell:
            return Color.txtNegative
            
        default:
            return nil
        }
    }
}

// MARK: - OrderHistoryStatus
extension OrderHistoryStatus: OptionSelectItem {
    
    public var id: Int {
        OrderHistoryStatus.allCases.firstIndex(where: { $0 == self }) ?? 0
    }
    
    public var optionTitle: String { title }
    
    public var isDefault: Bool { self == .all }
}

public extension OrderHistoryStatus {
    
    var dotColor: UIColor? {
        switch self {
        case .approved:
            return Color.btnPositive
            
        case .pending:
            return Color.btnCaution
            
        default:
            return nil
        }
    }
    
    var dotIcon: UIImage? {
        switch self {
        case .approved:
            return .image(named: "dot_approved")
            
        case .pending:
            return .image(named: "dot_pending")
            
        default:
            return nil
        }
    }
}

// MARK: - TransactionHistoryStatus
extension TransactionHistoryStatus: OptionSelectItem {
    
    public var id: Int {
        TransactionHistoryStatus.allCases.firstIndex(where: { $0 == self }) ?? 0
    }
    
    public var optionTitle: String { title }
    
    public var isDefault: Bool { self == .all }
}

public extension TransactionHistoryStatus {
    
    var dotColor: UIColor? {
        switch self {
        case .pending:
            return Color.btnCaution
        case .approved:
            return Color.btnPositive
        case .rejected:
            return Color.btnNegative
            
        default:
            return nil
        }
    }
    
    var dotIcon: UIImage? {
        switch self {
        case .approved:
            return .image(named: "dot_approved")
            
        case .pending:
            return .image(named: "dot_pending")
            
        case .rejected:
            return .image(named: "dot_rejected")
            
        default:
            return nil
        }
    }
}

// MARK: - TransactionHistoryCurrency
extension TransactionHistoryCurrency: OptionSelectItem {
    
    public var id: Int {
        TransactionHistoryCurrency.allCases.firstIndex(where: { $0 == self }) ?? 0
    }
    
    public var optionTitle: String { title }
    
    public var isDefault: Bool { self == .all }
}

// MARK: - TransactionHistoryType
public extension TransactionHistoryType {
    
    var color: UIColor {
        switch self {
        case .deposit:
            return Color.txtPositive
        case .withdraw:
            return Color.txtNegative
        }
    }
}

// MARK: - AllocationClass
extension AllocationClass: OptionSelectItem {
    
    public var id: Int { sort }
    
    public var optionTitle: String { title }
    
    public var isDefault: Bool { 
        self.queryValue == InstrumentInvestmenentModels.allocationClasses.first?.queryValue
    }
}

// MARK: - WatchListSortingType
extension WatchListSortingType: OptionSelectItem {
    
    public var id: Int { WatchListSortingType.allCases.firstIndex(of: self) ?? 0 }
    
    public var optionTitle: String { title }
    
    public var isDefault: Bool { self == .gainer }
}

// MARK: - BankAccountStatus
public extension BankAccountStatus {
    
    var textColor: UIColor? {
        switch self {
        case .pending:
            return Color.txtCaution
            
        case .rejected:
            return Color.txtNegative
            
        default:
            return nil
        }
    }
    
    var badgeColor: UIColor? {
        switch self {
        case .pending:
            return Color.bgCaution
            
        case .rejected:
            return Color.bgNegative
            
        default:
            return nil
        }
    }
}
