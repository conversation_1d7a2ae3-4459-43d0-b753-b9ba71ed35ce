//
//  XCoordinator.swift
//  CUIModule
//
//  Created by <PERSON> on 24/11/2023.
//

import Foundation
import XCoordinator

// MARK: - Custom Animations
public extension Animation {
    
    static let fade = Animation(
        presentation: InteractiveTransitionAnimation.fade,
        dismissal: InteractiveTransitionAnimation.fade
    )
    
    static let fadeNoDuration = Animation(
        presentation: InteractiveTransitionAnimation.fadeNoDuration,
        dismissal: InteractiveTransitionAnimation.fadeNoDuration
    )
    
    static let navigation = Animation(
        presentation: InteractiveTransitionAnimation.push,
        dismissal: InteractiveTransitionAnimation.pop
    )
}

extension InteractiveTransitionAnimation {
    private static let duration: TimeInterval = 0.25
    
    fileprivate static let fade = InteractiveTransitionAnimation(duration: 0.35) { transitionContext in
        let containerView = transitionContext.containerView
        let toView = transitionContext.view(forKey: .to)!
        
        toView.alpha = 0.0
        containerView.addSubview(toView)
        
        UIView.animate(withDuration: 0.35, delay: 0, options: [.curveLinear], animations: {
            toView.alpha = 1.0
        }, completion: { _ in
            transitionContext.completeTransition(!transitionContext.transitionWasCancelled)
        })
    }
    
    fileprivate static let fadeNoDuration = InteractiveTransitionAnimation(duration: 0.01) { transitionContext in
        let containerView = transitionContext.containerView
        let toView = transitionContext.view(forKey: .to)!
        
        toView.alpha = 1.0
        containerView.addSubview(toView)
        
        UIView.animate(withDuration: 0.01, delay: 0, options: [.curveLinear], animations: {
            toView.alpha = 1.0
        }, completion: { _ in
            transitionContext.completeTransition(!transitionContext.transitionWasCancelled)
        })
    }
    
    fileprivate static let push = InteractiveTransitionAnimation(duration: duration) { context in
        let toView = context.view(forKey: .to)!
        let fromView = context.view(forKey: .from)!
        
        let middleFrame = fromView.frame
        
        var leftFrame = middleFrame
        leftFrame.origin.x -= middleFrame.width * 0.3
        
        var rightFrame = middleFrame
        rightFrame.origin.x += middleFrame.width
        
        context.containerView.addSubview(toView)
        context.containerView.bringSubviewToFront(toView)
        toView.frame = rightFrame
        
        UIView.animate(withDuration: duration, delay: 0, options: [.curveEaseOut], animations: {
            fromView.frame = leftFrame
            toView.frame = middleFrame
        }, completion: { _ in
            context.completeTransition(!context.transitionWasCancelled)
        })
    }
    
    fileprivate static let pop = InteractiveTransitionAnimation(duration: duration) { context in
        let toView = context.view(forKey: .to)!
        let fromView = context.view(forKey: .from)!
        
        let middleFrame = fromView.frame
        
        var leftFrame = middleFrame
        leftFrame.origin.x -= middleFrame.width * 0.3
        
        var rightFrame = middleFrame
        rightFrame.origin.x += middleFrame.width
        
        context.containerView.addSubview(toView)
        context.containerView.sendSubviewToBack(toView)
        toView.frame = leftFrame
        
        UIView.animate(withDuration: duration, delay: 0, options: [.curveEaseOut], animations: {
            fromView.frame = rightFrame
            toView.frame = middleFrame
        }, completion: { _ in
            context.completeTransition(!context.transitionWasCancelled)
        })
    }
}

// MARK: - Custom Transitions
public extension Transition {
    
    static func presentFullScreen(_ presentable: Presentable, animation: Animation? = nil) -> Transition {
        presentable.viewController?.modalPresentationStyle = .fullScreen
        return .present(presentable, animation: animation)
    }
    
    static func dismissAll() -> Transition {
        return Transition(presentables: [], animationInUse: nil) { rootViewController, options, completion in
            guard let presentedViewController = rootViewController.presentedViewController else {
                completion?()
                return
            }
            presentedViewController.dismiss(animated: options.animated) {
                Transition.dismissAll()
                    .perform(on: rootViewController, with: options, completion: completion)
            }
        }
    }
}
