//
//  UIViewController.swift
//  CUIModule
//
//  Created by <PERSON><PERSON><PERSON> on 10/27/66.
//
import SnapKit
import UIKit
import SharedData

// MARK: - Alert Conttoller
public extension UIViewController {
    
    var currentOrientation: UIInterfaceOrientation? {
        var orientation = UIInterfaceOrientation.unknown
        if #available(iOS 13.0, *) {
            guard let scene = UIApplication.shared.mainWindow?.windowScene else {
                return nil
            }
            orientation = scene.interfaceOrientation
        } else {
            orientation = UIApplication.shared.statusBarOrientation
        }
        return orientation
    }
    
    func showLoading() {
        CUILoadingView.showLoading()
    }
    
    func hiddenLoading() {
        CUILoadingView.hiddenLoading()
    }
    
    func showAlert(title: String? = "",
                   message: String?,
                   actionTitle: String = "key0832".localized(),
                   completion: (() -> Void)? = nil) {
        
        let alertController = UIAlertController(title: title, message: message, preferredStyle: .alert)
        
        alertController.addAction(UIAlertAction(title: actionTitle, style: .default, handler: { (_) in
            alertController.dismiss(animated: true, completion: completion)
        }))
        
        present(alertController, animated: true, completion: nil)
    }

    func addChild(controller: UIViewController, container: UIView) {
        addChild(controller)
        controller.view.frame = container.bounds
        container.addSubview(controller.view)
        controller.didMove(toParent: self)
    }
}
