//
//  AssetType.swift
//  CUIModule
//
//  Created by <PERSON><PERSON> on 7/8/67.
//

import SharedData
import UIKit

public extension AssetType {

    var color: UIColor {
        switch self {
        case .cashEquivalent:
            return Color.chartEmeraldGreen
        case .fixedIncome:
            return Color.chartPurple
        case .structuredProducts:
            return Color.chartOrange
        case .privateCapital:
            return Color.chartBlue
        case .virtualAssets:
            return Color.chartRed
        case .commodities:
            return Color.chartGreen
        case .equity:
            return Color.chartYellow
        case .multiAssets:
            return Color.chartPink
        }
    }
}
