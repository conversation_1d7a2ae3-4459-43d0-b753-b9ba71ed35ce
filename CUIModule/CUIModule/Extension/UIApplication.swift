//
//  UIApplication.swift
//  CUIModule
//
//  Created by <PERSON> on 15/11/2023.
//

import UIKit

public extension UIApplication {
    
    var topMostViewController: UIViewController? {
        guard
            let keyWindow = UIApplication.shared.windows.filter({ $0.isKeyWindow }).first,
            var topViewController = keyWindow.rootViewController
        else { return nil }
        
        while let presentedViewController = topViewController.presentedViewController {
            topViewController = presentedViewController
        }
        
        return topViewController
    }
}
