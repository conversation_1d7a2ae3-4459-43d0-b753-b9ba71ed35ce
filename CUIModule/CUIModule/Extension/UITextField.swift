//
//  UITextField.swift
//  CUIModule
//
//  Created by <PERSON> on 21/11/2023.
//

import UIKit

public extension UITextField {
    
    convenience init(backgroundColor: UIColor? = nil,
                     placeholder: String? = nil,
                     attributedPlaceholder: NSAttributedString? = nil,
                     textColor: UIColor? = nil,
                     text: String? = nil,
                     font: UIFont? = nil,
                     autocorrectionType: UITextAutocorrectionType = .default,
                     autocapitalizationType: UITextAutocapitalizationType = .sentences,
                     keyboardType: UIKeyboardType = .default,
                     adjustsFontSizeToFitWidth: Bool = false,
                     minimumFontSize: CGFloat? = nil,
                     textAlignment: NSTextAlignment = .left,
                     tintColor: UIColor? = nil,
                     borderStyle: UITextField.BorderStyle = .none,
                     isSecureTextEntry: Bool = false,
                     isEnabled: Bool = true,
                     clearButtonMode: UITextField.ViewMode = .never,
                     leftPaddingPoints: CGFloat? = nil,
                     rightPaddingPoints: CGFloat? = nil,
                     tag: Int = 0) {
        self.init()
        
        if let backgroundColor = backgroundColor {
            self.backgroundColor = backgroundColor
        }
        if let placeholder = placeholder {
            self.placeholder = placeholder
        }
        if let attributedPlaceholder = attributedPlaceholder {
            self.attributedPlaceholder = attributedPlaceholder
        }
        if let textColor = textColor {
            self.textColor = textColor
        }
        if let text = text {
            self.text = text
        }
        if let font = font {
            self.font = font
        }
        self.autocapitalizationType = autocapitalizationType
        self.autocorrectionType = autocorrectionType
        self.keyboardType = keyboardType
        self.adjustsFontSizeToFitWidth = adjustsFontSizeToFitWidth
        if let minimumFontSize = minimumFontSize {
            self.minimumFontSize = minimumFontSize
        }
        self.textAlignment = textAlignment
        if let tintColor = tintColor {
            self.tintColor = tintColor
        }
        self.borderStyle = borderStyle
        self.isSecureTextEntry = isSecureTextEntry
        self.isEnabled = isEnabled
        self.clearButtonMode = clearButtonMode
        if let leftPaddingPoints = leftPaddingPoints {
            let paddingView = UIView(frame: CGRect(x: 0,
                                                   y: 0,
                                                   width: leftPaddingPoints,
                                                   height: self.frame.size.height))
            self.leftView = paddingView
            self.leftViewMode = .always
        }
        if let rightPaddingPoints = rightPaddingPoints {
            let paddingView = UIView(frame: CGRect(x: 0,
                                                   y: 0,
                                                   width: rightPaddingPoints,
                                                   height: self.frame.size.height))
            self.rightView = paddingView
            self.rightViewMode = .always
        }
        
        self.tag = tag
    }
}
