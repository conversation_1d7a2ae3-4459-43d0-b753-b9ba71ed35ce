//
//  UITableView.swift
//  CUIModule
//
//  Created by <PERSON> on 21/11/2023.
//

import UIKit

public extension UITableView {
    
    convenience init(style: UITableView.Style = .plain,
                     backgroundColor: UIColor? = nil,
                     contentInsetAdjustmentBehavior: UIScrollView.ContentInsetAdjustmentBehavior = .automatic,
                     isScrollEnabled: Bool = true,
                     separatorStyle: UITableViewCell.SeparatorStyle = .none,
                     showsVerticalScrollIndicator: Bool = true,
                     rowHeight: CGFloat = UITableView.automaticDimension,
                     estimatedRowHeight: CGFloat? = nil,
                     tag: Int = 0) {
        self.init(frame: .zero, style: style)
        
        if let backgroundColor = backgroundColor {
            self.backgroundColor = backgroundColor
        }
        self.contentInsetAdjustmentBehavior = contentInsetAdjustmentBehavior
        self.isScrollEnabled = isScrollEnabled
        self.separatorStyle = separatorStyle
        self.showsVerticalScrollIndicator = showsVerticalScrollIndicator
        self.rowHeight = rowHeight
        if let estimatedRowHeight = estimatedRowHeight {
            self.estimatedRowHeight = estimatedRowHeight
        }
        self.tag = tag
    }
}
