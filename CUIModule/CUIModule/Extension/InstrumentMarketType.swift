//
//  InstrumentMarketType.swift
//  CUIModule
//
//  Created by <PERSON><PERSON><PERSON> on 3/20/67.
//

import SharedData
import UIKit

public extension InstrumentMarketType {

    var icon: UIImage {
        switch self {
        case .all: return .image(named: "iconmarketall")
        case .thai: return .image(named: "iconmarketthai")
        case .global:  return .image(named: "iconmarketglobal")
        case .mutualFunds: return .image(named: "iconmarketmutual")
        case .bonds: return .image(named: "iconmarketbond")
        case .digitalAssets: return .image(named: "iconmarketdigital")
        case .structureProducts: return .image(named: "iconmarketstructure")
        case .derivatives: return .image(named: "iconmarketderivative")
        }
    }

    var walletIcon: UIImage {
        switch self {
        case .thai: return .image(named: "iconlotussmall")
        case .global: return .image(named: "iconglobesmall")
        default: return UIImage()
        }
    }
}
