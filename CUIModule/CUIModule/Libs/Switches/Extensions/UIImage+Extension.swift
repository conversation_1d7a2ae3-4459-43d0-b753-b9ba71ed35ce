//
//  UIImage+Extension.swift
//  testi
//
//  Created by <PERSON><PERSON><PERSON> on 29/08/2020.
//  Copyright © 2020 J<PERSON><PERSON>. All rights reserved.
//

import UIKit
public extension UIImage {
  func maskWithColor(color: UIColor) -> UIImage? {
    let maskLayer = CALayer()
    maskLayer.bounds = CGRect(x: 0, y: 0, width: size.width, height: size.height)
    maskLayer.backgroundColor = color.cgColor
    maskLayer.doMask(by: self)
    let maskImage = maskLayer.toImage()
    return maskImage
  }

    func compressToData(_ expectedSizeInMb: Double, stepSize: Double = 0.01) -> Data? {
        let sizeInBytes = Int(expectedSizeInMb * 1024 * 1024)
        var needCompress:Bool = true
        var imgData:Data?
        var compressingValue:CGFloat = 1.0
        while (needCompress && compressingValue > 0.0) {
            if let data:Data = self.jpegData(compressionQuality: compressingValue) {
                if data.count <= sizeInByte<PERSON> {
                    needCompress = false
                    imgData = data
                } else {
                    compressingValue -= stepSize
                }
            }
        }

        if let data = imgData {
            if (data.count <= sizeInBytes) {
                return data
            }
        }
        return self.jpegData(compressionQuality: 0.05)
    }
}
