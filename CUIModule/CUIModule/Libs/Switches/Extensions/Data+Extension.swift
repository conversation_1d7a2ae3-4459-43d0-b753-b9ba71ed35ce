//
//  Data+Extension.swift
//  CUIModule
//
//  Created by <PERSON><PERSON> on 6/27/67.
//

import Foundation

public extension Data {

    func compress(to expectedSizeInMb: Double, stepSize: Double = 0.01) -> Data {
        let sizeInBytes = Int(expectedSizeInMb * 1024 * 1024)
        var needCompress:Bool = true
        var compressedData:Data?
        var compressingValue:CGFloat = 1.0
        while (needCompress && compressingValue > 0.0) {
            if self.count <= sizeInBytes {
                needCompress = false
                compressedData = self
            } else {
                compressingValue -= stepSize
            }
        }

        if let data = compressedData {
            if (data.count <= sizeInBytes) {
                return data
            }
        }
        return self
    }
}
