//
//  ESTabBarController.swift
//
//  Created by <PERSON> on 2017/2/8.
//  Copyright (c) 2013-2020 ESTabBarController (https://github.com/eggswift/ESTabBarController)
//
//  Permission is hereby granted, free of charge, to any person obtaining a copy
//  of this software and associated documentation files (the "Software"), to deal
//  in the Software without restriction, including without limitation the rights
//  to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
//  copies of the Software, and to permit persons to whom the Software is
//  furnished to do so, subject to the following conditions:
//
//  The above copyright notice and this permission notice shall be included in
//  all copies or substantial portions of the Software.
//
//  THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
//  IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
//  FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
//  AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
//  LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
//  OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
//  THE SOFTWARE.
//

import UIKit

/// ESTabBarItem inherits from UITabBarItem, the purpose is to provide UITabBarItem property settings for ESTabBarItemContentView.
/// Support most commonly used attributes, such as image, selectedImage, title, tag etc.
///
/// Unsupport properties:
/// MARK: UIBarItem properties
///     1. var landscapeImagePhone: UIImage?
///     2. var imageInsets: UIEdgeInsets
///     3. var landscapeImagePhoneInsets: UIEdgeInsets
///     4. func setTitleTextAttributes(_ attributes: [String : Any]?, for state: UIControlState)
///     5. func titleTextAttributes(for state: UIControlState) -> [String : Any]?
/// MARK: UITabBarItem properties
///     1. func setBadgeTextAttributes(_ textAttributes: [String : Any]?, for state: UIControlState)
///     2. func badgeTextAttributes(for state: UIControlState) -> [String : Any]?
///
@available(iOS 8.0, *)
open class ESTabBarItem: UITabBarItem {
    
    // MARK: UIView properties
    
    /// The receiver’s tag, an application-supplied integer that you can use to identify bar item objects in your application. default is `0`
    open override var tag: Int
    {
        didSet { self.contentView.tag = tag }
    }
    
    // MARK: UIBarItem properties
    
    /// A Boolean value indicating whether the item is enabled, default is `YES`.
    open override var isEnabled: Bool
    {
        didSet { self.contentView.enabled = isEnabled }
    }
    
    /// The title displayed on the item, default is `nil`
    open override var title: String?
    {
        didSet { self.contentView.title = title }
    }
    
    /// The image used to represent the item, default is `nil`
    open override var image: UIImage?
    {
        didSet { self.contentView.image = image }
    }
    
    // MARK: UITabBarItem properties
    
    /// The image displayed when the tab bar item is selected, default is `nil`.
    open override var selectedImage: UIImage?
    {
        get { return contentView.selectedImage }
        set(newValue) { contentView.selectedImage = newValue }
    }
    
    /// Text that is displayed in the upper-right corner of the item with a surrounding red oval, default is `nil`.
    open override var badgeValue: String?
    {
        get { return contentView.badgeValue }
        set(newValue) { contentView.badgeValue = newValue }
    }
    
    /// The offset to use to adjust the title position, default is `UIOffset.zero`.
    open override var titlePositionAdjustment: UIOffset
    {
        get { return contentView.titlePositionAdjustment }
        set(newValue) { contentView.titlePositionAdjustment = newValue }
    }
    
    /// The background color to apply to the badge, make it available for iOS8.0 and later. If this item displays a badge, this color will be used for the badge's background. If set to nil, the default background color will be used instead.
    @available(iOS 8.0, *)
    open override var badgeColor: UIColor? {
        get { return contentView.badgeColor }
        set(newValue) { contentView.badgeColor = newValue }
    }
    
    // MARK: ESTabBarItem properties
    
    /// Customize content view, default is `ESTabBarItemContentView`
    open var contentView: ESTabBarItemContentView = ESTabBarItemContentView()
    {
        didSet {
            self.contentView.updateLayout()
            self.contentView.updateDisplay()
        }
    }
    
    /// The unselected image is autogenerated from the image argument. The selected image is autogenerated from the selectedImage if provided and the image argument otherwise. To prevent system coloring, provide images with UIImageRenderingModeAlwaysOriginal (see UIImage.h)
    public init(_ contentView: ESTabBarItemContentView = ESTabBarItemContentView(), title: String? = nil, image: UIImage? = nil, selectedImage: UIImage? = nil, tag: Int = 0) {
        super.init()
        self.contentView = contentView
        self.contentView.title = title
        self.contentView.image = image
        self.contentView.selectedImage = selectedImage
        self.contentView.tag = tag
        self.tag = tag
    }
    
    public required init?(coder aDecoder: NSCoder) {
        super.init(coder: aDecoder)
    }
    
}
