//
//  BaseViewController.swift
//  CUIModule
//
//  Created by <PERSON><PERSON> on 10/27/66.
//
import Core
import RxSwift
import SnapKit
import UIKit
import Utils
import SharedData

open class BaseViewController: UIViewController, LocalizeNeededView {
    
    // MARK: Properties
    public lazy var loadingBar = CUILoadingBar()
    
    private var loadingBarAdded = false
    public let disposeBag = DisposeBag()
    
    // MARK: Override
    override open func viewDidLoad() {
        super.viewDidLoad()
        
        view.backgroundColor = Color.bgDefault
        NotificationCenter.default.addObserver(self,
                                               selector: #selector(setTexts),
                                               name: NSNotification.Name(LocalizeManager.LCLLanguageChangeNotification),
                                               object: nil)
    }
    
    open override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        
        navigationBarHidden(true)
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
    }
    
    open override var shouldAutorotate: Bool {
        return false
    }
    
    open override var supportedInterfaceOrientations: UIInterfaceOrientationMask {
        return .portrait
    }
    
    // MARK: Functions
    open func navigationBarHidden(_ hidden: Bool) {
        navigationController?.setNavigationBarHidden(hidden, 
                                                     animated: false)
    }
    
    public func showBackButton() {
        navigationBarHidden(false)
        let backItem = UIBarButtonItem(image: UIImage.image(named: "merit_ic_back"),
                                       style: UIBarButtonItem.Style.plain,
                                       target: self,
                                       action: #selector(backPress))
        backItem.tintColor = .white
        navigationItem.leftBarButtonItem = backItem
    }
    
    open func hiddenBackButton() {
        navigationController?.navigationItem.setHidesBackButton(true,
                                                                animated: false)
        navigationItem.setHidesBackButton(true, 
                                          animated: false)
        navigationItem.leftBarButtonItem = nil
    }
    
    @objc open func backPress() {
        navigationController?.popViewController(animated: true)
    }
    
    @objc open func homePress() {
        navigationController?.popToRootViewController(animated: true)
    }
    
    public func setNavigationBarTransparent(isTransparent: Bool) {
        if isTransparent {
            navigationController?.navigationBar.setBackgroundImage(UIImage(),
                                                                   for: .default)
            navigationController?.navigationBar.shadowImage = UIImage()
        } else {
            navigationController?.navigationBar.setBackgroundImage(nil, 
                                                                   for: .default)
            navigationController?.navigationBar.shadowImage = nil
        }
        navigationController?.navigationBar.isTranslucent = isTransparent
    }
    
    open func addSubviews(_ views: [UIView]) {
        for tempView in views {
            view.addSubview(tempView)
        }
    }

    public func showAlert(title: String? = "",
                          message: String?,
                          positiveTitle: String = "key0832".localized(),
                          positiveAction: (() -> Void)? = nil,
                          negativeTitle: String? = nil,
                          negativeAction: (() -> Void)? = nil) {
        
        let alertController = UIAlertController(title: title, 
                                                message: message,
                                                preferredStyle: .alert)
        
        if let negativeTitle = negativeTitle {
            alertController.addAction(UIAlertAction(title: negativeTitle, style: .default, handler: { (_) in
                alertController.dismiss(animated: true, completion: nil)
                negativeAction?()
            }))
        }
        
        alertController.addAction(UIAlertAction(title: positiveTitle, style: .default, handler: { (_) in
            alertController.dismiss(animated: true, completion: nil)
            positiveAction?()
        }))
        
        present(alertController, animated: true, completion: nil)
    }
    
    public func showAlertWithList(title: String? = "",
                                  message: String?,
                                  list: [String] = [],
                                  action: ((_ index: Int, _ title: String) -> Void)? = nil) {
        
        let alertController = UIAlertController(title: title,
                                                message: message,
                                                preferredStyle: .alert)
        
        list.enumerated().forEach { index, title in
            alertController.addAction(UIAlertAction(title: title, style: .default, handler: { (_) in
                alertController.dismiss(animated: true, completion: nil)
                action?(index, title)
            }))
        }

        alertController.addAction(UIAlertAction(title: "key0273".localized(), style: .default, handler: { (_) in
            alertController.dismiss(animated: true, completion: nil)
        }))

        present(alertController, animated: true, completion: nil)
    }

    public func showLoadingBar(_ show: Bool, under navView: UIView) {
        addLoadingBar(navView: navView)
        loadingBar.show(show)
    }
    
   @objc open func setTexts() {}
}

// MARK: - Private
private extension BaseViewController {
    
    func addLoadingBar(navView: UIView) {
        guard !loadingBarAdded else { return }
        
        loadingBarAdded = true

        if let superView = navView.superview {
            superView.addSubview(loadingBar)
        } else {
            view.addSubview(loadingBar)
        }
        loadingBar.snp.makeConstraints { make in
            make.top.equalTo(navView.snp.bottom)
            make.left.right.equalTo(navView)
        }
    }
}
