//
//  SegmentedViewContainer.swift
//  CUIModule
//
//  Created by <PERSON> on 12/11/24.
//

import UIKit

// MARK: - SegmentedViewContainer
public protocol SegmentedViewContainer: AnyObject {
    var segmentedSettingDatas: [SegmentedDataSource] { get }
    var updatedTitles: [Int: String]? {get set}
    var segmentedView: JXSegmentedView! { get set }
    var segmentedDataSource: JXSegmentedTitleImageDataSource! { get set }
    var segmentedContentView: JXSegmentedListContainerView! { get set }
    
    /// Setup segmented view
    func setupSegmentedView(customIconSize: CGSize?,
                            indicatorWidth: CGFloat?,
                            itemWidth: CGFloat?,
                            itemSpacing: CGFloat,
                            defaultSelectedIndex: Int)
    /// Setup segmeted data source
    func setupSegmentedDataSource(itemWidth: CGFloat?, itemSpacing: CGFloat)
    /// Update dynamic segmented titles
    /// - Parameter updatingTitles: Titles to update for indexs
    func updateDynamicSegmentedTitles(_ updatingTitles: [Int: String])
    /// Update Segmented data source
    func updateSegmentedDataSource()
}

// MARK: - SegmentedViewContainer implement default functions
public extension SegmentedViewContainer {
    
    func updateDynamicSegmentedTitles(_ updatingTitles: [Int: String]) {
        guard
            segmentedView != nil, !updatingTitles.isEmpty
        else { return }
        
        updatedTitles = updatingTitles
        let newTitles = segmentedSettingDatas.map({ updatingTitles[$0.titleKey] ?? $0.title })
        segmentedDataSource.titles = newTitles
        segmentedView.reloadDataWithoutListContainer()
    }
}

// MARK: - Implement JXSegmentedListContainerViewDataSource
public extension SegmentedViewContainer where Self: JXSegmentedListContainerViewDataSource {
    
    func setupSegmentedView(customIconSize: CGSize? = nil,
                            indicatorWidth: CGFloat? = nil,
                            itemWidth: CGFloat? = nil,
                            itemSpacing: CGFloat = 0,
                            defaultSelectedIndex: Int = 0) {
        segmentedView = JXSegmentedView()
        segmentedView.defaultSelectedIndex = defaultSelectedIndex
        // Setup Data source before configuring
        setupSegmentedDataSource(itemWidth: itemWidth, itemSpacing: itemSpacing)
        if let customIconSize = customIconSize {
            segmentedDataSource.imageSize = customIconSize
        }
        segmentedView.dataSource = segmentedDataSource
        // Config List container view
        segmentedContentView = JXSegmentedListContainerView(dataSource: self)
        segmentedView.listContainer = segmentedContentView
        
        // Indicator
        let indicator = JXSegmentedIndicatorBackgroundView()
        indicator.indicatorHeight = 44
        indicator.indicatorColor = Color.btn4th
        indicator.indicatorCornerRadius = 2
        
        segmentedView.indicators = [indicator]
        
        // Currently segmented view can't swipe to change tab
        segmentedView.contentScrollView?.isScrollEnabled = false
        // Set SegmentedView delegate
        if let segmentedDelegate = self as? JXSegmentedViewDelegate {
            segmentedView.delegate = segmentedDelegate
        }
    }
    
    // Setup segmeted data source
    func setupSegmentedDataSource(itemWidth: CGFloat?, itemSpacing: CGFloat) {
        segmentedDataSource = JXSegmentedTitleImageDataSource()
        guard !segmentedSettingDatas.isEmpty else { return }
        
        segmentedDataSource.titles = segmentedSettingDatas.map({ $0.title })
        segmentedDataSource.titleImageType = .leftImage
        segmentedDataSource.itemSpacing = itemSpacing
        
        if let itemWidth = itemWidth {
            segmentedDataSource.itemWidth = itemWidth
        }
        segmentedDataSource.titleImageSpacing = 0
        segmentedDataSource.titleNormalColor = Color.txtInactive
        segmentedDataSource.titleSelectedColor = Color.txtTitle
        segmentedDataSource.titleNormalFont = Font.light.of(size: 14)
        segmentedDataSource.titleSelectedFont = Font.medium.of(size: 14)
        segmentedDataSource.normalImageInfos = segmentedSettingDatas.map({ $0.iconName })
        segmentedDataSource.selectedImageInfos = segmentedSettingDatas.map({ $0.selectedIconName })
        segmentedDataSource.loadImageClosure = { (imageView, imageInfo) in
            imageView.image = UIImage.image(named: imageInfo)
        }
    }
    
    func updateSegmentedDataSource() {
        guard
            segmentedView != nil,
            segmentedDataSource != nil,
            !segmentedSettingDatas.isEmpty
        else { return }
        
        if let updatingTitles = updatedTitles {
            let newTitles = segmentedSettingDatas.map({ updatingTitles[$0.titleKey] ?? $0.title })
            segmentedDataSource.titles = newTitles
        } else {
            segmentedDataSource.titles = segmentedSettingDatas.map({ $0.title })
        }
        
        segmentedDataSource.titleNormalColor = Color.txtInactive
        segmentedDataSource.titleSelectedColor = Color.txtTitle
        segmentedDataSource.titleNormalFont = Font.light.of(size: 14)
        segmentedDataSource.titleSelectedFont = Font.medium.of(size: 14)
        segmentedDataSource.normalImageInfos = segmentedSettingDatas.map({ $0.iconName })
        segmentedDataSource.selectedImageInfos = segmentedSettingDatas.map({ $0.selectedIconName })
        segmentedView.reloadData()
    }
}

// MARK: - SegmentedDataSource
public protocol SegmentedDataSource {
    /// Index used for updating dynamic title only
    var titleKey: Int { get }
    var title: String { get }
    var tabTitle: String { get }
    var iconName: String? { get }
    var selectedIconName: String? { get }
    var selectedRightIconName: String? { get }
    var canBeSelected: Bool { get }
    var needReload: Bool { get }
}

// MARK: - DefaultSegmentedContentView
public class DefaultSegmentedContentView: UIView, JXSegmentedListContainerViewListDelegate {
    
    public func listView() -> UIView { self }
}
