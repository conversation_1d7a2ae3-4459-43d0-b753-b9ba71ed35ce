//
//  BaseBottomSheet.swift
//  CUIModule
//
//  Created by Chung C<PERSON> on 15/11/2023.
//

import UIKit
import FittedSheets
import RxSwift
import XCoordinator

// MARK: - BaseBottomSheet
public protocol BaseBottomSheet: AnyObject {
    var disposeBagForDismiss: DisposeBag { get set }
    var onDismiss: PublishSubject<(() -> Void)?> { get }
    var onUpdateHeight: PublishSubject<Void> { get }
    
    func customSheetOptions() -> SheetOptions?
    func customSheetConfig() -> SheetConfig
    
    func present(sizes: [SheetSize], completion: (() -> Void)?)
}

// MARK: - Default implementations for BaseBottomSheet
public extension BaseBottomSheet where Self: UIViewController {
    
    var onUpdateHeight: PublishSubject<Void> {
        PublishSubject<Void>()
    }
    
    func customSheetOptions() -> SheetOptions? {
        return nil
    }
    
    func customSheetConfig() -> SheetConfig {
        return SheetConfig.default
    }
    
    func present(sizes: [SheetSize] = [.intrinsic], completion: (() -> Void)? = nil) {
        guard let topMostVC = UIApplication.shared.topMostViewController else { return }
        
        // Reset disposeBag
        self.disposeBagForDismiss = DisposeBag()
        
        let sheet = SheetViewController(controller: self, sizes: sizes, options: self.sheetOptions())
        let sheetConfig = self.customSheetConfig()
        
        if let gripSize = sheetConfig.gripSize {
            sheet.gripSize = gripSize
        }
         
        if let gripColor = sheetConfig.gripColor {
            sheet.gripColor = gripColor
        }
         
        if let cornerRadius = sheetConfig.cornerRadius {
            sheet.cornerRadius = cornerRadius
        }
         
        if let minimumSpaceAbovePullBar = sheetConfig.minimumSpaceAbovePullBar {
            sheet.minimumSpaceAbovePullBar = minimumSpaceAbovePullBar
        }
         
        if let pullBarBackgroundColor = sheetConfig.pullBarBackgroundColor {
            sheet.pullBarBackgroundColor = pullBarBackgroundColor
        }
         
        if let treatPullBarAsClear = sheetConfig.treatPullBarAsClear {
            sheet.treatPullBarAsClear = treatPullBarAsClear
        }
         
        if let dismissOnOverlayTap = sheetConfig.dismissOnOverlayTap {
            sheet.dismissOnOverlayTap = dismissOnOverlayTap
        }
         
        if let dismissOnPull = sheetConfig.dismissOnPull {
            sheet.dismissOnPull = dismissOnPull
        }
         
        if let allowPullingPastMaxHeight = sheetConfig.allowPullingPastMaxHeight {
            sheet.allowPullingPastMaxHeight = allowPullingPastMaxHeight
        }
          
        if let autoAdjustToKeyboard = sheetConfig.autoAdjustToKeyboard {
            sheet.autoAdjustToKeyboard = autoAdjustToKeyboard
        }
          
        if let overlayColor = sheetConfig.overlayColor {
            sheet.overlayColor = overlayColor
        }
        
        onDismiss
            .subscribe(onNext: { completion in
                sheet.dismiss(animated: true, completion: completion)
            })
            .disposed(by: disposeBagForDismiss)
        
        onUpdateHeight
            .subscribe(onNext: {
                sheet.updateIntrinsicHeight()
            })
            .disposed(by: disposeBagForDismiss)
        
        topMostVC.present(sheet, animated: true, completion: completion)
    }
}

// MARK: - Setup default configurations
private extension BaseBottomSheet {
    
    var defaultSheetOptions: SheetOptions {
        SheetOptions(pullBarHeight: 0,
                     shrinkPresentingViewController: false)
    }
    
    func sheetOptions() -> SheetOptions {
        return self.customSheetOptions() ?? defaultSheetOptions
    }
}

// MARK: - SheetConfig
public struct SheetConfig {
    public static var `default` = SheetConfig(gripSize: .zero,
                                              cornerRadius: 8.0,
                                              allowPullingPastMaxHeight: false)
    
    /// The size of the grip in the pull bar
    var gripSize: CGSize?
    
    /// The color of the grip on the pull bar
    var gripColor: UIColor?
    
    /// The corner radius of the sheet
    var cornerRadius: CGFloat?
    
    /// minimum distance above the pull bar, prevents bar from coming right up to the edge of the screen
    var minimumSpaceAbovePullBar: CGFloat?
    
    /// Set the pullbar's background explicitly
    var pullBarBackgroundColor: UIColor?
    
    /// Determine if the rounding should happen on the pullbar or the presented controller only
    /// (should only be true when the pull bar's background color is .clear)
    var treatPullBarAsClear: Bool?
    
    /// Disable the dismiss on background tap functionality
    var dismissOnOverlayTap: Bool?
    
    /// Disable the ability to pull down to dismiss the modal
    var dismissOnPull: Bool?
    
    /// Allow pulling past the maximum height and bounce back. Defaults to true.
    var allowPullingPastMaxHeight: Bool?
    
    /// Automatically grow/move the sheet to accomidate the keyboard. Defaults to true.
    var autoAdjustToKeyboard: Bool?
    
    /// Change the overlay color
    var overlayColor: UIColor?
    
    public init(gripSize: CGSize? = nil,
                gripColor: UIColor? = nil,
                cornerRadius: CGFloat? = nil,
                minimumSpaceAbovePullBar: CGFloat? = nil,
                pullBarBackgroundColor: UIColor? = nil,
                treatPullBarAsClear: Bool? = nil,
                dismissOnOverlayTap: Bool? = nil,
                dismissOnPull: Bool? = nil,
                allowPullingPastMaxHeight: Bool? = nil,
                autoAdjustToKeyboard: Bool? = nil,
                overlayColor: UIColor? = nil) {
        self.gripSize = gripSize
        self.gripColor = gripColor
        self.cornerRadius = cornerRadius
        self.minimumSpaceAbovePullBar = minimumSpaceAbovePullBar
        self.pullBarBackgroundColor = pullBarBackgroundColor
        self.treatPullBarAsClear = treatPullBarAsClear
        self.dismissOnOverlayTap = dismissOnOverlayTap
        self.dismissOnPull = dismissOnPull
        self.allowPullingPastMaxHeight = allowPullingPastMaxHeight
        self.autoAdjustToKeyboard = autoAdjustToKeyboard
        self.overlayColor = overlayColor
    }
}
