//
//  TranslateTransition.swift
//  CUIModule
//
//  Created by <PERSON><PERSON><PERSON> on 12/5/66.
//
import RxSwift
import XCoordinator

// MARK: - Custom Animations
public extension Animation {

    static let naviagationReverse = Animation(
        presentation: InteractiveTransitionAnimation.pushFromLeft,
        dismissal: InteractiveTransitionAnimation.popToRight
    )
}

extension InteractiveTransitionAnimation {

    private static let duration: TimeInterval = 0.25

    fileprivate static let pushFromLeft = InteractiveTransitionAnimation(duration: duration) { context in
        guard let toView = context.view(forKey: .to) else {
            return
        }

        let screenWidth = UIScreen.main.bounds.width

        context.containerView.addSubview(toView)
        context.containerView.bringSubviewToFront(toView)
        toView.transform = CGAffineTransform(translationX: -screenWidth, y: 0)

        UIView.animate(withDuration: duration, delay: 0, options: [.curveEaseOut], animations: {
            toView.transform = .identity
        }, completion: { _ in
            context.completeTransition(!context.transitionWasCancelled)
        })
    }

    fileprivate static let popToRight = InteractiveTransitionAnimation(duration: duration) { context in
        guard let toView = context.view(forKey: .to),
              let fromView = context.view(forKey: .from) else {
            return
        }

        let screenWidth = UIScreen.main.bounds.width

        context.containerView.addSubview(toView)
        context.containerView.sendSubviewToBack(toView)

        UIView.animate(withDuration: duration, delay: 0, options: [.curveEaseOut], animations: {
            fromView.transform = CGAffineTransform(translationX: -screenWidth, y: 0)
        }, completion: { _ in
            context.completeTransition(!context.transitionWasCancelled)
        })
    }

}
