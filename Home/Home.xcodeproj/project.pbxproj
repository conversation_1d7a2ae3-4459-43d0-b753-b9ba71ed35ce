// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 56;
	objects = {

/* Begin PBXBuildFile section */
		0C4DB7262E58D37E007439DA /* MeritReport.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0C4DB7252E58D37E007439DA /* MeritReport.swift */; };
		0C4DB7282E58D395007439DA /* MeritReportCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0C4DB7272E58D395007439DA /* MeritReportCell.swift */; };
		0C4DB72C2E5B08DE007439DA /* FundRecommendationCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0C4DB72B2E5B08DE007439DA /* FundRecommendationCell.swift */; };
		835A4A402D2A729E00C212EE /* WatchListSortingViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 835A4A3F2D2A729E00C212EE /* WatchListSortingViewController.swift */; };
		83D977BD2CF4B8F600A6BC4D /* IndexIndicatorFooterView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 83D977BC2CF4B8F600A6BC4D /* IndexIndicatorFooterView.swift */; };
		AA1234562E5B08DE007439DA /* FundRecommendationListViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = AA1234552E5B08DE007439DA /* FundRecommendationListViewController.swift */; };
		AA1234582E5B08DE007439DA /* FundRecommendationListViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = AA1234572E5B08DE007439DA /* FundRecommendationListViewModel.swift */; };
		DF0ADCC32BD769C30098FDAF /* HomeCoordinator.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0ADCA72BD769C30098FDAF /* HomeCoordinator.swift */; };
		DF0ADCC42BD769C30098FDAF /* NewsListViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0ADCA92BD769C30098FDAF /* NewsListViewModel.swift */; };
		DF0ADCC52BD769C30098FDAF /* BannerCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0ADCAB2BD769C30098FDAF /* BannerCell.swift */; };
		DF0ADCC62BD769C30098FDAF /* NewsCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0ADCAC2BD769C30098FDAF /* NewsCell.swift */; };
		DF0ADCC72BD769C30098FDAF /* ShortcutCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0ADCAD2BD769C30098FDAF /* ShortcutCell.swift */; };
		DF0ADCC82BD769C30098FDAF /* NewsLargeCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0ADCAE2BD769C30098FDAF /* NewsLargeCell.swift */; };
		DF0ADCC92BD769C30098FDAF /* InstrumentCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0ADCAF2BD769C30098FDAF /* InstrumentCell.swift */; };
		DF0ADCCA2BD769C30098FDAF /* MessageCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0ADCB02BD769C30098FDAF /* MessageCell.swift */; };
		DF0ADCCB2BD769C30098FDAF /* WalletSummaryCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0ADCB12BD769C30098FDAF /* WalletSummaryCell.swift */; };
		DF0ADCCC2BD769C30098FDAF /* WatchlistTypeCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0ADCB22BD769C30098FDAF /* WatchlistTypeCell.swift */; };
		DF0ADCCD2BD769C30098FDAF /* NewsDetailViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0ADCB32BD769C30098FDAF /* NewsDetailViewController.swift */; };
		DF0ADCCE2BD769C30098FDAF /* HomeLayoutConfig.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0ADCB42BD769C30098FDAF /* HomeLayoutConfig.swift */; };
		DF0ADCCF2BD769C30098FDAF /* HomeViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0ADCB52BD769C30098FDAF /* HomeViewModel.swift */; };
		DF0ADCD02BD769C30098FDAF /* HomeSectionData.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0ADCB62BD769C30098FDAF /* HomeSectionData.swift */; };
		DF0ADCD12BD769C30098FDAF /* HomeViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0ADCB72BD769C30098FDAF /* HomeViewController.swift */; };
		DF0ADCD22BD769C30098FDAF /* UserProfileHeaderView.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0ADCB92BD769C30098FDAF /* UserProfileHeaderView.swift */; };
		DF0ADCD32BD769C30098FDAF /* WatchlistHeaderView.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0ADCBA2BD769C30098FDAF /* WatchlistHeaderView.swift */; };
		DF0ADCD42BD769C30098FDAF /* TitleHeaderView.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0ADCBB2BD769C30098FDAF /* TitleHeaderView.swift */; };
		DF0ADCD52BD769C30098FDAF /* InstrumentItemView.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0ADCBC2BD769C30098FDAF /* InstrumentItemView.swift */; };
		DF0ADCD62BD769C30098FDAF /* WatchlistTypeView.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0ADCBD2BD769C30098FDAF /* WatchlistTypeView.swift */; };
		DF0ADCD72BD769C30098FDAF /* RoundedBackgroundView.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0ADCBE2BD769C30098FDAF /* RoundedBackgroundView.swift */; };
		DF0ADCD82BD769C30098FDAF /* SeeMoreFooterView.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0ADCBF2BD769C30098FDAF /* SeeMoreFooterView.swift */; };
		DF0ADCD92BD769C30098FDAF /* NewsListViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0ADCC02BD769C30098FDAF /* NewsListViewController.swift */; };
		DF0AE0672BD76D6E0098FDAF /* APILayer.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = DF0AE0652BD76D6E0098FDAF /* APILayer.framework */; };
		DF0AE0692BD76D6E0098FDAF /* CUIModule.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = DF0AE0662BD76D6E0098FDAF /* CUIModule.framework */; };
		DF273D6D2BD7680A003F9464 /* Home.h in Headers */ = {isa = PBXBuildFile; fileRef = DF273D6C2BD7680A003F9464 /* Home.h */; settings = {ATTRIBUTES = (Public, ); }; };
		E65BCAB94F9FAF8B2BF3F676 /* Pods_Home.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = F37A9EE49C69C2566FE448AD /* Pods_Home.framework */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		0C4DB7252E58D37E007439DA /* MeritReport.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MeritReport.swift; sourceTree = "<group>"; };
		0C4DB7272E58D395007439DA /* MeritReportCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MeritReportCell.swift; sourceTree = "<group>"; };
		0C4DB72B2E5B08DE007439DA /* FundRecommendationCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FundRecommendationCell.swift; sourceTree = "<group>"; };
		70CBAE0A2CB2B63477A30A97 /* Pods-Home.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Home.release.xcconfig"; path = "Target Support Files/Pods-Home/Pods-Home.release.xcconfig"; sourceTree = "<group>"; };
		82AC42C14C6E74A32CDFEDE9 /* Pods-Home.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Home.debug.xcconfig"; path = "Target Support Files/Pods-Home/Pods-Home.debug.xcconfig"; sourceTree = "<group>"; };
		835A4A3F2D2A729E00C212EE /* WatchListSortingViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WatchListSortingViewController.swift; sourceTree = "<group>"; };
		83D977BC2CF4B8F600A6BC4D /* IndexIndicatorFooterView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = IndexIndicatorFooterView.swift; sourceTree = "<group>"; };
		AA1234552E5B08DE007439DA /* FundRecommendationListViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FundRecommendationListViewController.swift; sourceTree = "<group>"; };
		AA1234572E5B08DE007439DA /* FundRecommendationListViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FundRecommendationListViewModel.swift; sourceTree = "<group>"; };
		DF0ADCA72BD769C30098FDAF /* HomeCoordinator.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = HomeCoordinator.swift; sourceTree = "<group>"; };
		DF0ADCA92BD769C30098FDAF /* NewsListViewModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = NewsListViewModel.swift; sourceTree = "<group>"; };
		DF0ADCAB2BD769C30098FDAF /* BannerCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = BannerCell.swift; sourceTree = "<group>"; };
		DF0ADCAC2BD769C30098FDAF /* NewsCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = NewsCell.swift; sourceTree = "<group>"; };
		DF0ADCAD2BD769C30098FDAF /* ShortcutCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ShortcutCell.swift; sourceTree = "<group>"; };
		DF0ADCAE2BD769C30098FDAF /* NewsLargeCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = NewsLargeCell.swift; sourceTree = "<group>"; };
		DF0ADCAF2BD769C30098FDAF /* InstrumentCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = InstrumentCell.swift; sourceTree = "<group>"; };
		DF0ADCB02BD769C30098FDAF /* MessageCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MessageCell.swift; sourceTree = "<group>"; };
		DF0ADCB12BD769C30098FDAF /* WalletSummaryCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = WalletSummaryCell.swift; sourceTree = "<group>"; };
		DF0ADCB22BD769C30098FDAF /* WatchlistTypeCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = WatchlistTypeCell.swift; sourceTree = "<group>"; };
		DF0ADCB32BD769C30098FDAF /* NewsDetailViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = NewsDetailViewController.swift; sourceTree = "<group>"; };
		DF0ADCB42BD769C30098FDAF /* HomeLayoutConfig.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = HomeLayoutConfig.swift; sourceTree = "<group>"; };
		DF0ADCB52BD769C30098FDAF /* HomeViewModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = HomeViewModel.swift; sourceTree = "<group>"; };
		DF0ADCB62BD769C30098FDAF /* HomeSectionData.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = HomeSectionData.swift; sourceTree = "<group>"; };
		DF0ADCB72BD769C30098FDAF /* HomeViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = HomeViewController.swift; sourceTree = "<group>"; };
		DF0ADCB92BD769C30098FDAF /* UserProfileHeaderView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = UserProfileHeaderView.swift; sourceTree = "<group>"; };
		DF0ADCBA2BD769C30098FDAF /* WatchlistHeaderView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = WatchlistHeaderView.swift; sourceTree = "<group>"; };
		DF0ADCBB2BD769C30098FDAF /* TitleHeaderView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = TitleHeaderView.swift; sourceTree = "<group>"; };
		DF0ADCBC2BD769C30098FDAF /* InstrumentItemView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = InstrumentItemView.swift; sourceTree = "<group>"; };
		DF0ADCBD2BD769C30098FDAF /* WatchlistTypeView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = WatchlistTypeView.swift; sourceTree = "<group>"; };
		DF0ADCBE2BD769C30098FDAF /* RoundedBackgroundView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = RoundedBackgroundView.swift; sourceTree = "<group>"; };
		DF0ADCBF2BD769C30098FDAF /* SeeMoreFooterView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SeeMoreFooterView.swift; sourceTree = "<group>"; };
		DF0ADCC02BD769C30098FDAF /* NewsListViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = NewsListViewController.swift; sourceTree = "<group>"; };
		DF0AE0652BD76D6E0098FDAF /* APILayer.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = APILayer.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		DF0AE0662BD76D6E0098FDAF /* CUIModule.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = CUIModule.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		DF273D692BD7680A003F9464 /* Home.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Home.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		DF273D6C2BD7680A003F9464 /* Home.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = Home.h; sourceTree = "<group>"; };
		F37A9EE49C69C2566FE448AD /* Pods_Home.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_Home.framework; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		DF273D662BD7680A003F9464 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				E65BCAB94F9FAF8B2BF3F676 /* Pods_Home.framework in Frameworks */,
				DF0AE0672BD76D6E0098FDAF /* APILayer.framework in Frameworks */,
				DF0AE0692BD76D6E0098FDAF /* CUIModule.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		65C1CF4C7B86846F2B921B97 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				DF0AE0652BD76D6E0098FDAF /* APILayer.framework */,
				DF0AE0662BD76D6E0098FDAF /* CUIModule.framework */,
				F37A9EE49C69C2566FE448AD /* Pods_Home.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		758E5C33406D5F3960FE6743 /* Pods */ = {
			isa = PBXGroup;
			children = (
				82AC42C14C6E74A32CDFEDE9 /* Pods-Home.debug.xcconfig */,
				70CBAE0A2CB2B63477A30A97 /* Pods-Home.release.xcconfig */,
			);
			name = Pods;
			path = ../Pods;
			sourceTree = "<group>";
		};
		DF0ADCA82BD769C30098FDAF /* Home */ = {
			isa = PBXGroup;
			children = (
				DF0ADCB72BD769C30098FDAF /* HomeViewController.swift */,
				DF0ADCB52BD769C30098FDAF /* HomeViewModel.swift */,
				DF0ADCB42BD769C30098FDAF /* HomeLayoutConfig.swift */,
				DF0ADCB62BD769C30098FDAF /* HomeSectionData.swift */,
				DF0ADCAA2BD769C30098FDAF /* Cells */,
				DF0ADCB82BD769C30098FDAF /* Views */,
				DF0ADCC02BD769C30098FDAF /* NewsListViewController.swift */,
				DF0ADCA92BD769C30098FDAF /* NewsListViewModel.swift */,
				DF0ADCB32BD769C30098FDAF /* NewsDetailViewController.swift */,
				835A4A3F2D2A729E00C212EE /* WatchListSortingViewController.swift */,
				AA1234552E5B08DE007439DA /* FundRecommendationListViewController.swift */,
				AA1234572E5B08DE007439DA /* FundRecommendationListViewModel.swift */,
			);
			path = Home;
			sourceTree = "<group>";
		};
		DF0ADCAA2BD769C30098FDAF /* Cells */ = {
			isa = PBXGroup;
			children = (
				0C4DB72B2E5B08DE007439DA /* FundRecommendationCell.swift */,
				0C4DB7272E58D395007439DA /* MeritReportCell.swift */,
				DF0ADCAB2BD769C30098FDAF /* BannerCell.swift */,
				DF0ADCAC2BD769C30098FDAF /* NewsCell.swift */,
				DF0ADCAD2BD769C30098FDAF /* ShortcutCell.swift */,
				DF0ADCAE2BD769C30098FDAF /* NewsLargeCell.swift */,
				DF0ADCAF2BD769C30098FDAF /* InstrumentCell.swift */,
				DF0ADCB02BD769C30098FDAF /* MessageCell.swift */,
				DF0ADCB12BD769C30098FDAF /* WalletSummaryCell.swift */,
				DF0ADCB22BD769C30098FDAF /* WatchlistTypeCell.swift */,
			);
			path = Cells;
			sourceTree = "<group>";
		};
		DF0ADCB82BD769C30098FDAF /* Views */ = {
			isa = PBXGroup;
			children = (
				DF0ADCB92BD769C30098FDAF /* UserProfileHeaderView.swift */,
				DF0ADCBA2BD769C30098FDAF /* WatchlistHeaderView.swift */,
				DF0ADCBB2BD769C30098FDAF /* TitleHeaderView.swift */,
				DF0ADCBC2BD769C30098FDAF /* InstrumentItemView.swift */,
				DF0ADCBD2BD769C30098FDAF /* WatchlistTypeView.swift */,
				DF0ADCBE2BD769C30098FDAF /* RoundedBackgroundView.swift */,
				DF0ADCBF2BD769C30098FDAF /* SeeMoreFooterView.swift */,
				83D977BC2CF4B8F600A6BC4D /* IndexIndicatorFooterView.swift */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		DF273D5F2BD7680A003F9464 = {
			isa = PBXGroup;
			children = (
				DF273D6B2BD7680A003F9464 /* Home */,
				DF273D6A2BD7680A003F9464 /* Products */,
				758E5C33406D5F3960FE6743 /* Pods */,
				65C1CF4C7B86846F2B921B97 /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		DF273D6A2BD7680A003F9464 /* Products */ = {
			isa = PBXGroup;
			children = (
				DF273D692BD7680A003F9464 /* Home.framework */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		DF273D6B2BD7680A003F9464 /* Home */ = {
			isa = PBXGroup;
			children = (
				0C4DB7252E58D37E007439DA /* MeritReport.swift */,
				DF0ADCA72BD769C30098FDAF /* HomeCoordinator.swift */,
				DF0ADCA82BD769C30098FDAF /* Home */,
				DF273D6C2BD7680A003F9464 /* Home.h */,
			);
			path = Home;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		DF273D642BD7680A003F9464 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				DF273D6D2BD7680A003F9464 /* Home.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		DF273D682BD7680A003F9464 /* Home */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = DF273D702BD7680A003F9464 /* Build configuration list for PBXNativeTarget "Home" */;
			buildPhases = (
				17F25CA076CB842732BD30D3 /* [CP] Check Pods Manifest.lock */,
				DF273D642BD7680A003F9464 /* Headers */,
				DF273D652BD7680A003F9464 /* Sources */,
				DF273D662BD7680A003F9464 /* Frameworks */,
				DF273D672BD7680A003F9464 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = Home;
			productName = Home;
			productReference = DF273D692BD7680A003F9464 /* Home.framework */;
			productType = "com.apple.product-type.framework";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		DF273D602BD7680A003F9464 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastUpgradeCheck = 1500;
				TargetAttributes = {
					DF273D682BD7680A003F9464 = {
						CreatedOnToolsVersion = 15.0;
						LastSwiftMigration = 1500;
					};
				};
			};
			buildConfigurationList = DF273D632BD7680A003F9464 /* Build configuration list for PBXProject "Home" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = DF273D5F2BD7680A003F9464;
			productRefGroup = DF273D6A2BD7680A003F9464 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				DF273D682BD7680A003F9464 /* Home */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		DF273D672BD7680A003F9464 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		17F25CA076CB842732BD30D3 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-Home-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		DF273D652BD7680A003F9464 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				DF0ADCC92BD769C30098FDAF /* InstrumentCell.swift in Sources */,
				DF0ADCCA2BD769C30098FDAF /* MessageCell.swift in Sources */,
				DF0ADCD82BD769C30098FDAF /* SeeMoreFooterView.swift in Sources */,
				DF0ADCC32BD769C30098FDAF /* HomeCoordinator.swift in Sources */,
				DF0ADCCB2BD769C30098FDAF /* WalletSummaryCell.swift in Sources */,
				DF0ADCD52BD769C30098FDAF /* InstrumentItemView.swift in Sources */,
				DF0ADCC42BD769C30098FDAF /* NewsListViewModel.swift in Sources */,
				835A4A402D2A729E00C212EE /* WatchListSortingViewController.swift in Sources */,
				DF0ADCD12BD769C30098FDAF /* HomeViewController.swift in Sources */,
				DF0ADCC62BD769C30098FDAF /* NewsCell.swift in Sources */,
				DF0ADCCE2BD769C30098FDAF /* HomeLayoutConfig.swift in Sources */,
				DF0ADCCD2BD769C30098FDAF /* NewsDetailViewController.swift in Sources */,
				DF0ADCD02BD769C30098FDAF /* HomeSectionData.swift in Sources */,
				DF0ADCD22BD769C30098FDAF /* UserProfileHeaderView.swift in Sources */,
				DF0ADCD32BD769C30098FDAF /* WatchlistHeaderView.swift in Sources */,
				DF0ADCCF2BD769C30098FDAF /* HomeViewModel.swift in Sources */,
				DF0ADCC52BD769C30098FDAF /* BannerCell.swift in Sources */,
				DF0ADCCC2BD769C30098FDAF /* WatchlistTypeCell.swift in Sources */,
				DF0ADCD72BD769C30098FDAF /* RoundedBackgroundView.swift in Sources */,
				0C4DB7262E58D37E007439DA /* MeritReport.swift in Sources */,
				DF0ADCC82BD769C30098FDAF /* NewsLargeCell.swift in Sources */,
				0C4DB72C2E5B08DE007439DA /* FundRecommendationCell.swift in Sources */,
				DF0ADCD92BD769C30098FDAF /* NewsListViewController.swift in Sources */,
				DF0ADCD42BD769C30098FDAF /* TitleHeaderView.swift in Sources */,
				DF0ADCD62BD769C30098FDAF /* WatchlistTypeView.swift in Sources */,
				83D977BD2CF4B8F600A6BC4D /* IndexIndicatorFooterView.swift in Sources */,
				0C4DB7282E58D395007439DA /* MeritReportCell.swift in Sources */,
				DF0ADCC72BD769C30098FDAF /* ShortcutCell.swift in Sources */,
				AA1234562E5B08DE007439DA /* FundRecommendationListViewController.swift in Sources */,
				AA1234582E5B08DE007439DA /* FundRecommendationListViewModel.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		DF273D6E2BD7680A003F9464 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		DF273D6F2BD7680A003F9464 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		DF273D712BD7680A003F9464 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 82AC42C14C6E74A32CDFEDE9 /* Pods-Home.debug.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				MODULE_VERIFIER_SUPPORTED_LANGUAGES = "objective-c objective-c++";
				MODULE_VERIFIER_SUPPORTED_LANGUAGE_STANDARDS = "gnu17 gnu++20";
				PRODUCT_BUNDLE_IDENTIFIER = com.sirius.Home;
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		DF273D722BD7680A003F9464 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 70CBAE0A2CB2B63477A30A97 /* Pods-Home.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				MODULE_VERIFIER_SUPPORTED_LANGUAGES = "objective-c objective-c++";
				MODULE_VERIFIER_SUPPORTED_LANGUAGE_STANDARDS = "gnu17 gnu++20";
				PRODUCT_BUNDLE_IDENTIFIER = com.sirius.Home;
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		DF273D632BD7680A003F9464 /* Build configuration list for PBXProject "Home" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				DF273D6E2BD7680A003F9464 /* Debug */,
				DF273D6F2BD7680A003F9464 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		DF273D702BD7680A003F9464 /* Build configuration list for PBXNativeTarget "Home" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				DF273D712BD7680A003F9464 /* Debug */,
				DF273D722BD7680A003F9464 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = DF273D602BD7680A003F9464 /* Project object */;
}
