//
//  MeritReportCell.swift
//  Home
//
//  Created by Augment Agent on 22/08/2025.
//

import UIKit
import Core
import CUIModule
import Utils

final class MeritReportCell: UICollectionViewCell, AnyView {
    
    // MARK: UI properties
    private lazy var contentStackView = {
        let stackView = UIStackView(axis: .vertical,
                                    spacing: 4)
        stackView.addArrangedSubviews([titleLabel,
                                       authorDateLabel])
        
        return stackView
    }()
    
    private lazy var titleLabel = UILabel(font: Font.semiBold.of(size: 14),
                                          numberOfLines: 2,
                                          textColor: Color.txtTitle)
    
    private lazy var authorDateLabel = UILabel(font: Font.regular.of(size: 12),
                                               textColor: Color.txtParagraph)
    
    // MARK: Life cycle
    override init(frame: CGRect) {
        super.init(frame: frame)
        
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func setupUI() {
        backgroundColor = .clear
        contentView.backgroundColor = .clear
        contentView.addSubview(contentStackView)
        
        contentStackView.snp.makeConstraints { 
            $0.top.equalTo(8)
            $0.bottom.equalTo(-8).priority(.high)
            $0.left.right.equalToSuperview()
        }
    }
    
    func updateUI(with displayModel: UIDisplayModel) {
        guard let meritReport = displayModel as? MeritReport else { return }

        titleLabel.text = meritReport.title
        titleLabel.setLineSpacing(lineHeightMultiple: 1.07)

        // Format date with proper spacing
        let formattedDate = meritReport.formattedPublishedDate

        // Create attributed string for date (no author in new API response)
        authorDateLabel.text = formattedDate
    }
}
