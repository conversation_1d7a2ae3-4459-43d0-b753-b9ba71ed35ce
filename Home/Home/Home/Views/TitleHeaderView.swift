//
//  TitleHeaderView.swift
//  Home
//
//  Created by <PERSON> on 23/02/2024.
//

import UIKit
import Core
import CUIModule
import RxSwift
import RxCocoa

final class TitleHeaderView: UICollectionReusableView, AnyView {

    // MARK: UI properties
    private lazy var titleLabel = UILabel(font: Font.bold.of(size: 12),
                                          textColor: Color.txtTitle)

    // MARK: Life cycle
    override init(frame: CGRect) {
        super.init(frame: frame)

        setupUI()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    func setupUI() {
        addSubview(titleLabel)
        titleLabel.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }

    func update(title: String) {
        titleLabel.text = title
    }
}

// MARK: - FundRecommendationTitleHeaderView
final class FundRecommendationTitleHeaderView: UICollectionReusableView, AnyView {

    // MARK: UI properties
    private lazy var containerStackView = {
        let stackView = UIStackView(axis: .horizontal, alignment: .center)
        stackView.addArrangedSubviews([titleLabel, UIView(), moreButton])
        return stackView
    }()

    private lazy var titleLabel = UILabel(font: Font.medium.of(size: 12),
                                          textColor: Color.txtTitle)

    private lazy var moreButton = {
        let button = UIButton(type: .system)
        button.setTitle("More", for: .normal)
        button.setImage(.image(named: "icon-arrow"), for: .normal)
        button.titleLabel?.font = Font.medium.of(size: 12)
        button.setTitleColor(Color.txtLabel, for: .normal)
        button.tintColor = Color.txtLabel
        button.semanticContentAttribute = .forceRightToLeft
        button.titleEdgeInsets = UIEdgeInsets(top: 0, left: -4, bottom: 0, right: 4)
        button.imageEdgeInsets = UIEdgeInsets(top: 0, left: 4, bottom: 0, right: -4)
        return button
    }()

    // MARK: Properties
    var disposeBag = DisposeBag()

    // MARK: Life cycle
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    override func prepareForReuse() {
        super.prepareForReuse()
        disposeBag = DisposeBag()
    }

    func setupUI() {
        addSubview(containerStackView)
        containerStackView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }

    func update(title: String) {
        titleLabel.text = title
    }
}

// MARK: - Reactive Extension
extension Reactive where Base: FundRecommendationTitleHeaderView {
    var tapMore: Observable<Void> {
        base.moreButton.rx.tap.asObservable()
    }
}
