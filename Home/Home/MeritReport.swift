//
//  MeritReport.swift
//  Home
//
//  Created by Augment Agent on 23/08/2025.
//

import Foundation
import CUIModule
import Core

public struct MeritReport: Codable {
    public let id: Int
    public let uuid: String
    public let title: String
    public let status: Int
    public let webUrl: String
    public let banner: Int
    public let created: Int
    public let published: Int64

    public init(id: Int, uuid: String, title: String, status: Int, webUrl: String, banner: Int, created: Int, published: Int64) {
        self.id = id
        self.uuid = uuid
        self.title = title
        self.status = status
        self.webUrl = webUrl
        self.banner = banner
        self.created = created
        self.published = published
    }
    
    // Legacy initializer for backward compatibility
    public init(uuid: String, title: String, author: String, publishedAt: String) {
        self.id = 0
        self.uuid = uuid
        self.title = title
        self.status = 0
        self.webUrl = ""
        self.banner = 0
        self.created = 0
        
        // Convert string date to timestamp
        let dateFormatter = DateFormatter()
        dateFormatter.calendar = Calendar(identifier: .gregorian)
        dateFormatter.dateFormat = "yyyy-MM-dd"
        if let date = dateFormatter.date(from: publishedAt) {
            self.published = Int64(date.timeIntervalSince1970 * 1000)
        } else {
            self.published = 0
        }
    }
    
    public func publishedDate() -> Date {
        // Convert milliseconds timestamp to Date
        let timeInterval = TimeInterval(published / 1000)
        return Date(timeIntervalSince1970: timeInterval)
    }
    
    // Helper property for displaying formatted date
    public var formattedPublishedDate: String {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        return dateFormatter.string(from: publishedDate())
    }
}

// MARK: - extension MeritReport
extension MeritReport: UIDisplayModel {}
