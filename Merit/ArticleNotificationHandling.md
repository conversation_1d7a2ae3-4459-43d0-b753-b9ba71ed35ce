# Article Link Notification Handling

This document describes the implementation of JPush notification handling for article links in the Merit iOS app.

## Overview

The app now supports handling JPush notifications that contain article links with message type "ARTICL<PERSON>". When a user taps on such a notification, the app will:

1. Check if the message type is "ARTICLE"
2. Extract the article URL from the notification payload
3. Handle different app states (foreground, background, not started)
4. Navigate to the main app interface
5. Switch to the Home tab
6. Open the article in a web view

## Implementation Details

### AppDelegate Changes

The `AppDelegate.swift` file has been modified to include:

1. **Article URL Storage**: Properties to store URLs for different app states
2. **Notification Handling**: Enhanced `jpushNotificationCenter didReceive response` method
3. **Message Type Validation**: Check for "ARTICLE" message type before processing
4. **App State Handling**: Handle three different app states (foreground, background, not started)
5. **Login Integration**: Store article URLs for after login completion

### MainTabbarCoordinator Changes

The `MainTabbarCoordinator.swift` file has been modified to:

1. **Notification Observer**: Listen for article notification events
2. **Navigation Logic**: Handle navigation to the Home tab and open the article web view
3. **Memory Management**: Proper cleanup of notification observers

### AppCoordinator Changes

The `AppCoordinator.swift` file has been modified to:

1. **Login Success Handler**: Call pending article handler after successful login
2. **Integration**: Connect login flow with article notification handling

## App State Handling

The implementation handles three different app states:

### 1. App in Foreground (Active)
- Immediately navigate to the article
- Switch to Home tab and open web view

### 2. App in Background
- Store the article URL temporarily
- Navigate when app becomes active

### 3. App Not Started (Inactive)
- Store the article URL in UserDefaults
- Show article after user completes login

## Notification Payload Format

The implementation requires a specific JPush notification payload format:

### Required Format
```json
{
  "aps": {
    "alert": "Check out this new article!"
  },
  "extras": {
    "message_type": "ARTICLE",
    "article_url": "https://example.com/article/123"
  }
}
```

**Important Notes:**
- The `message_type` must be exactly "ARTICLE" (case-sensitive)
- The `article_url` must be a valid URL string
- Both fields must be in the `extras` object
- Other message types can be added in the future by using different `message_type` values

## Usage

### For Backend/Push Service

When sending a push notification with an article link, use the required format:

```json
{
  "aps": {
    "alert": "New article available!",
    "sound": "default"
  },
  "extras": {
    "message_type": "ARTICLE",
    "article_url": "https://your-domain.com/articles/article-id"
  }
}
```

### For Testing

To test the functionality in different app states:

1. **App in Foreground**: Send notification while app is open - should navigate immediately
2. **App in Background**: Send notification while app is backgrounded - should navigate when app becomes active
3. **App Not Started**: Send notification while app is closed - should show article after login

### Testing with Debug Function

In debug builds, you can test using the provided test function:

```swift
if let appDelegate = UIApplication.shared.delegate as? AppDelegate {
    appDelegate.testArticleNotification(url: "https://example.com/test-article")
}
```

## Technical Notes

1. **Thread Safety**: All UI navigation is performed on the main thread
2. **App State Handling**: URLs are stored when the app is not active and processed when it becomes active
3. **Error Handling**: Invalid URLs or missing data are gracefully ignored
4. **Memory Management**: Notification observers are properly cleaned up to prevent memory leaks

## Future Enhancements

Potential improvements could include:

1. **Custom Article Viewer**: Instead of using a generic web view, create a dedicated article viewer
2. **Analytics**: Track article opens from notifications
3. **Offline Support**: Cache articles for offline reading
4. **Deep Linking**: Support for more complex URL schemes and parameters
