// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 56;
	objects = {

/* Begin PBXBuildFile section */
		0907F31B82984808E4DC441E /* Pods_AppSetting.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7C70AAE99B71D7A5BF5535FB /* Pods_AppSetting.framework */; };
		83DA72CF2D13E15800CA4BD7 /* OTPVerificationViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 83DA72CE2D13E15800CA4BD7 /* OTPVerificationViewModel.swift */; };
		83DA72D12D13F43A00CA4BD7 /* EmailUpdatedViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 83DA72D02D13F43A00CA4BD7 /* EmailUpdatedViewController.swift */; };
		83DA72D32D14015900CA4BD7 /* EmailUpdatedViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 83DA72D22D14015900CA4BD7 /* EmailUpdatedViewModel.swift */; };
		83DA72D52D140DE700CA4BD7 /* BankAccountDetailViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 83DA72D42D140DE700CA4BD7 /* BankAccountDetailViewController.swift */; };
		83DA72D72D140DF200CA4BD7 /* BankAccountDetailViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 83DA72D62D140DF200CA4BD7 /* BankAccountDetailViewModel.swift */; };
		83DA72DA2D14355500CA4BD7 /* BankAccountDetailCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 83DA72D92D14355500CA4BD7 /* BankAccountDetailCell.swift */; };
		83DA72DD2D14356C00CA4BD7 /* BankAccountDetailHeaderView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 83DA72DC2D14356C00CA4BD7 /* BankAccountDetailHeaderView.swift */; };
		83DA72E42D147F5400CA4BD7 /* AddEditBankAccountViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 83DA72E32D147F5400CA4BD7 /* AddEditBankAccountViewController.swift */; };
		83DA72E62D147F6000CA4BD7 /* AddEditBankAccountViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 83DA72E52D147F6000CA4BD7 /* AddEditBankAccountViewModel.swift */; };
		83DA72EB2D148CF900CA4BD7 /* BankAccountModels.swift in Sources */ = {isa = PBXBuildFile; fileRef = 83DA72EA2D148CF900CA4BD7 /* BankAccountModels.swift */; };
		83DA72ED2D15348800CA4BD7 /* BankAccountActionBottomSheet.swift in Sources */ = {isa = PBXBuildFile; fileRef = 83DA72EC2D15348800CA4BD7 /* BankAccountActionBottomSheet.swift */; };
		83F6C6522D0AF56F001158C0 /* SettingItemCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 83F6C6512D0AF56F001158C0 /* SettingItemCell.swift */; };
		DF0AD8EB2BD769700098FDAF /* SetNewPasswordViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8972BD769700098FDAF /* SetNewPasswordViewController.swift */; };
		DF0AD8EC2BD769700098FDAF /* ChangePasswordViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8982BD769700098FDAF /* ChangePasswordViewController.swift */; };
		DF0AD8ED2BD769700098FDAF /* ChangePasswordViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8992BD769700098FDAF /* ChangePasswordViewModel.swift */; };
		DF0AD8EE2BD769700098FDAF /* CustomThemeHeaderView.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD89B2BD769700098FDAF /* CustomThemeHeaderView.swift */; };
		DF0AD8EF2BD769700098FDAF /* NotiSettingResetBottomSheet.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD89C2BD769700098FDAF /* NotiSettingResetBottomSheet.swift */; };
		DF0AD8F02BD769700098FDAF /* CustomThemeOptionButton.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD89E2BD769700098FDAF /* CustomThemeOptionButton.swift */; };
		DF0AD8F12BD769700098FDAF /* CustomThemeOptionBottomSheet.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD89F2BD769700098FDAF /* CustomThemeOptionBottomSheet.swift */; };
		DF0AD8F22BD769700098FDAF /* CustomThemeView.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8A02BD769700098FDAF /* CustomThemeView.swift */; };
		DF0AD8F32BD769700098FDAF /* ProfileItemView.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8A12BD769700098FDAF /* ProfileItemView.swift */; };
		DF0AD8F42BD769700098FDAF /* ThemeListEmptyView.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8A32BD769700098FDAF /* ThemeListEmptyView.swift */; };
		DF0AD8F52BD769700098FDAF /* ThemeListCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8A42BD769700098FDAF /* ThemeListCell.swift */; };
		DF0AD8F62BD769700098FDAF /* ThemeListHeaderView.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8A52BD769700098FDAF /* ThemeListHeaderView.swift */; };
		DF0AD8F72BD769700098FDAF /* ThemeColorItemView.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8A72BD769700098FDAF /* ThemeColorItemView.swift */; };
		DF0AD8F82BD769700098FDAF /* ThemeSmartColorView.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8A82BD769700098FDAF /* ThemeSmartColorView.swift */; };
		DF0AD8F92BD769700098FDAF /* ThemePresetStyleSelectionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8A92BD769700098FDAF /* ThemePresetStyleSelectionView.swift */; };
		DF0AD8FA2BD769700098FDAF /* ThemeCustomStyleSelectionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8AA2BD769700098FDAF /* ThemeCustomStyleSelectionView.swift */; };
		DF0AD8FB2BD769700098FDAF /* ThemeColorSelectionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8AB2BD769700098FDAF /* ThemeColorSelectionView.swift */; };
		DF0AD8FC2BD769700098FDAF /* ThemeSegementPreview.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8AD2BD769700098FDAF /* ThemeSegementPreview.swift */; };
		DF0AD8FD2BD769700098FDAF /* ThemeTextPreview.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8AE2BD769700098FDAF /* ThemeTextPreview.swift */; };
		DF0AD8FE2BD769700098FDAF /* ThemeButtonsPreview.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8AF2BD769700098FDAF /* ThemeButtonsPreview.swift */; };
		DF0AD8FF2BD769700098FDAF /* ThemeBadgePreview.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8B02BD769700098FDAF /* ThemeBadgePreview.swift */; };
		DF0AD9002BD769700098FDAF /* ThemePreview.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8B12BD769700098FDAF /* ThemePreview.swift */; };
		DF0AD9012BD769700098FDAF /* AppThemeViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8B32BD769700098FDAF /* AppThemeViewController.swift */; };
		DF0AD9022BD769700098FDAF /* NotificaitonSettingView.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8B42BD769700098FDAF /* NotificaitonSettingView.swift */; };
		DF0AD9032BD769700098FDAF /* AppThemeViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8B52BD769700098FDAF /* AppThemeViewModel.swift */; };
		DF0AD9042BD769700098FDAF /* NotificationSettingViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8B72BD769700098FDAF /* NotificationSettingViewModel.swift */; };
		DF0AD9052BD769700098FDAF /* NotificationSettingViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8B82BD769700098FDAF /* NotificationSettingViewController.swift */; };
		DF0AD9062BD769700098FDAF /* AppSettingCoordinator.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8B92BD769700098FDAF /* AppSettingCoordinator.swift */; };
		DF0AD9072BD769700098FDAF /* PrivacyViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8BB2BD769700098FDAF /* PrivacyViewController.swift */; };
		DF0AD9082BD769700098FDAF /* SecurityViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8BD2BD769700098FDAF /* SecurityViewController.swift */; };
		DF0AD9092BD769700098FDAF /* SecurityItemView.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8BE2BD769700098FDAF /* SecurityItemView.swift */; };
		DF0AD90A2BD769700098FDAF /* SecurityViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8BF2BD769700098FDAF /* SecurityViewModel.swift */; };
		DF0AD90B2BD769700098FDAF /* TermsViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8C12BD769700098FDAF /* TermsViewController.swift */; };
		DF0AD90C2BD769700098FDAF /* AppSettingViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8C32BD769700098FDAF /* AppSettingViewModel.swift */; };
		DF0AD90E2BD769700098FDAF /* AppSettingViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8C52BD769700098FDAF /* AppSettingViewController.swift */; };
		DF0AD90F2BD769700098FDAF /* CustomThemeCreateViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8C72BD769700098FDAF /* CustomThemeCreateViewController.swift */; };
		DF0AD9102BD769700098FDAF /* CustomThemeCreateViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8C82BD769700098FDAF /* CustomThemeCreateViewModel.swift */; };
		DF0AD9112BD769700098FDAF /* CustomThemeNameViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8CA2BD769700098FDAF /* CustomThemeNameViewController.swift */; };
		DF0AD9122BD769700098FDAF /* CustomThemeNameViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8CB2BD769700098FDAF /* CustomThemeNameViewModel.swift */; };
		DF0AD9132BD769700098FDAF /* CustomThemeStyleViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8CD2BD769700098FDAF /* CustomThemeStyleViewController.swift */; };
		DF0AD9142BD769700098FDAF /* CustomThemeStyleViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8CE2BD769700098FDAF /* CustomThemeStyleViewModel.swift */; };
		DF0AD9152BD769700098FDAF /* CustomThemeTutorialViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8D02BD769700098FDAF /* CustomThemeTutorialViewModel.swift */; };
		DF0AD9162BD769700098FDAF /* CustomThemeTutorialViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8D12BD769700098FDAF /* CustomThemeTutorialViewController.swift */; };
		DF0AD9172BD769700098FDAF /* SuspendAccountViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8D32BD769700098FDAF /* SuspendAccountViewController.swift */; };
		DF0AD9182BD769700098FDAF /* SuspendAccountViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8D42BD769700098FDAF /* SuspendAccountViewModel.swift */; };
		DF0AD9192BD769700098FDAF /* SuspendAccountSuccessfulViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8D62BD769700098FDAF /* SuspendAccountSuccessfulViewController.swift */; };
		DF0AD91A2BD769700098FDAF /* SuspendAccountSuccessfulViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8D72BD769700098FDAF /* SuspendAccountSuccessfulViewModel.swift */; };
		DF0AD91B2BD769700098FDAF /* FAQCategoryHeaderView.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8DA2BD769700098FDAF /* FAQCategoryHeaderView.swift */; };
		DF0AD91C2BD769700098FDAF /* FAQCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8DB2BD769700098FDAF /* FAQCell.swift */; };
		DF0AD91D2BD769700098FDAF /* FAQDataModels.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8DC2BD769700098FDAF /* FAQDataModels.swift */; };
		DF0AD91E2BD769700098FDAF /* FAQViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8DD2BD769700098FDAF /* FAQViewModel.swift */; };
		DF0AD91F2BD769700098FDAF /* FAQViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8DE2BD769700098FDAF /* FAQViewController.swift */; };
		DF0AD9202BD769700098FDAF /* CustomThemeListViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8E02BD769700098FDAF /* CustomThemeListViewController.swift */; };
		DF0AD9212BD769700098FDAF /* CustomThemeListViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8E12BD769700098FDAF /* CustomThemeListViewModel.swift */; };
		DF0AD9222BD769700098FDAF /* EditProfileViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8E32BD769700098FDAF /* EditProfileViewController.swift */; };
		DF0AD9232BD769700098FDAF /* OTPVerificationViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8E42BD769700098FDAF /* OTPVerificationViewController.swift */; };
		DF0AD9242BD769700098FDAF /* EditProfileViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8E52BD769700098FDAF /* EditProfileViewModel.swift */; };
		DF0AD9252BD769700098FDAF /* ChangeProfileInfoBottomSheet.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8E72BD769700098FDAF /* ChangeProfileInfoBottomSheet.swift */; };
		DF0AD9262BD769700098FDAF /* NonTradingWarningBottomSheet.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8E82BD769700098FDAF /* NonTradingWarningBottomSheet.swift */; };
		DF0AD9272BD769700098FDAF /* ChangeProfileInfoViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8E92BD769700098FDAF /* ChangeProfileInfoViewModel.swift */; };
		DF0AD9282BD769700098FDAF /* EditProfilePictureBottomSheet.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8EA2BD769700098FDAF /* EditProfilePictureBottomSheet.swift */; };
		DF0AE0352BD76CA30098FDAF /* APILayer.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = DF0AE0342BD76CA30098FDAF /* APILayer.framework */; };
		DF0AE0392BD76CAC0098FDAF /* Authentication.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = DF0AE0382BD76CAC0098FDAF /* Authentication.framework */; };
		DF0AE03C2BD76CB70098FDAF /* CUIModule.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = DF0AE03B2BD76CB70098FDAF /* CUIModule.framework */; };
		DF0AE03F2BD76CC30098FDAF /* Storage.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = DF0AE03E2BD76CC30098FDAF /* Storage.framework */; };
		DF1A112F2C04691100203086 /* SettingItemView.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF1A112E2C04691100203086 /* SettingItemView.swift */; };
		************************ /* AppSetting.h in Headers */ = {isa = PBXBuildFile; fileRef = ************************ /* AppSetting.h */; settings = {ATTRIBUTES = (Public, ); }; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		7C70AAE99B71D7A5BF5535FB /* Pods_AppSetting.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_AppSetting.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		83DA72CE2D13E15800CA4BD7 /* OTPVerificationViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = OTPVerificationViewModel.swift; sourceTree = "<group>"; };
		83DA72D02D13F43A00CA4BD7 /* EmailUpdatedViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = EmailUpdatedViewController.swift; sourceTree = "<group>"; };
		83DA72D22D14015900CA4BD7 /* EmailUpdatedViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = EmailUpdatedViewModel.swift; sourceTree = "<group>"; };
		83DA72D42D140DE700CA4BD7 /* BankAccountDetailViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BankAccountDetailViewController.swift; sourceTree = "<group>"; };
		83DA72D62D140DF200CA4BD7 /* BankAccountDetailViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BankAccountDetailViewModel.swift; sourceTree = "<group>"; };
		83DA72D92D14355500CA4BD7 /* BankAccountDetailCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BankAccountDetailCell.swift; sourceTree = "<group>"; };
		83DA72DC2D14356C00CA4BD7 /* BankAccountDetailHeaderView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BankAccountDetailHeaderView.swift; sourceTree = "<group>"; };
		83DA72E32D147F5400CA4BD7 /* AddEditBankAccountViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AddEditBankAccountViewController.swift; sourceTree = "<group>"; };
		83DA72E52D147F6000CA4BD7 /* AddEditBankAccountViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AddEditBankAccountViewModel.swift; sourceTree = "<group>"; };
		83DA72EA2D148CF900CA4BD7 /* BankAccountModels.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BankAccountModels.swift; sourceTree = "<group>"; };
		83DA72EC2D15348800CA4BD7 /* BankAccountActionBottomSheet.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BankAccountActionBottomSheet.swift; sourceTree = "<group>"; };
		83F6C6512D0AF56F001158C0 /* SettingItemCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SettingItemCell.swift; sourceTree = "<group>"; };
		A1EE4385BAB876608AAC3E83 /* Pods-AppSetting.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-AppSetting.debug.xcconfig"; path = "Target Support Files/Pods-AppSetting/Pods-AppSetting.debug.xcconfig"; sourceTree = "<group>"; };
		DF0AD8972BD769700098FDAF /* SetNewPasswordViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SetNewPasswordViewController.swift; sourceTree = "<group>"; };
		DF0AD8982BD769700098FDAF /* ChangePasswordViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ChangePasswordViewController.swift; sourceTree = "<group>"; };
		DF0AD8992BD769700098FDAF /* ChangePasswordViewModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ChangePasswordViewModel.swift; sourceTree = "<group>"; };
		DF0AD89B2BD769700098FDAF /* CustomThemeHeaderView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CustomThemeHeaderView.swift; sourceTree = "<group>"; };
		DF0AD89C2BD769700098FDAF /* NotiSettingResetBottomSheet.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = NotiSettingResetBottomSheet.swift; sourceTree = "<group>"; };
		DF0AD89E2BD769700098FDAF /* CustomThemeOptionButton.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CustomThemeOptionButton.swift; sourceTree = "<group>"; };
		DF0AD89F2BD769700098FDAF /* CustomThemeOptionBottomSheet.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CustomThemeOptionBottomSheet.swift; sourceTree = "<group>"; };
		DF0AD8A02BD769700098FDAF /* CustomThemeView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CustomThemeView.swift; sourceTree = "<group>"; };
		DF0AD8A12BD769700098FDAF /* ProfileItemView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ProfileItemView.swift; sourceTree = "<group>"; };
		DF0AD8A32BD769700098FDAF /* ThemeListEmptyView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ThemeListEmptyView.swift; sourceTree = "<group>"; };
		DF0AD8A42BD769700098FDAF /* ThemeListCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ThemeListCell.swift; sourceTree = "<group>"; };
		DF0AD8A52BD769700098FDAF /* ThemeListHeaderView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ThemeListHeaderView.swift; sourceTree = "<group>"; };
		DF0AD8A72BD769700098FDAF /* ThemeColorItemView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ThemeColorItemView.swift; sourceTree = "<group>"; };
		DF0AD8A82BD769700098FDAF /* ThemeSmartColorView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ThemeSmartColorView.swift; sourceTree = "<group>"; };
		DF0AD8A92BD769700098FDAF /* ThemePresetStyleSelectionView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ThemePresetStyleSelectionView.swift; sourceTree = "<group>"; };
		DF0AD8AA2BD769700098FDAF /* ThemeCustomStyleSelectionView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ThemeCustomStyleSelectionView.swift; sourceTree = "<group>"; };
		DF0AD8AB2BD769700098FDAF /* ThemeColorSelectionView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ThemeColorSelectionView.swift; sourceTree = "<group>"; };
		DF0AD8AD2BD769700098FDAF /* ThemeSegementPreview.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ThemeSegementPreview.swift; sourceTree = "<group>"; };
		DF0AD8AE2BD769700098FDAF /* ThemeTextPreview.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ThemeTextPreview.swift; sourceTree = "<group>"; };
		DF0AD8AF2BD769700098FDAF /* ThemeButtonsPreview.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ThemeButtonsPreview.swift; sourceTree = "<group>"; };
		DF0AD8B02BD769700098FDAF /* ThemeBadgePreview.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ThemeBadgePreview.swift; sourceTree = "<group>"; };
		DF0AD8B12BD769700098FDAF /* ThemePreview.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ThemePreview.swift; sourceTree = "<group>"; };
		DF0AD8B32BD769700098FDAF /* AppThemeViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AppThemeViewController.swift; sourceTree = "<group>"; };
		DF0AD8B42BD769700098FDAF /* NotificaitonSettingView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = NotificaitonSettingView.swift; sourceTree = "<group>"; };
		DF0AD8B52BD769700098FDAF /* AppThemeViewModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AppThemeViewModel.swift; sourceTree = "<group>"; };
		DF0AD8B72BD769700098FDAF /* NotificationSettingViewModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = NotificationSettingViewModel.swift; sourceTree = "<group>"; };
		DF0AD8B82BD769700098FDAF /* NotificationSettingViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = NotificationSettingViewController.swift; sourceTree = "<group>"; };
		DF0AD8B92BD769700098FDAF /* AppSettingCoordinator.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AppSettingCoordinator.swift; sourceTree = "<group>"; };
		DF0AD8BB2BD769700098FDAF /* PrivacyViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = PrivacyViewController.swift; sourceTree = "<group>"; };
		DF0AD8BD2BD769700098FDAF /* SecurityViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SecurityViewController.swift; sourceTree = "<group>"; };
		DF0AD8BE2BD769700098FDAF /* SecurityItemView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SecurityItemView.swift; sourceTree = "<group>"; };
		DF0AD8BF2BD769700098FDAF /* SecurityViewModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SecurityViewModel.swift; sourceTree = "<group>"; };
		DF0AD8C12BD769700098FDAF /* TermsViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = TermsViewController.swift; sourceTree = "<group>"; };
		DF0AD8C32BD769700098FDAF /* AppSettingViewModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AppSettingViewModel.swift; sourceTree = "<group>"; };
		DF0AD8C52BD769700098FDAF /* AppSettingViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AppSettingViewController.swift; sourceTree = "<group>"; };
		DF0AD8C72BD769700098FDAF /* CustomThemeCreateViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CustomThemeCreateViewController.swift; sourceTree = "<group>"; };
		DF0AD8C82BD769700098FDAF /* CustomThemeCreateViewModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CustomThemeCreateViewModel.swift; sourceTree = "<group>"; };
		DF0AD8CA2BD769700098FDAF /* CustomThemeNameViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CustomThemeNameViewController.swift; sourceTree = "<group>"; };
		DF0AD8CB2BD769700098FDAF /* CustomThemeNameViewModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CustomThemeNameViewModel.swift; sourceTree = "<group>"; };
		DF0AD8CD2BD769700098FDAF /* CustomThemeStyleViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CustomThemeStyleViewController.swift; sourceTree = "<group>"; };
		DF0AD8CE2BD769700098FDAF /* CustomThemeStyleViewModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CustomThemeStyleViewModel.swift; sourceTree = "<group>"; };
		DF0AD8D02BD769700098FDAF /* CustomThemeTutorialViewModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CustomThemeTutorialViewModel.swift; sourceTree = "<group>"; };
		DF0AD8D12BD769700098FDAF /* CustomThemeTutorialViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CustomThemeTutorialViewController.swift; sourceTree = "<group>"; };
		DF0AD8D32BD769700098FDAF /* SuspendAccountViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SuspendAccountViewController.swift; sourceTree = "<group>"; };
		DF0AD8D42BD769700098FDAF /* SuspendAccountViewModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SuspendAccountViewModel.swift; sourceTree = "<group>"; };
		DF0AD8D62BD769700098FDAF /* SuspendAccountSuccessfulViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SuspendAccountSuccessfulViewController.swift; sourceTree = "<group>"; };
		DF0AD8D72BD769700098FDAF /* SuspendAccountSuccessfulViewModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SuspendAccountSuccessfulViewModel.swift; sourceTree = "<group>"; };
		DF0AD8DA2BD769700098FDAF /* FAQCategoryHeaderView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = FAQCategoryHeaderView.swift; sourceTree = "<group>"; };
		DF0AD8DB2BD769700098FDAF /* FAQCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = FAQCell.swift; sourceTree = "<group>"; };
		DF0AD8DC2BD769700098FDAF /* FAQDataModels.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = FAQDataModels.swift; sourceTree = "<group>"; };
		DF0AD8DD2BD769700098FDAF /* FAQViewModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = FAQViewModel.swift; sourceTree = "<group>"; };
		DF0AD8DE2BD769700098FDAF /* FAQViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = FAQViewController.swift; sourceTree = "<group>"; };
		DF0AD8E02BD769700098FDAF /* CustomThemeListViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CustomThemeListViewController.swift; sourceTree = "<group>"; };
		DF0AD8E12BD769700098FDAF /* CustomThemeListViewModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CustomThemeListViewModel.swift; sourceTree = "<group>"; };
		DF0AD8E32BD769700098FDAF /* EditProfileViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = EditProfileViewController.swift; sourceTree = "<group>"; };
		DF0AD8E42BD769700098FDAF /* OTPVerificationViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = OTPVerificationViewController.swift; sourceTree = "<group>"; };
		DF0AD8E52BD769700098FDAF /* EditProfileViewModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = EditProfileViewModel.swift; sourceTree = "<group>"; };
		DF0AD8E72BD769700098FDAF /* ChangeProfileInfoBottomSheet.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ChangeProfileInfoBottomSheet.swift; sourceTree = "<group>"; };
		DF0AD8E82BD769700098FDAF /* NonTradingWarningBottomSheet.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = NonTradingWarningBottomSheet.swift; sourceTree = "<group>"; };
		DF0AD8E92BD769700098FDAF /* ChangeProfileInfoViewModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ChangeProfileInfoViewModel.swift; sourceTree = "<group>"; };
		DF0AD8EA2BD769700098FDAF /* EditProfilePictureBottomSheet.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = EditProfilePictureBottomSheet.swift; sourceTree = "<group>"; };
		DF0AE0342BD76CA30098FDAF /* APILayer.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = APILayer.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		DF0AE0382BD76CAC0098FDAF /* Authentication.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = Authentication.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		DF0AE03B2BD76CB70098FDAF /* CUIModule.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = CUIModule.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		DF0AE03E2BD76CC30098FDAF /* Storage.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = Storage.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		DF1A112E2C04691100203086 /* SettingItemView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SettingItemView.swift; sourceTree = "<group>"; };
		DF273D052BD767A8003F9464 /* AppSetting.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = AppSetting.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		************************ /* AppSetting.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AppSetting.h; sourceTree = "<group>"; };
		E1F98707B337D84372B48A60 /* Pods-AppSetting.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-AppSetting.release.xcconfig"; path = "Target Support Files/Pods-AppSetting/Pods-AppSetting.release.xcconfig"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		************************ /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				DF0AE0392BD76CAC0098FDAF /* Authentication.framework in Frameworks */,
				0907F31B82984808E4DC441E /* Pods_AppSetting.framework in Frameworks */,
				DF0AE03F2BD76CC30098FDAF /* Storage.framework in Frameworks */,
				DF0AE03C2BD76CB70098FDAF /* CUIModule.framework in Frameworks */,
				DF0AE0352BD76CA30098FDAF /* APILayer.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		3CCF97BD953EF3FF70B7A806 /* Pods */ = {
			isa = PBXGroup;
			children = (
				A1EE4385BAB876608AAC3E83 /* Pods-AppSetting.debug.xcconfig */,
				E1F98707B337D84372B48A60 /* Pods-AppSetting.release.xcconfig */,
			);
			name = Pods;
			path = ../Pods;
			sourceTree = "<group>";
		};
		5A148FAA14D66577CF239605 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				DF0AE03E2BD76CC30098FDAF /* Storage.framework */,
				DF0AE03B2BD76CB70098FDAF /* CUIModule.framework */,
				DF0AE0382BD76CAC0098FDAF /* Authentication.framework */,
				DF0AE0342BD76CA30098FDAF /* APILayer.framework */,
				7C70AAE99B71D7A5BF5535FB /* Pods_AppSetting.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		83DA72D82D140DF700CA4BD7 /* BankAccount */ = {
			isa = PBXGroup;
			children = (
				83DA72D42D140DE700CA4BD7 /* BankAccountDetailViewController.swift */,
				83DA72D62D140DF200CA4BD7 /* BankAccountDetailViewModel.swift */,
				83DA72E32D147F5400CA4BD7 /* AddEditBankAccountViewController.swift */,
				83DA72E52D147F6000CA4BD7 /* AddEditBankAccountViewModel.swift */,
				83DA72EA2D148CF900CA4BD7 /* BankAccountModels.swift */,
				83DA72DB2D14355C00CA4BD7 /* Subviews */,
				83DA72EE2D15348D00CA4BD7 /* BottomSheets */,
			);
			path = BankAccount;
			sourceTree = "<group>";
		};
		83DA72DB2D14355C00CA4BD7 /* Subviews */ = {
			isa = PBXGroup;
			children = (
				83DA72DC2D14356C00CA4BD7 /* BankAccountDetailHeaderView.swift */,
				83DA72D92D14355500CA4BD7 /* BankAccountDetailCell.swift */,
			);
			path = Subviews;
			sourceTree = "<group>";
		};
		83DA72EE2D15348D00CA4BD7 /* BottomSheets */ = {
			isa = PBXGroup;
			children = (
				83DA72EC2D15348800CA4BD7 /* BankAccountActionBottomSheet.swift */,
			);
			path = BottomSheets;
			sourceTree = "<group>";
		};
		83F6C6532D0AF574001158C0 /* Subviews */ = {
			isa = PBXGroup;
			children = (
				83F6C6512D0AF56F001158C0 /* SettingItemCell.swift */,
			);
			path = Subviews;
			sourceTree = "<group>";
		};
		DF0AD8962BD769700098FDAF /* ChangePassword */ = {
			isa = PBXGroup;
			children = (
				DF0AD8982BD769700098FDAF /* ChangePasswordViewController.swift */,
				DF0AD8992BD769700098FDAF /* ChangePasswordViewModel.swift */,
				DF0AD8972BD769700098FDAF /* SetNewPasswordViewController.swift */,
			);
			path = ChangePassword;
			sourceTree = "<group>";
		};
		DF0AD89A2BD769700098FDAF /* Views */ = {
			isa = PBXGroup;
			children = (
				DF0AD89B2BD769700098FDAF /* CustomThemeHeaderView.swift */,
				DF0AD89C2BD769700098FDAF /* NotiSettingResetBottomSheet.swift */,
				DF0AD89D2BD769700098FDAF /* CustomThemeOption */,
				DF0AD8A02BD769700098FDAF /* CustomThemeView.swift */,
				DF0AD8A12BD769700098FDAF /* ProfileItemView.swift */,
				DF0AD8A22BD769700098FDAF /* ThemeList */,
				DF0AD8A62BD769700098FDAF /* ThemeStyleSelection */,
				DF0AD8AC2BD769700098FDAF /* ThemePreview */,
				DF1A112E2C04691100203086 /* SettingItemView.swift */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		DF0AD89D2BD769700098FDAF /* CustomThemeOption */ = {
			isa = PBXGroup;
			children = (
				DF0AD89E2BD769700098FDAF /* CustomThemeOptionButton.swift */,
				DF0AD89F2BD769700098FDAF /* CustomThemeOptionBottomSheet.swift */,
			);
			path = CustomThemeOption;
			sourceTree = "<group>";
		};
		DF0AD8A22BD769700098FDAF /* ThemeList */ = {
			isa = PBXGroup;
			children = (
				DF0AD8A32BD769700098FDAF /* ThemeListEmptyView.swift */,
				DF0AD8A42BD769700098FDAF /* ThemeListCell.swift */,
				DF0AD8A52BD769700098FDAF /* ThemeListHeaderView.swift */,
			);
			path = ThemeList;
			sourceTree = "<group>";
		};
		DF0AD8A62BD769700098FDAF /* ThemeStyleSelection */ = {
			isa = PBXGroup;
			children = (
				DF0AD8A72BD769700098FDAF /* ThemeColorItemView.swift */,
				DF0AD8A82BD769700098FDAF /* ThemeSmartColorView.swift */,
				DF0AD8A92BD769700098FDAF /* ThemePresetStyleSelectionView.swift */,
				DF0AD8AA2BD769700098FDAF /* ThemeCustomStyleSelectionView.swift */,
				DF0AD8AB2BD769700098FDAF /* ThemeColorSelectionView.swift */,
			);
			path = ThemeStyleSelection;
			sourceTree = "<group>";
		};
		DF0AD8AC2BD769700098FDAF /* ThemePreview */ = {
			isa = PBXGroup;
			children = (
				DF0AD8AD2BD769700098FDAF /* ThemeSegementPreview.swift */,
				DF0AD8AE2BD769700098FDAF /* ThemeTextPreview.swift */,
				DF0AD8AF2BD769700098FDAF /* ThemeButtonsPreview.swift */,
				DF0AD8B02BD769700098FDAF /* ThemeBadgePreview.swift */,
				DF0AD8B12BD769700098FDAF /* ThemePreview.swift */,
			);
			path = ThemePreview;
			sourceTree = "<group>";
		};
		DF0AD8B22BD769700098FDAF /* AppTheme */ = {
			isa = PBXGroup;
			children = (
				DF0AD8B32BD769700098FDAF /* AppThemeViewController.swift */,
				DF0AD8B42BD769700098FDAF /* NotificaitonSettingView.swift */,
				DF0AD8B52BD769700098FDAF /* AppThemeViewModel.swift */,
			);
			path = AppTheme;
			sourceTree = "<group>";
		};
		DF0AD8B62BD769700098FDAF /* Notification */ = {
			isa = PBXGroup;
			children = (
				DF0AD8B82BD769700098FDAF /* NotificationSettingViewController.swift */,
				DF0AD8B72BD769700098FDAF /* NotificationSettingViewModel.swift */,
			);
			path = Notification;
			sourceTree = "<group>";
		};
		DF0AD8BA2BD769700098FDAF /* Privacy */ = {
			isa = PBXGroup;
			children = (
				DF0AD8BB2BD769700098FDAF /* PrivacyViewController.swift */,
			);
			path = Privacy;
			sourceTree = "<group>";
		};
		DF0AD8BC2BD769700098FDAF /* Security */ = {
			isa = PBXGroup;
			children = (
				DF0AD8BD2BD769700098FDAF /* SecurityViewController.swift */,
				DF0AD8BF2BD769700098FDAF /* SecurityViewModel.swift */,
				DF0AD8BE2BD769700098FDAF /* SecurityItemView.swift */,
			);
			path = Security;
			sourceTree = "<group>";
		};
		DF0AD8C02BD769700098FDAF /* Terms */ = {
			isa = PBXGroup;
			children = (
				DF0AD8C12BD769700098FDAF /* TermsViewController.swift */,
			);
			path = Terms;
			sourceTree = "<group>";
		};
		DF0AD8C22BD769700098FDAF /* AppSetting */ = {
			isa = PBXGroup;
			children = (
				DF0AD8C52BD769700098FDAF /* AppSettingViewController.swift */,
				DF0AD8C32BD769700098FDAF /* AppSettingViewModel.swift */,
				83F6C6532D0AF574001158C0 /* Subviews */,
			);
			path = AppSetting;
			sourceTree = "<group>";
		};
		DF0AD8C62BD769700098FDAF /* CusomThemeCreate */ = {
			isa = PBXGroup;
			children = (
				DF0AD8C72BD769700098FDAF /* CustomThemeCreateViewController.swift */,
				DF0AD8C82BD769700098FDAF /* CustomThemeCreateViewModel.swift */,
				DF0AD8C92BD769700098FDAF /* CustomThemeName */,
				DF0AD8CC2BD769700098FDAF /* CustomThemeStyle */,
			);
			path = CusomThemeCreate;
			sourceTree = "<group>";
		};
		DF0AD8C92BD769700098FDAF /* CustomThemeName */ = {
			isa = PBXGroup;
			children = (
				DF0AD8CA2BD769700098FDAF /* CustomThemeNameViewController.swift */,
				DF0AD8CB2BD769700098FDAF /* CustomThemeNameViewModel.swift */,
			);
			path = CustomThemeName;
			sourceTree = "<group>";
		};
		DF0AD8CC2BD769700098FDAF /* CustomThemeStyle */ = {
			isa = PBXGroup;
			children = (
				DF0AD8CD2BD769700098FDAF /* CustomThemeStyleViewController.swift */,
				DF0AD8CE2BD769700098FDAF /* CustomThemeStyleViewModel.swift */,
			);
			path = CustomThemeStyle;
			sourceTree = "<group>";
		};
		DF0AD8CF2BD769700098FDAF /* CustomThemeTutorial */ = {
			isa = PBXGroup;
			children = (
				DF0AD8D02BD769700098FDAF /* CustomThemeTutorialViewModel.swift */,
				DF0AD8D12BD769700098FDAF /* CustomThemeTutorialViewController.swift */,
			);
			path = CustomThemeTutorial;
			sourceTree = "<group>";
		};
		DF0AD8D22BD769700098FDAF /* SuspendAccount */ = {
			isa = PBXGroup;
			children = (
				DF0AD8D32BD769700098FDAF /* SuspendAccountViewController.swift */,
				DF0AD8D42BD769700098FDAF /* SuspendAccountViewModel.swift */,
			);
			path = SuspendAccount;
			sourceTree = "<group>";
		};
		DF0AD8D52BD769700098FDAF /* SuspendAccountSuccessful */ = {
			isa = PBXGroup;
			children = (
				DF0AD8D62BD769700098FDAF /* SuspendAccountSuccessfulViewController.swift */,
				DF0AD8D72BD769700098FDAF /* SuspendAccountSuccessfulViewModel.swift */,
			);
			path = SuspendAccountSuccessful;
			sourceTree = "<group>";
		};
		DF0AD8D82BD769700098FDAF /* FAQs */ = {
			isa = PBXGroup;
			children = (
				DF0AD8D92BD769700098FDAF /* Subviews */,
				DF0AD8DC2BD769700098FDAF /* FAQDataModels.swift */,
				DF0AD8DD2BD769700098FDAF /* FAQViewModel.swift */,
				DF0AD8DE2BD769700098FDAF /* FAQViewController.swift */,
			);
			path = FAQs;
			sourceTree = "<group>";
		};
		DF0AD8D92BD769700098FDAF /* Subviews */ = {
			isa = PBXGroup;
			children = (
				DF0AD8DA2BD769700098FDAF /* FAQCategoryHeaderView.swift */,
				DF0AD8DB2BD769700098FDAF /* FAQCell.swift */,
			);
			path = Subviews;
			sourceTree = "<group>";
		};
		DF0AD8DF2BD769700098FDAF /* CustomThemeList */ = {
			isa = PBXGroup;
			children = (
				DF0AD8E02BD769700098FDAF /* CustomThemeListViewController.swift */,
				DF0AD8E12BD769700098FDAF /* CustomThemeListViewModel.swift */,
			);
			path = CustomThemeList;
			sourceTree = "<group>";
		};
		DF0AD8E22BD769700098FDAF /* Profile */ = {
			isa = PBXGroup;
			children = (
				DF0AD8E32BD769700098FDAF /* EditProfileViewController.swift */,
				DF0AD8E52BD769700098FDAF /* EditProfileViewModel.swift */,
				DF0AD8E42BD769700098FDAF /* OTPVerificationViewController.swift */,
				83DA72CE2D13E15800CA4BD7 /* OTPVerificationViewModel.swift */,
				83DA72D02D13F43A00CA4BD7 /* EmailUpdatedViewController.swift */,
				83DA72D22D14015900CA4BD7 /* EmailUpdatedViewModel.swift */,
				DF0AD8E62BD769700098FDAF /* BottomSheet */,
			);
			path = Profile;
			sourceTree = "<group>";
		};
		DF0AD8E62BD769700098FDAF /* BottomSheet */ = {
			isa = PBXGroup;
			children = (
				DF0AD8E72BD769700098FDAF /* ChangeProfileInfoBottomSheet.swift */,
				DF0AD8E92BD769700098FDAF /* ChangeProfileInfoViewModel.swift */,
				DF0AD8E82BD769700098FDAF /* NonTradingWarningBottomSheet.swift */,
				DF0AD8EA2BD769700098FDAF /* EditProfilePictureBottomSheet.swift */,
			);
			path = BottomSheet;
			sourceTree = "<group>";
		};
		DF273CFB2BD767A8003F9464 = {
			isa = PBXGroup;
			children = (
				DF273D072BD767A8003F9464 /* AppSetting */,
				DF273D062BD767A8003F9464 /* Products */,
				3CCF97BD953EF3FF70B7A806 /* Pods */,
				5A148FAA14D66577CF239605 /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		DF273D062BD767A8003F9464 /* Products */ = {
			isa = PBXGroup;
			children = (
				DF273D052BD767A8003F9464 /* AppSetting.framework */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		DF273D072BD767A8003F9464 /* AppSetting */ = {
			isa = PBXGroup;
			children = (
				DF0AD8B92BD769700098FDAF /* AppSettingCoordinator.swift */,
				DF0AD8C22BD769700098FDAF /* AppSetting */,
				DF0AD8B22BD769700098FDAF /* AppTheme */,
				DF0AD8962BD769700098FDAF /* ChangePassword */,
				DF0AD8C62BD769700098FDAF /* CusomThemeCreate */,
				DF0AD8DF2BD769700098FDAF /* CustomThemeList */,
				DF0AD8CF2BD769700098FDAF /* CustomThemeTutorial */,
				DF0AD8D82BD769700098FDAF /* FAQs */,
				DF0AD8B62BD769700098FDAF /* Notification */,
				DF0AD8BA2BD769700098FDAF /* Privacy */,
				DF0AD8E22BD769700098FDAF /* Profile */,
				DF0AD8BC2BD769700098FDAF /* Security */,
				83DA72D82D140DF700CA4BD7 /* BankAccount */,
				DF0AD8D22BD769700098FDAF /* SuspendAccount */,
				DF0AD8D52BD769700098FDAF /* SuspendAccountSuccessful */,
				DF0AD8C02BD769700098FDAF /* Terms */,
				DF0AD89A2BD769700098FDAF /* Views */,
				************************ /* AppSetting.h */,
			);
			path = AppSetting;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		************************ /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = **********;
			files = (
				************************ /* AppSetting.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		************************ /* AppSetting */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = ************************ /* Build configuration list for PBXNativeTarget "AppSetting" */;
			buildPhases = (
				16FD5F5DFF710916A664E574 /* [CP] Check Pods Manifest.lock */,
				************************ /* Headers */,
				************************ /* Sources */,
				************************ /* Frameworks */,
				DF273D032BD767A8003F9464 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = AppSetting;
			productName = AppSetting;
			productReference = DF273D052BD767A8003F9464 /* AppSetting.framework */;
			productType = "com.apple.product-type.framework";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		DF273CFC2BD767A8003F9464 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastUpgradeCheck = 1500;
				TargetAttributes = {
					************************ = {
						CreatedOnToolsVersion = 15.0;
						LastSwiftMigration = 1500;
					};
				};
			};
			buildConfigurationList = DF273CFF2BD767A8003F9464 /* Build configuration list for PBXProject "AppSetting" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = DF273CFB2BD767A8003F9464;
			productRefGroup = DF273D062BD767A8003F9464 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				************************ /* AppSetting */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		DF273D032BD767A8003F9464 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		16FD5F5DFF710916A664E574 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-AppSetting-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		************************ /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				DF0AD9112BD769700098FDAF /* CustomThemeNameViewController.swift in Sources */,
				DF1A112F2C04691100203086 /* SettingItemView.swift in Sources */,
				DF0AD9202BD769700098FDAF /* CustomThemeListViewController.swift in Sources */,
				DF0AD9092BD769700098FDAF /* SecurityItemView.swift in Sources */,
				DF0AD9002BD769700098FDAF /* ThemePreview.swift in Sources */,
				DF0AD9162BD769700098FDAF /* CustomThemeTutorialViewController.swift in Sources */,
				83DA72D72D140DF200CA4BD7 /* BankAccountDetailViewModel.swift in Sources */,
				DF0AD9272BD769700098FDAF /* ChangeProfileInfoViewModel.swift in Sources */,
				DF0AD9192BD769700098FDAF /* SuspendAccountSuccessfulViewController.swift in Sources */,
				DF0AD9062BD769700098FDAF /* AppSettingCoordinator.swift in Sources */,
				83DA72D52D140DE700CA4BD7 /* BankAccountDetailViewController.swift in Sources */,
				83DA72DA2D14355500CA4BD7 /* BankAccountDetailCell.swift in Sources */,
				DF0AD8FE2BD769700098FDAF /* ThemeButtonsPreview.swift in Sources */,
				83DA72EB2D148CF900CA4BD7 /* BankAccountModels.swift in Sources */,
				DF0AD8F42BD769700098FDAF /* ThemeListEmptyView.swift in Sources */,
				DF0AD9182BD769700098FDAF /* SuspendAccountViewModel.swift in Sources */,
				DF0AD9032BD769700098FDAF /* AppThemeViewModel.swift in Sources */,
				DF0AD8EE2BD769700098FDAF /* CustomThemeHeaderView.swift in Sources */,
				DF0AD9072BD769700098FDAF /* PrivacyViewController.swift in Sources */,
				DF0AD8FC2BD769700098FDAF /* ThemeSegementPreview.swift in Sources */,
				DF0AD91F2BD769700098FDAF /* FAQViewController.swift in Sources */,
				DF0AD9082BD769700098FDAF /* SecurityViewController.swift in Sources */,
				DF0AD8F22BD769700098FDAF /* CustomThemeView.swift in Sources */,
				83DA72DD2D14356C00CA4BD7 /* BankAccountDetailHeaderView.swift in Sources */,
				DF0AD8F02BD769700098FDAF /* CustomThemeOptionButton.swift in Sources */,
				83DA72E62D147F6000CA4BD7 /* AddEditBankAccountViewModel.swift in Sources */,
				DF0AD90F2BD769700098FDAF /* CustomThemeCreateViewController.swift in Sources */,
				DF0AD9042BD769700098FDAF /* NotificationSettingViewModel.swift in Sources */,
				DF0AD9052BD769700098FDAF /* NotificationSettingViewController.swift in Sources */,
				DF0AD8EB2BD769700098FDAF /* SetNewPasswordViewController.swift in Sources */,
				DF0AD9022BD769700098FDAF /* NotificaitonSettingView.swift in Sources */,
				83DA72E42D147F5400CA4BD7 /* AddEditBankAccountViewController.swift in Sources */,
				DF0AD8F72BD769700098FDAF /* ThemeColorItemView.swift in Sources */,
				DF0AD9122BD769700098FDAF /* CustomThemeNameViewModel.swift in Sources */,
				83DA72D32D14015900CA4BD7 /* EmailUpdatedViewModel.swift in Sources */,
				83DA72CF2D13E15800CA4BD7 /* OTPVerificationViewModel.swift in Sources */,
				DF0AD9132BD769700098FDAF /* CustomThemeStyleViewController.swift in Sources */,
				DF0AD90C2BD769700098FDAF /* AppSettingViewModel.swift in Sources */,
				DF0AD8FF2BD769700098FDAF /* ThemeBadgePreview.swift in Sources */,
				DF0AD8EF2BD769700098FDAF /* NotiSettingResetBottomSheet.swift in Sources */,
				DF0AD9282BD769700098FDAF /* EditProfilePictureBottomSheet.swift in Sources */,
				DF0AD9212BD769700098FDAF /* CustomThemeListViewModel.swift in Sources */,
				DF0AD8F12BD769700098FDAF /* CustomThemeOptionBottomSheet.swift in Sources */,
				DF0AD91A2BD769700098FDAF /* SuspendAccountSuccessfulViewModel.swift in Sources */,
				DF0AD8ED2BD769700098FDAF /* ChangePasswordViewModel.swift in Sources */,
				DF0AD9242BD769700098FDAF /* EditProfileViewModel.swift in Sources */,
				DF0AD8FD2BD769700098FDAF /* ThemeTextPreview.swift in Sources */,
				DF0AD90B2BD769700098FDAF /* TermsViewController.swift in Sources */,
				DF0AD9102BD769700098FDAF /* CustomThemeCreateViewModel.swift in Sources */,
				DF0AD90A2BD769700098FDAF /* SecurityViewModel.swift in Sources */,
				83DA72D12D13F43A00CA4BD7 /* EmailUpdatedViewController.swift in Sources */,
				DF0AD8F92BD769700098FDAF /* ThemePresetStyleSelectionView.swift in Sources */,
				83DA72ED2D15348800CA4BD7 /* BankAccountActionBottomSheet.swift in Sources */,
				DF0AD9152BD769700098FDAF /* CustomThemeTutorialViewModel.swift in Sources */,
				DF0AD91E2BD769700098FDAF /* FAQViewModel.swift in Sources */,
				DF0AD90E2BD769700098FDAF /* AppSettingViewController.swift in Sources */,
				DF0AD91B2BD769700098FDAF /* FAQCategoryHeaderView.swift in Sources */,
				DF0AD8F32BD769700098FDAF /* ProfileItemView.swift in Sources */,
				DF0AD8F82BD769700098FDAF /* ThemeSmartColorView.swift in Sources */,
				DF0AD9172BD769700098FDAF /* SuspendAccountViewController.swift in Sources */,
				DF0AD8EC2BD769700098FDAF /* ChangePasswordViewController.swift in Sources */,
				DF0AD8F52BD769700098FDAF /* ThemeListCell.swift in Sources */,
				DF0AD9222BD769700098FDAF /* EditProfileViewController.swift in Sources */,
				83F6C6522D0AF56F001158C0 /* SettingItemCell.swift in Sources */,
				DF0AD91C2BD769700098FDAF /* FAQCell.swift in Sources */,
				DF0AD8FA2BD769700098FDAF /* ThemeCustomStyleSelectionView.swift in Sources */,
				DF0AD9232BD769700098FDAF /* OTPVerificationViewController.swift in Sources */,
				DF0AD9262BD769700098FDAF /* NonTradingWarningBottomSheet.swift in Sources */,
				DF0AD9252BD769700098FDAF /* ChangeProfileInfoBottomSheet.swift in Sources */,
				DF0AD8FB2BD769700098FDAF /* ThemeColorSelectionView.swift in Sources */,
				DF0AD9142BD769700098FDAF /* CustomThemeStyleViewModel.swift in Sources */,
				DF0AD91D2BD769700098FDAF /* FAQDataModels.swift in Sources */,
				DF0AD8F62BD769700098FDAF /* ThemeListHeaderView.swift in Sources */,
				DF0AD9012BD769700098FDAF /* AppThemeViewController.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		DF273D0A2BD767A8003F9464 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		DF273D0B2BD767A8003F9464 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		DF273D0D2BD767A8003F9464 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = A1EE4385BAB876608AAC3E83 /* Pods-AppSetting.debug.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				MODULE_VERIFIER_SUPPORTED_LANGUAGES = "objective-c objective-c++";
				MODULE_VERIFIER_SUPPORTED_LANGUAGE_STANDARDS = "gnu17 gnu++20";
				PRODUCT_BUNDLE_IDENTIFIER = com.sirius.AppSetting;
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		DF273D0E2BD767A8003F9464 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = E1F98707B337D84372B48A60 /* Pods-AppSetting.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				MODULE_VERIFIER_SUPPORTED_LANGUAGES = "objective-c objective-c++";
				MODULE_VERIFIER_SUPPORTED_LANGUAGE_STANDARDS = "gnu17 gnu++20";
				PRODUCT_BUNDLE_IDENTIFIER = com.sirius.AppSetting;
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		DF273CFF2BD767A8003F9464 /* Build configuration list for PBXProject "AppSetting" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				DF273D0A2BD767A8003F9464 /* Debug */,
				DF273D0B2BD767A8003F9464 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		************************ /* Build configuration list for PBXNativeTarget "AppSetting" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				DF273D0D2BD767A8003F9464 /* Debug */,
				DF273D0E2BD767A8003F9464 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = DF273CFC2BD767A8003F9464 /* Project object */;
}
