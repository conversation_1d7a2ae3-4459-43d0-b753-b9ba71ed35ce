//
//  EditProfileViewModel.swift
//  AppSetting
//
//  Created by <PERSON> on 06/03/2024.
//

import Core
import RxSwift
import RxCocoa
import APILayer
import XCoordinator
import Storage
import SharedData

final class EditProfileViewModel: AnyViewModel {
    
    struct Input {
        let onViewAppear: Observable<Void>
        let onDoneUpdateUserInfo: Observable<OTPAuthType>
        let updateProfileImage: Observable<Data?>
        let downloadImage: Observable<String>
        let otpRequest: Observable<ProfileInfoModel>
    }
    
    struct Output {
        let onError: Driver<Error>
        let onShowLoading: Driver<Bool>
        let onDisplayUserInfo: Driver<GetUserInfoEndPoint.Response>
        let didUpdateProfileImage: Observable<Bool>
        let didDownloadImage: Observable<Data>
        let otpRequested: Driver<(model: ProfileInfoModel,
                                  response: OTPRequestEndPoint.Response)>
    }

    private let router: UnownedRouter<AppSettingsRoute>
    private let disposeBag = DisposeBag()
    
    init(router: UnownedRouter<AppSettingsRoute>) {
        self.router = router
    }
    
    func transform(input: Input) -> Output {
        let activityIndicator = ActivityIndicator()
        let errorTracker = ErrorTracker()
        
        input.onViewAppear
            .filter { LocalPreference.countryCodes?.isEmpty != false }
            .flatMap {
                EnumerationEndPoint.service.getCountryCodes()
                    .track(activityIndicator, error: errorTracker)
            }
            .subscribe(onNext: {})
            .disposed(by: disposeBag)
        
        let deleteProfileImage = input.updateProfileImage
            .filter { $0 == nil }
            .flatMap { [unowned self] _ in
                updateProfilePicture(delete: true)
                    .track(activityIndicator, error: errorTracker)
            }
            .map { true }
            .share()
        
        let uploadProfileImage = input.updateProfileImage.compactMap { $0 }
            .flatMap { [unowned self] in
                upload(file: $0)
                    .track(activityIndicator, error: errorTracker)
            }
            .map { $0.dosKey }
            .flatMap { [unowned self] in
                updateProfilePicture(dosKey: $0)
                    .track(activityIndicator, error: errorTracker)
            }
            .map { false }
            .share()
        
        let downloadProfilImage = input.downloadImage
            .flatMap { dosKey in
                FileDownloadEndPoint.service.download(fileKey: dosKey)
            }
        
        let getUserInfo = Observable.merge(input.onViewAppear,
                                           deleteProfileImage.mapToVoid(),
                                           uploadProfileImage.mapToVoid(),
                                           input.onDoneUpdateUserInfo.mapToVoid())
            .flatMap {
                GetUserInfoEndPoint.service.call()
                    .track(activityIndicator, error: errorTracker)
            }
        
        let sendOtpRequest = input.otpRequest
            .flatMap { [unowned self] model in
                requestOTP(for: model)
                    .track(activityIndicator, error: errorTracker)
                    .map { (model: model, response: $0) }
            }
        
        return Output(onError: errorTracker.asDriver(),
                      onShowLoading: activityIndicator.asDriver(),
                      onDisplayUserInfo: getUserInfo.asDriverOnErrorNever(),
                      didUpdateProfileImage: Observable.merge(deleteProfileImage,
                                                              uploadProfileImage),
                      didDownloadImage: downloadProfilImage,
                      otpRequested: sendOtpRequest.asDriverOnErrorNever())
    }
    
    func navigate(to route: AppSettingsRoute) {
        router.trigger(route)
    }
}

// MARK: - API
private extension EditProfileViewModel {
    
    func updateProfilePicture(delete: Bool = false, dosKey: String? = nil) -> Observable<Void> {
        let request = UpdateUserInfoEndPoint.Request(deletePicture: delete,
                                                     profilePicture: dosKey)
        
        return UpdateUserInfoEndPoint.service.request(parameters: request)
            .mapToVoid()
    }
    
    func upload(file: Data) -> Observable<UserFileUploadEndPoint.Response> {
        UserFileUploadEndPoint.service.upload(data: file)
    }
    
    func requestOTP(for profileModel: ProfileInfoModel) -> Observable<OTPRequestEndPoint.Response> {
        OTPRequestEndPoint.service.call(parameter: .init(bizType: BizType.updateUserInfo.rawValue,
                                                         otpType: profileModel.otpType.rawValue,
                                                         otpAddress: profileModel.address,
                                                         mobileRegion: profileModel.mobileRegion))
    }
}

public struct ProfileInfoModel {
    let otpType: OTPAuthType
    let address: String
    let value: String
    var mobileRegion: String?
}
