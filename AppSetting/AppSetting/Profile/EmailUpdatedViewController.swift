//
//  EmailUpdatedViewController.swift
//  AppSetting
//
//  Created by <PERSON> on 19/12/24.
//

import UIKit
import Core
import CUIModule
import XCoordinator
import APILayer

final class EmailUpdatedViewController: BaseViewController, VMView {
    
    // MARK: UI properties
    private lazy var containerView = {
        let view = UIView()
        view.addSubview(contentStackView)
        contentStackView.snp.makeConstraints { make in
            make.left.right.centerY.equalToSuperview()
        }
        
        return view
    }()
    
    private lazy var contentStackView = {
        let stackView = UIStackView(axis: .vertical,
                                    spacing: 16)
        stackView.addArrangedSubviews([logoView,
                                       titleLabel,
                                       descLabel1,
                                       descLabel2])
        stackView.setCustomSpacing(24, after: logoView)
        logoView.snp.makeConstraints { $0.width.height.equalTo(135) }
        
        return stackView
    }()
    
    private lazy var logoView = UIImageView(contentMode: .scaleAspectFill,
                                            image: .image(named: "merit_logo_otp_verified"))
    
    private lazy var titleLabel = UILabel(font: Font.semiBold.of(size: 18),
                                          text: "key0928".localized(),
                                          textColor: Color.txtTitle,
                                          textAlignment: .center)
    
    private lazy var descLabel1 = UILabel(font: Font.light.of(size: 14),
                                          numberOfLines: 0,
                                          text: "key0927".localized(),
                                          textColor: Color.txtParagraph)
    
    private lazy var descLabel2 = UILabel(font: Font.light.of(size: 14),
                                          numberOfLines: 0,
                                          text: "key0926".localized(),
                                          textColor: Color.txtParagraph)
    
    private lazy var loginButton = UIButton(type: .system,
                                            backgroundColor: Color.btn2nd,
                                            titleFont: Font.semiBold.of(size: 14),
                                            title: "key0248".localized(),
                                            normalTitleColor: Color.txtInverted,
                                            cornerRadius: 22)
    // MARK: Properties
    private let router: UnownedRouter<AppSettingsRoute>
    var viewModel: EmailUpdatedViewModel!
    
    // MARK: Life cycle
    init(router: UnownedRouter<AppSettingsRoute>) {
        self.router = router
        
        super.init(nibName: nil, bundle: nil)
        
        bind(viewModel: EmailUpdatedViewModel())
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        setupUI()
        bindViewModelLogic()
    }
    
    func setupUI() {
        view.addSubviews([containerView,
                          loginButton])
        
        loginButton.snp.makeConstraints { make in
            make.left.equalTo(12)
            make.right.equalTo(-12)
            make.bottom.equalTo(view.safeAreaLayoutGuide).offset(-16)
            make.height.equalTo(44)
        }
        
        containerView.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.left.equalTo(12)
            make.right.equalTo(-12)
            make.bottom.equalTo(loginButton.snp.top).offset(-12)
        }
    }

    func bindViewModelLogic() {
        let input = EmailUpdatedViewModel.Input(onLogout: loginButton.rx.tap.asObservable())
        
        let output = viewModel.transform(input: input)
        
        disposeBag.insert([
            // Loading
            output.onLoading
                .drive(onNext: { [unowned self] isLoading in
                    isLoading ? showLoading() : hiddenLoading()
                }),
            
            // Error
            output.onError
                .drive(onNext: { [unowned self] error in
                    showAlert(message: (error as? APIError)?.message)
                }),
        
            output.didLogout
                .subscribe(onNext: { [unowned self] in
                    router.trigger(.logout)
                })
        ])
    }
}
