//
//  EditProfileViewController.swift
//  AppSetting
//
//  Created by <PERSON> on 06/03/2024.
//

import UIKit
import Core
import CUIModule
import SharedData
import Photos
import PhotosUI
import Storage
import RxSwift
import APILayer
import Authentication

// swiftlint:disable type_body_length
final class EditProfileViewController: BaseViewController, VMView {
    
    // MARK: UI properties
    private lazy var headerView = {
        let displayModel = CUINavigationBar.DisplayModel(leftIcon: .image(named: "merit_ic_back"),
                                                         titleView: titleStackView)
        let view = CUINavigationBar(displayModel: displayModel)
        
        return view
    }()
    
    private lazy var titleStackView = {
        let stackView = UIStackView(alignment: .center,
                                    spacing: 4)
        stackView.addArrangedSubviews([titleImgView,
                                       titleLabel])
        
        return stackView
    }()
    
    private lazy var titleImgView = UIImageView(image: AppSetting.editProfile.icon)
    
    private lazy var titleLabel = UILabel(font: Font.semiBold.of(size: 14),
                                          text: AppSetting.editProfile.title,
                                          textColor: Color.txtTitle)
    
    // Content
    private lazy var contentStackView = {
        let stackView = UIStackView(axis: .vertical,
                                    spacing: 16)
        stackView.addArrangedSubviews([profilePictureContainer,
                                       yourNameTitleLabel,
                                       nameStackView,
                                       phoneNumberTF,
                                       emailAddressTF,
                                       CUIDivider(canvasHeight: 2)])
        
        stackView.setCustomSpacing(8, after: yourNameTitleLabel)
        
        return stackView
    }()
    
    // Profile picture
    private lazy var profilePictureContainer = {
        let view = UIView()
        view.addSubviews([profilePictureView,
                          cameraButton])
        profilePictureView.snp.makeConstraints { make in
            make.top.equalTo(8)
            make.bottom.equalTo(-8)
            make.centerX.equalToSuperview()
            make.width.height.equalTo(120)
        }
        
        cameraButton.snp.makeConstraints { make in
            make.bottom.right.equalTo(profilePictureView)
        }
        
        return view
    }()
    
    private lazy var profilePictureView = UIImageView(contentMode: .scaleAspectFill,
                                                      cornerRadius: 60,
                                                      image: LocalPreference.profilePicture ?? .image(named: "ic_default_avatar"))
    
    private lazy var cameraButton = UIButton(normalImage: .image(named: "ic_camera_circle"))
    
    // Name
    private lazy var yourNameTitleLabel = UILabel(font: Font.medium.of(size: 14),
                                                  text: "key0722".localized(),
                                                  textColor: Color.txtTitle)
    
    private lazy var nameStackView = {
        let stackView = UIStackView(alignment: .center,
                                    spacing: 4)
        stackView.addArrangedSubviews([nameLabel,
                                       UIView()])
        
        return stackView
    }()
    
    private lazy var nameLabel = UILabel(font: Font.light.of(size: 14),
                                         textColor: Color.txtTitle)
    
    fileprivate lazy var phoneNumberTF = {
        let displayModel = AuthInputTextField.DisplayModel
            .initialSetup(title: "key0018".localized(),
                          placeholder: "key0018".localized(),
                          tfColor: Color.bgDefaultTone,
                          editable: false)
        
        return AuthInputTextField(initialDisplay: displayModel)
    }()
        
    fileprivate lazy var emailAddressTF = {
        let displayModel = AuthInputTextField.DisplayModel
            .initialSetup(title: "key0020".localized(),
                          placeholder: "key0020".localized(),
                          tfColor: Color.bgDefaultTone,
                          editable: false)
        
        return AuthInputTextField(initialDisplay: displayModel)
    }()

    private lazy var imagePicker = {
        let picker = UIImagePickerController()
        picker.delegate = self
        picker.allowsEditing = true
        
        return picker
    }()
    
    // MARK: Properties
    var viewModel: EditProfileViewModel!
    private var userInfo: GetUserInfoEndPoint.Response?
//    private let onUpdateInfor = PublishSubject<EditProfileViewModel.ProfileInfoModel>()
    private let updateProfileImage = PublishSubject<Data?>()
    private let downloadImage = PublishSubject<String>()
    private let onOtpRequest = PublishSubject<ProfileInfoModel>()
    private let onDoneUpdateUserInfo = PublishSubject<OTPAuthType>()
    
    private var allPhotos = PHFetchResult<PHAsset>()
    
    
    // MARK: Life cycle
    override func viewDidLoad() {
        super.viewDidLoad()
        
        setupUI()
        bindActions()
        bindViewModelLogic()
    }

    func setupUI() {
        view.backgroundColor = Color.bgDefault
        
        view.addSubviews([headerView,
                          contentStackView])
        
        headerView.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide)
            make.left.right.equalToSuperview()
        }
        
        contentStackView.snp.makeConstraints { make in
            make.top.equalTo(headerView.snp.bottom).offset(16)
            make.left.equalTo(12)
            make.right.equalTo(-12)
        }
        
        // Set datas
        let name: String
        switch LocalizeManager.currentLanguage {
        case .english:
            name = Keychain.userInformation?.basic?.englishName ?? ""
        case .hongKong:
            name = Keychain.userInformation?.basic?.chineseName ?? ""
        }
        nameLabel.text = name

        let phoneNumber = Keychain.userInformation?.basic?.base?.phoneNumber ?? ""
        let emailAddress = Keychain.userInformation?.basic?.base?.email ?? ""
        phoneNumberTF.updateUI(with: AuthInputTextField.DisplayModel.setValue(value: phoneNumber))
        emailAddressTF.updateUI(with: AuthInputTextField.DisplayModel.setValue(value: emailAddress))
    }
    
    func bindActions() {
        // Back
        headerView.rx.leftTap
            .subscribe(onNext: { [unowned self] in
                self.backPress()
            })
            .disposed(by: disposeBag)
        
        // Edit profile picture
        cameraButton.rx.tap
            .subscribe(onNext: { [unowned self] in
                let bottomSheet = EditProfilePictureBottomSheet()
                bottomSheet.rx.selectOption
                    .subscribe(onNext: { [unowned self] option in
                        switch option {
                        case .openCamera:
                            self.imagePicker.sourceType = .camera
                            self.imagePicker.cameraDevice = .front
                            
                            self.present(self.imagePicker, animated: true)
                            
                        case .selectPhoto:
                            if #available(iOS 14.0, *) {
                                var phPickerConfig = PHPickerConfiguration(photoLibrary: .shared())
                                phPickerConfig.selectionLimit = 1
                                phPickerConfig.filter = .images
                                
                                let picker = PHPickerViewController(configuration: phPickerConfig)
                                picker.delegate = self
                                
                                self.present(picker, animated: true)
                            } else {
                                // Fallback on earlier versions
                                self.imagePicker.sourceType = .photoLibrary
                                
                                self.present(self.imagePicker, animated: true)
                            }
                            
                        case .delete:
                            updateProfileImage.onNext(nil)
                        }
                    })
                    .disposed(by: self.disposeBag)
                bottomSheet.present()
            })
            .disposed(by: disposeBag)
        
        // Edit phone number
        phoneNumberTF.rx.tapOnTextField
            .subscribe(onNext: { [unowned self] in
                guard let phoneNumber = userInfo?.mobile else { return }
                
                let countryCode = LocalPreference.countryCodes?.first(where: { $0.code == userInfo?.mobileRegion })
                let bottomSheet = ChangeProfileInfoBottomSheet(infoType: .phoneNumber,
                                                               currentInfo: phoneNumber,
                                                               countryCode: countryCode)
                
                bottomSheet.rx.changeInfo
                    .subscribe(onNext: { [unowned self] newPhoneNumber, countryCode in
                        onOtpRequest.onNext(ProfileInfoModel(otpType: .mobile,
                                                             address: newPhoneNumber,
                                                             value: newPhoneNumber,
                                                             mobileRegion: countryCode))
                    })
                    .disposed(by: disposeBag)
                
                bottomSheet.present()
            })
            .disposed(by: disposeBag)
        
        // Edit email address
        emailAddressTF.rx.tapOnTextField
            .withLatestFrom(emailAddressTF.rx.text)
            .subscribe(onNext: { [unowned self] emailAddress in
                let bottomSheet = ChangeProfileInfoBottomSheet(infoType: .emailAddress,
                                                               currentInfo: emailAddress)
                bottomSheet.rx.changeInfo
                    .subscribe(onNext: { [unowned self] newEmail, _ in
                        onOtpRequest.onNext(ProfileInfoModel(otpType: .email,
                                                             address: newEmail,
                                                             value: newEmail))
                    })
                    .disposed(by: disposeBag)
                
                bottomSheet.present()
            })
            .disposed(by: disposeBag)
    }
    
    func bindViewModelLogic() {
        let input = EditProfileViewModel.Input(onViewAppear: rx.viewWillAppear.take(1).mapToVoid(),
                                               onDoneUpdateUserInfo: onDoneUpdateUserInfo.asObservable(),
                                               updateProfileImage: updateProfileImage.asObservable(),
                                               downloadImage: downloadImage.asObservable(), 
                                               otpRequest: onOtpRequest.asObservable())
        let output = viewModel.transform(input: input)
        
        output.onShowLoading
            .drive(onNext: { [unowned self] show in
                show ? showLoading() : hiddenLoading()
            })
            .disposed(by: disposeBag)
        
        output.onError
            .drive(onNext: { [unowned self] error in
                var errorMessage = error.localizedDescription
                if let apiError = error as? APIError {
                    errorMessage = apiError.message
                }
                showAlert(message: errorMessage)
            })
            .disposed(by: disposeBag)
        
        output.onDisplayUserInfo
            .drive(onNext: { [unowned self] userInfo in
                self.userInfo = userInfo
                nameLabel.text = userInfo.fullName
                
                let mobileRegion = userInfo.mobileRegion ?? ""
                let mobileNumber = mobileRegion.isEmpty ? (userInfo.mobile ?? "") : "(\(mobileRegion)) " + (userInfo.mobile ?? "")
                phoneNumberTF.updateUI(with: AuthInputTextField.DisplayModel.setValue(value: mobileNumber))
                emailAddressTF.updateUI(with: AuthInputTextField.DisplayModel.setValue(value: userInfo.email ?? ""))
                
                if let profilePicture = userInfo.profilePicture {
                    downloadImage.onNext(profilePicture)
                }
            }).disposed(by: disposeBag)
        
        // Profile image
        output.didDownloadImage
            .subscribe(onNext: { [unowned self] data in
                if let image = UIImage(data: data) {
                    profilePictureView.image = image
                    LocalPreference.profilePicture = image
                }
            }).disposed(by: disposeBag)
        
        // OTP requested
        output.otpRequested
            .drive(onNext: { [unowned self] model, response in
                viewModel.navigate(to: .otpVerification(profileModel: model,
                                                        otpResponse: response,
                                                        onDone: { [unowned self] otpType in
                    switch otpType {
                    case .mobile:
                        onDoneUpdateUserInfo.onNext(otpType)
                        CUIToastView.show(data: .init(title: "key0783".localized(),
                                                      desc: "key0785".localized()),
                                          inView: view,
                                          underView: headerView)
                        
                    case .email:
                        viewModel.navigate(to: .didChangeEmail)
                    }
                }))
            }).disposed(by: disposeBag)
        
        // Di update profile picture
        output.didUpdateProfileImage
            .subscribe(onNext: { [unowned self] isDeleted in
                let desc: String
                if isDeleted {
                    profilePictureView.image = .image(named: "ic_default_avatar")
                    desc = "key0923".localized()
                    LocalPreference.profilePicture = nil
                } else {
                    desc = "key0924".localized()
                }
                
                CUIToastView.show(data: .init(title: "key0783".localized(),
                                              desc: desc),
                                  inView: view,
                                  underView: headerView)
            }).disposed(by: disposeBag)
    }
}

// MARK: - UIImagePickerControllerDelegate
extension EditProfileViewController: UIImagePickerControllerDelegate, UINavigationControllerDelegate {
    
    func imagePickerController(_ picker: UIImagePickerController,
                               didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey: Any]) {
        if let image = info[UIImagePickerController.InfoKey.originalImage] as? UIImage {
            profilePictureView.image = image
            
            if let imageData = image.compressToData(5, stepSize: 0.1) {
                updateProfileImage.onNext(imageData)
            }
        }
        
        dismiss(animated: true)
    }
    
    func imagePickerControllerDidCancel(_ picker: UIImagePickerController) {
        dismiss(animated: true)
    }
}

// MARK: - PHPickerViewControllerDelegate
@available(iOS 14.0, *)
extension EditProfileViewController: PHPickerViewControllerDelegate {
    
    func picker(_ picker: PHPickerViewController, didFinishPicking results: [PHPickerResult]) {
        dismiss(animated: true)
        
        guard let result = results.first else { return }
        let itemProvider = result.itemProvider
        
        if itemProvider.canLoadObject(ofClass: UIImage.self) {
            itemProvider.loadObject(ofClass: UIImage.self) { [unowned self] image, _ in
                if let image = image as? UIImage {
                    if let imageData = image.compressToData(5, stepSize: 0.1) {
                        updateProfileImage.onNext(imageData)
                    }
                }
            }
        }
    }
}
// swiftlint:enable type_body_length

// MARK: - Extension
extension OTPAuthType {
    
    var sourceType: OTPInputViewController.SourceType {
        switch self {
        case .mobile:
            return .phone
        case .email:
            return .email
        }
    }
}
