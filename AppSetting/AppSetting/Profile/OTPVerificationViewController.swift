//
//  OTPVerificationViewController.swift
//  AppSetting
//
//  Created by <PERSON> on 12/03/2024.
//

import UIKit
import Core
import CUIModule
import Authentication
import APILayer
import SharedData

final class OTPVerificationViewController: BaseViewController, VMView {
    
    // MARK: UI properties
    private lazy var headerView = {
        let displayModel = CUINavigationBar.DisplayModel(leftIcon: .image(named: "merit_ic_back"),
                                                         titleView: titleLabel)
        let view = CUINavigationBar(displayModel: displayModel)
        
        return view
    }()
    
    private lazy var titleLabel = UILabel(font: Font.semiBold.of(size: 14),
                                          text: "key0925".localized(),
                                          textColor: Color.txtTitle,
                                          textAlignment: .center)
    
    private lazy var container = UIView()
    
    private lazy var otpInputViewController = OTPInputViewController(sourceType: sourceType,
                                                                     parentView: view,
                                                                     navigationView: headerView)
    // MARK: Properties
    var viewModel: OTPVerificationViewModel!
    private let sourceType: OTPInputViewController.SourceType
    private let onDone: ((OTPAuthType) -> Void)?
    
    // MARK: Life cycle
    init(sourceType: OTPInputViewController.SourceType,
         onDone: ((OTPAuthType) -> Void)?) {
        self.sourceType = sourceType
        self.onDone = onDone
        
        super.init(nibName: nil, bundle: nil)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        setupUI()
        bindActions()
        bindViewModelLogic()
    }
    
    func setupUI() {
        view.backgroundColor = Color.bgDefault
        
        view.addSubviews([headerView,
                          container])
        
        headerView.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide)
            make.left.right.equalToSuperview()
        }
        container.snp.makeConstraints { make in
            make.top.equalTo(headerView.snp.bottom).offset(24)
            make.left.right.bottom.equalToSuperview()
        }
        
        addChild(controller: otpInputViewController, container: container)
    }
    
    func bindActions() {
        // Back
        headerView.rx.leftTap
            .subscribe(onNext: { [unowned self] in
                backPress()
            })
            .disposed(by: disposeBag)
    }
    
    func bindViewModelLogic() {
        let input = OTPVerificationViewModel.Input(onViewAppear: rx.viewWillAppear.take(1).mapToVoid(),
                                                   resendOtp: otpInputViewController.rx.resendOTP,
                                                   submitOtp: otpInputViewController.rx.submitOtp)
        
        let output = viewModel.transform(input: input)
        
        disposeBag.insert([
            // Loading
            output.onShowLoading
                .drive(onNext: { [unowned self] isLoading in
                    isLoading ? showLoading() : hiddenLoading()
                }),
            
            // Error
            output.onError
                .drive(onNext: { [unowned self] error in
                    var errorMessage = error.localizedDescription
                    if let apiError = error as? APIError {
                        errorMessage = apiError.message
                    }
                    CUIToastView.show(data: .init(title: "key0895".localized(),
                                                  desc: errorMessage,
                                                  titleStyle: .init(textColor: Color.txtNegative)),
                                      inView: view,
                                      underView: headerView)
                }),
            
            output.didRequestOtp
                .drive(onNext: { [unowned self] address, mobileRegion, response in
                    otpInputViewController.otpRequested.onNext((address: address,
                                                                mobileRegion: mobileRegion,
                                                                response: response))
                }),
            
            // Did update user info
            output.didUpdateUserInfo
                .drive(onNext: { [unowned self] in
                    onDone?(viewModel.profileModel.otpType)
                    if viewModel.profileModel.otpType == .mobile {
                        backPress()
                    }
                })
        ])
    }
}
