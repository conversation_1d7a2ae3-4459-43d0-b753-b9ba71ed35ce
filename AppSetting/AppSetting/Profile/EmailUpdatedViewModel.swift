//
//  EmailUpdatedViewModel.swift
//  AppSetting
//
//  Created by <PERSON> on 19/12/24.
//

import Core
import APILayer
import RxSwift
import RxCocoa

final class EmailUpdatedViewModel: AnyViewModel {
    
    struct Input {
        let onLogout: Observable<Void>
    }
    
    struct Output {
        let onLoading: Driver<Bool>
        let onError: Driver<Error>
        let didLogout: Observable<Void>
    }
    
    func transform(input: Input) -> Output {
        let activityIndicator = ActivityIndicator()
        let errorTracker = ErrorTracker()
        
        let logout = input.onLogout
            .flatMap {
                UserLogoutEndPoint.service.call()
                    .track(activityIndicator, error: errorTracker)
            }
            .mapToVoid()
        
        return Output(onLoading: activityIndicator.asDriver(),
                      onError: errorTracker.asDriver(),
                      didLogout: logout)
    }
}
