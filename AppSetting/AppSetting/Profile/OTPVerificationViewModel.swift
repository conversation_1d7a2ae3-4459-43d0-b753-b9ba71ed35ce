//
//  OTPVerificationViewModel.swift
//  AppSetting
//
//  Created by <PERSON> on 19/12/24.
//

import Core
import RxSwift
import RxCocoa
import APILayer
import SharedData
import Storage

final class OTPVerificationViewModel: AnyViewModel {
    
    struct Input {
        let onViewAppear: Observable<Void>
        let resendOtp: Observable<Void>
        let submitOtp: Observable<String>
    }
    
    struct Output {
        let onError: Driver<Error>
        let onShowLoading: Driver<Bool>
        let didRequestOtp: Driver<(address: String,
                                   mobileRegion: String?,
                                   response: OTPRequestEndPoint.Response)>
        let didUpdateUserInfo: Driver<Void>
    }
    
    // MARK: Properties
    let profileModel: ProfileInfoModel
    private(set) var otpResponse: OTPRequestEndPoint.Response
    
    // MARK: Life cycle
    init(profileModel: ProfileInfoModel,
         otpResponse: OTPRequestEndPoint.Response) {
        self.profileModel = profileModel
        self.otpResponse = otpResponse
    }
    
    func transform(input: Input) -> Output {
        let activityIndicator = ActivityIndicator()
        let errorTracker = ErrorTracker()
        
        let resendOtp = input.resendOtp
            .flatMap { [unowned self] in
                requestOTP(for: profileModel)
                    .track(activityIndicator, error: errorTracker)
            }
            .onNext { [unowned self] in
                otpResponse = $0
            }
        
        let updateUserInfo = input.submitOtp
            .flatMap { [unowned self] otp in
                self.updateUserInfo(profileModel: profileModel,
                                    otp: otp,
                                    refCode: otpResponse.refCode)
                .track(activityIndicator, error: errorTracker)
            }
        
        let otpRequested = Observable.merge(input.onViewAppear,
                                            resendOtp.mapToVoid())
            .map { [unowned self] in 
                (address: profileModel.address,
                 mobileRegion: profileModel.mobileRegion,
                 response: otpResponse)
            }
        
        return Output(onError: errorTracker.asDriver(),
                      onShowLoading: activityIndicator.asDriver(),
                      didRequestOtp: otpRequested.asDriverOnErrorNever(),
                      didUpdateUserInfo: updateUserInfo.asDriverOnErrorNever())
    }
}

// MARK: - API
private extension OTPVerificationViewModel {
    
    func requestOTP(for profileModel: ProfileInfoModel) -> Observable<OTPRequestEndPoint.Response> {
        OTPRequestEndPoint.service.call(parameter: .init(bizType: BizType.updateUserInfo.rawValue,
                                                         otpType: profileModel.otpType.rawValue,
                                                         otpAddress: profileModel.address,
                                                         mobileRegion: profileModel.mobileRegion))
    }
    
    func updateUserInfo(profileModel: ProfileInfoModel, otp: String, refCode: String?) -> Observable<Void> {
        let request = UpdateUserInfoEndPoint.Request(email: profileModel.otpType == .email ? profileModel.value : nil,
                                                     mobile: profileModel.otpType == .mobile ? profileModel.value : nil,
                                                     mobileRegion: profileModel.otpType == .mobile ? profileModel.mobileRegion : nil,
                                                     otp: otp,
                                                     otpType: profileModel.otpType.rawValue,
                                                     refCode: refCode)
        
        return UpdateUserInfoEndPoint.service.request(parameters: request)
            .mapToVoid()
    }
}
