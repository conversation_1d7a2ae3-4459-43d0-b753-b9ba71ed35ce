//
//  EditProfilePictureBottomSheet.swift
//  AppSetting
//
//  Created by <PERSON> on 06/03/2024.
//

import UIKit
import Core
import CUIModule
import RxSwift

final class EditProfilePictureBottomSheet: UIViewController, AnyView, BaseBottomSheet {
    
    // MARK: Properties
    var disposeBagForDismiss = DisposeBag()
    var onDismiss = PublishSubject<(() -> Void)?>()
    
    private var buttons: [UIButton] = []
    private var selectedOption: EditProfilePictureOption?
    fileprivate let onSelectOption = PublishSubject<EditProfilePictureOption>()
    
    // MARK: UI properties
    private lazy var titleStackView = {
        let stackView = UIStackView(alignment: .center,
                                    spacing: 4)
        stackView.addArrangedSubviews([cameraIconView,
                                       titleLabel,
                                       UIView()])
        
        return stackView
    }()
    
    private lazy var cameraIconView = UIImageView(image: .image(named: "ic_camera_line"))
    
    private lazy var titleLabel = {
        let label = UILabel(font: Font.regular.of(size: 14),
                            text: "\("key0102".localized()) \("key0760".localized())",
                            textColor: Color.txtParagraph)
        label.highlight("key0102".localized(),
                        highlightFont: Font.semiBold.of(size: 14),
                        highlightColor: Color.txtTitle)
        
        return label
    }()
    
    private lazy var closeButton = UIButton(normalImage: .image(named: "merit_ic_cross"))
    
    private lazy var optionsStackView = UIStackView(axis: .vertical,
                                                    spacing: 8)
    
    // MARK: Life cycle
    override func viewDidLoad() {
        super.viewDidLoad()
        
        setupUI()
        bindActions()
    }
    
    func setupUI() {
        view.backgroundColor = Color.bgDefault
        
        view.addSubviews([titleStackView,
                          closeButton,
                          optionsStackView])
        
        closeButton.snp.makeConstraints { make in
            make.top.equalTo(6)
            make.right.equalTo(-6)
            make.width.height.equalTo(44)
        }
        
        titleStackView.snp.makeConstraints { make in
            make.left.equalTo(16)
            make.right.equalTo(closeButton.snp.left)
            make.centerY.equalTo(closeButton)
        }
        
        optionsStackView.snp.makeConstraints { make in
            make.top.equalTo(closeButton.snp.bottom).offset(6)
            make.left.equalTo(16)
            make.bottom.equalTo(view.safeAreaLayoutGuide).offset(-16)
            make.right.equalTo(-16)
        }
        
        EditProfilePictureOption.allCases.forEach { option in
            let button = button(for: option)
            optionsStackView.addArrangedSubview(button)
            button.snp.makeConstraints { $0.height.equalTo(44).priority(.high) }
            buttons.append(button)
        }
    }
    
    func bindActions() {
        closeButton.rx.tap
            .map { nil }
            .bind(to: onDismiss)
            .disposed(by: disposeBagForDismiss)
        
        buttons.forEach { button in
            button.rx.tap
                .subscribe(onNext: { [unowned self] in
                    if let option = EditProfilePictureOption(rawValue: button.tag) {
                        selectedOption = option
                        onDismiss.onNext(selectOption)
                    }
                })
                .disposed(by: disposeBagForDismiss)
        }
    }
    
    private func selectOption() {
        guard let selectedOption = self.selectedOption else { return }
        
        onSelectOption.onNext(selectedOption)
    }
}

// MARK: - Private
private extension EditProfilePictureBottomSheet {
    
    func button(for option: EditProfilePictureOption) -> UIButton {
        UIButton(titleFont: Font.medium.of(size: 14),
                 title: option.title,
                 normalTitleColor: Color.txtTitle,
                 normalImage: option.icon,
                 contentHorizontalAlignment: .leading,
                 contentEdgeInsets: UIEdgeInsets(left: 4, right: 4),
                 titleInsets: UIEdgeInsets(left: 6, right: -6),
                 tag: option.rawValue)
    }
}

// MARK: - extension Reactive
extension Reactive where Base: EditProfilePictureBottomSheet {
    
    var selectOption: Observable<EditProfilePictureOption> {
        base.onSelectOption.asObservable()
    }
}

// MARK: - EditProfilePictureOption
enum EditProfilePictureOption: Int, CaseIterable {
    case openCamera = 1
    case selectPhoto
    case delete
    
    var icon: UIImage {
        switch self {
        case .openCamera:
            return .image(named: "merit_ic_camera")
        case .selectPhoto:
            return .image(named: "ic_image")
        case .delete:
            return .image(named: "merit_ic_trash_med")
        }
    }
    
    var title: String {
        switch self {
        case .openCamera:
            return "key0761".localized()
        case .selectPhoto:
            return "key0762".localized()
        case .delete:
            return "key0763".localized()
        }
    }
}
