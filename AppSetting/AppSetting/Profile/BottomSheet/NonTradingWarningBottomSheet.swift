//
//  NonTradingWarningBottomSheet.swift
//  AppSetting
//
//  Created by <PERSON> on 06/03/2024.
//

import UIKit
import Core
import CUIModule
import RxSwift

final class NonTradingWarningBottomSheet: UIViewController, AnyView, BaseBottomSheet {
    
    // MARK: Properties
    var disposeBagForDismiss = DisposeBag()
    var onDismiss = PublishSubject<(() -> Void)?>()
        
    // MARK: UI properties
    private lazy var closeButton = UIButton(normalImage: .image(named: "iconcrosssquare"))
    
    private lazy var contentStackView = {
        let stackView = UIStackView(axis: .vertical,
                                    spacing: 16)
        stackView.addArrangedSubviews([descLabel1,
                                       descLabel2,
                                       createAccountButton])
        createAccountButton.snp.makeConstraints { $0.height.equalTo(44).priority(.high) }
        
        return stackView
    }()
    
    private lazy var descLabel1 = UILabel(font: Font.medium.of(size: 12),
                                          numberOfLines: 0,
                                          // swiftlint:disable:next line_length
                                          text: "Your account is currently non-trading account. In order to retake a suit test, you need to open a trading account first. It would take a few minutes to complete the submission!",
                                          textColor: Color.txtParagraph)   
    
    private lazy var descLabel2 = UILabel(font: Font.medium.of(size: 12),
                                          numberOfLines: 0,
                                          // swiftlint: disable line_length
                                          text: "Would you like to open trading account now?",
                                          // swiftlint: enable line_length
                                          textColor: Color.txtParagraph)
    
    fileprivate lazy var createAccountButton = ThemeableButton(
        type: .system,
        titleFont: Font.bold.of(size: 16),
        title: "CREATE TRADING ACCOUNT",
        normalTitleColor: Color.pitchBlack,
        disabledTitleColor: Color.txtDisabled,
        normalBgColor: Color.bgButtonDefault,
        cornerRadius: 22,
        themeType: .primary
    )

    // MARK: Life cycle
    override func viewDidLoad() {
        super.viewDidLoad()
        
        setupUI()
        bindActions()
    }
    
    func setupUI() {
        view.backgroundColor = Color.bgDefault
        
        view.addSubviews([closeButton,
                          contentStackView])
        
        closeButton.snp.makeConstraints { make in
            make.top.equalTo(6)
            make.right.equalTo(-6)
            make.width.height.equalTo(44)
        }
        
        contentStackView.snp.makeConstraints { make in
            make.top.equalTo(closeButton.snp.bottom).offset(6)
            make.left.equalTo(16)
            make.bottom.equalTo(view.safeAreaLayoutGuide).offset(-16)
            make.right.equalTo(-16)
        }
        
        descLabel1.setLineSpacing(lineHeightMultiple: 1.23)
        descLabel2.setLineSpacing(lineHeightMultiple: 1.23)
    }
    
    func bindActions() {
        closeButton.rx.tap
            .map { nil }
            .bind(to: onDismiss)
            .disposed(by: disposeBagForDismiss)
    }
}
// MARK: - Reactive
extension Reactive where Base: NonTradingWarningBottomSheet {

    var createAccount: Observable<Void> {
        base.createAccountButton.rx.tap.mapToVoid()
    }
}
