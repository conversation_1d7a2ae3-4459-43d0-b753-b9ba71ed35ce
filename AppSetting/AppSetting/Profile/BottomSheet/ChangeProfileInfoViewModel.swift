//
//  ChangeProfileInfoViewModel.swift
//  AppSetting
//
//  Created by <PERSON> on 12/03/2024.
//

import Core
import APILayer
import RxSwift

final class ChangeProfileInfoViewModel: AnyViewModel {
    
    struct Input {
        let verifyInput: Observable<String>
    }
    
    struct Output {
        let onVerifiedInput: Observable<String>
        let onDuplicatedInput: Observable<Void>
    }
    
    private let infoType: ChangeProfileInfoType
    
    init(infoType: ChangeProfileInfoType) {
        self.infoType = infoType
    }
    
    func transform(input: Input) -> Output {
        let errorTracker = ErrorTracker()
        
        let verifyInput = input.verifyInput
            .flatMap { [unowned self] input in
                verifyDuplication(for: input)
                    .trackError(errorTracker)
            }
        
        return Output(onVerifiedInput: verifyInput,
                      onDuplicatedInput: errorTracker.asObservable().mapToVoid())
    }
}

// MARK: - API
private extension ChangeProfileInfoViewModel {
    
    func verifyDuplication(for input: String) -> Observable<String> {
        let email = infoType == .emailAddress ? input : nil
        let phoneNumber = infoType == .phoneNumber ? input : nil
        
        let request = CheckUserInfoEndPoint.Request(email: email,
                                                    mobile: phoneNumber)
        
        return CheckUserInfoEndPoint.service.request(parameters: request)
            .map { _ in input }
    }
}
