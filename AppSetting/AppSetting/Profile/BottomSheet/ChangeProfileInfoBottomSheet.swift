//
//  ChangeProfileInfoBottomSheet.swift
//  AppSetting
//
//  Created by <PERSON> on 06/03/2024.
//

import UIKit
import Core
import CUIModule
import RxSwift
import Storage

final class ChangeProfileInfoBottomSheet: UIViewController, AnyView, BaseBottomSheet {
    
    // MARK: Properties
    private let viewModel: ChangeProfileInfoViewModel
    var disposeBagForDismiss = DisposeBag()
    var onDismiss = PublishSubject<(() -> Void)?>()
    var onUpdateHeight = PublishSubject<Void>()
    
    private let infoType: ChangeProfileInfoType
    private let currentInfo: String
    private let countryCode: CountryCode?
    
    private var savedInput: String = ""
    private var mobileRegion: String?
    fileprivate let onChangeInfo = PublishSubject<(newInfo: String, mobileRegion: String?)>()
    
    // MARK: UI properties
    private lazy var titleLabel = {
        let label = UILabel(font: Font.regular.of(size: 14),
                            text: "key0102".localized() + " " + infoType.title,
                            textColor: Color.txtParagraph)
        label.highlight("key0102".localized(),
                        highlightFont: Font.semiBold.of(size: 14),
                        highlightColor: Color.txtTitle)
        
        return label
    }()
    
    private lazy var closeButton = UIButton(normalImage: .image(named: "merit_ic_cross"))
    
    private lazy var tfContainer = {
        let stackView = UIStackView(axis: .vertical)
        stackView.addArrangedSubviews([textField,
                                       mobileInputView])
        
        mobileInputView.isHidden = infoType != .phoneNumber
        textField.isHidden = infoType != .emailAddress
        
        return stackView
    }()
    
    private lazy var textField = {
        let initialDisplay = AuthInputTextField.DisplayModel
            .initialSetup(title: infoType.textFieldTitle,
                          titleColor: Color.txtLabel,
                          placeholder: infoType.textFieldPlaceholder,
                          tfColor: Color.bgDefaultTone,
                          keyboardType: infoType.keyboardType)
        
        return AuthInputTextField(initialDisplay: initialDisplay)
    }()
    
    private lazy var mobileInputView = {
        CountryCodeMobileInputView(displayModel: .init(titleAttribute: .init(font: Font.medium.of(size: 14),
                                                                             text: infoType.textFieldTitle,
                                                                             textColor: Color.txtLabel),
                                                       placeholder: infoType.textFieldPlaceholder))
    }()
    
    fileprivate lazy var saveButton = ThemeableButton(type: .system,
                                                      titleFont: Font.medium.of(size: 14),
                                                      title: "key0724".localized(),
                                                      normalTitleColor: Color.txtInverted,
                                                      disabledTitleColor: Color.txtDisabled,
                                                      normalBgColor: Color.btn2nd,
                                                      disabledBgColor: Color.btnDisabled,
                                                      cornerRadius: 22)
    
    // MARK: Life cycle
    init(infoType: ChangeProfileInfoType, currentInfo: String, countryCode: CountryCode? = nil) {
        self.infoType = infoType
        self.currentInfo = currentInfo
        self.countryCode = countryCode
        mobileRegion = countryCode?.code
        
        viewModel = ChangeProfileInfoViewModel(infoType: infoType)
        
        super.init(nibName: nil, bundle: nil)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        setupUI()
        bindActions()
        bindViewModelLogic()
        
        // Set initial data
        switch infoType {
        case .phoneNumber:
            mobileInputView.set(phoneNumber: currentInfo)
            
            if let countryCode = countryCode {
                mobileInputView.set(selectedCountryCode: countryCode)
            }
            
        case .emailAddress:
            textField.updateUI(with: AuthInputTextField.DisplayModel.setValue(value: currentInfo))
        }
    }
    
    func setupUI() {
        view.backgroundColor = Color.bgDefault
        
        view.addSubviews([titleLabel,
                          closeButton,
                          tfContainer,
                          saveButton])
        
        closeButton.snp.makeConstraints { make in
            make.top.equalTo(6)
            make.right.equalTo(-6)
            make.width.height.equalTo(44)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.left.equalTo(16)
            make.right.equalTo(closeButton.snp.left)
            make.centerY.equalTo(closeButton)
        }
        
        tfContainer.snp.makeConstraints { make in
            make.top.equalTo(closeButton.snp.bottom).offset(6)
            make.left.equalTo(16)
            make.right.equalTo(-16)
        }
        
        saveButton.snp.makeConstraints { make in
            make.top.equalTo(tfContainer.snp.bottom).offset(16)
            make.left.equalTo(16)
            make.bottom.equalTo(view.safeAreaLayoutGuide).offset(-16)
            make.right.equalTo(-16)
            make.height.equalTo(44).priority(.high)
        }
    }
    
    func bindActions() {
        closeButton.rx.tap
            .map { nil }
            .bind(to: onDismiss)
            .disposed(by: disposeBagForDismiss)
        
        textField.rx.text
            .subscribe(onNext: { [unowned self] text in
                var isValidInput = !text.isEmpty && text != currentInfo
                
                if isValidInput {
                    switch infoType {
                    case .phoneNumber:
                        isValidInput = text.isValidPhoneNumber()
                    case .emailAddress:
                        isValidInput = text.isValidEmail(softCheck: true)
                    }
                }
                
                saveButton.isEnabled = isValidInput
            })
            .disposed(by: disposeBagForDismiss)
        
        Observable.combineLatest(mobileInputView.rx.countryCode,
                                 mobileInputView.rx.phoneNumber)
        .subscribe(onNext: { [unowned self] newCountryCode, mobile in
            mobileRegion = newCountryCode.code
            saveButton.isEnabled = newCountryCode.code != countryCode?.code || mobile != currentInfo
        }).disposed(by: disposeBagForDismiss)
        
        textField.rx.didEndEditing
            .subscribe(onNext: { [unowned self] text in
                switch infoType {
                case .phoneNumber:
                    if !text.isValidPhoneNumber() {
                        let errorState = AuthInputTextField
                            .DisplayModel
                            .updateErrorState(isError: true,
                                              message: "key0929".localized())
                        textField.updateUI(with: errorState)
                        onUpdateHeight.onNext(())
                    }
                    
                case .emailAddress:
                    if !text.isValidEmail(softCheck: true) {
                        let errorState = AuthInputTextField
                            .DisplayModel
                            .updateErrorState(isError: true,
                                              message: "key0930".localized())
                        textField.updateUI(with: errorState)
                        onUpdateHeight.onNext(())
                    }
                }
            })
            .disposed(by: disposeBagForDismiss)
        
        textField.rx.didBeginEditing
            .subscribe(onNext: { [unowned self] in
                textField.updateUI(with: AuthInputTextField.DisplayModel.updateErrorState(isError: false))
                onUpdateHeight.onNext(())
            })
            .disposed(by: disposeBagForDismiss)
        
        saveButton.rx.tap
            .subscribe(onNext: { [unowned self] in
                switch infoType {
                case .phoneNumber:
                    mobileInputView.isUserInteractionEnabled = false
                    mobileInputView.endEditting()
                    
                case .emailAddress:
                    textField.isUserInteractionEnabled = false
                    textField.updateUI(with: AuthInputTextField.DisplayModel.exitEditting)
                }
                
                saveButton.isUserInteractionEnabled = false
            })
            .disposed(by: disposeBagForDismiss)
    }
    
    private func bindViewModelLogic() {
        let onVerifyInput = saveButton.rx.tap
            .withLatestFrom(Observable.merge(textField.rx.text,
                                             mobileInputView.rx.phoneNumber))
        let input = ChangeProfileInfoViewModel.Input(verifyInput: onVerifyInput)
        
        let output = viewModel.transform(input: input)
        
        output.onDuplicatedInput
            .subscribe(onNext: { [unowned self] in
                switch infoType {
                case .phoneNumber:
                    mobileInputView.showError("key0931".localized())
                    
                case .emailAddress:
                    let errorState = AuthInputTextField
                        .DisplayModel
                        .updateErrorState(isError: true,
                                          message: "key0932".localized())
                    textField.updateUI(with: errorState)
                }
                
                saveButton.isEnabled = false
                
                onUpdateHeight.onNext(())
                
                textField.isUserInteractionEnabled = true
                mobileInputView.isUserInteractionEnabled = true
                saveButton.isUserInteractionEnabled = true
            })
            .disposed(by: disposeBagForDismiss)
        
        output.onVerifiedInput
            .subscribe(onNext: { [unowned self] input in
                savedInput = input
                onDismiss.onNext(changeInfo)
            })
            .disposed(by: disposeBagForDismiss)
    }
    
    private func changeInfo() {
        onChangeInfo.onNext((newInfo: savedInput, mobileRegion: mobileRegion))
    }
}

// MARK: - extension Reactive
extension Reactive where Base: ChangeProfileInfoBottomSheet {
    
    var changeInfo: Observable<(newInfo: String, mobileRegion: String?)> {
        base.onChangeInfo.asObservable()
    }
}

// MARK: - ChangeProfileInfoType
enum ChangeProfileInfoType {
    case phoneNumber
    case emailAddress
    
    var title: String {
        switch self {
        case .phoneNumber:
            return "key0725".localized()
        case .emailAddress:
            return "key0726".localized()
        }
    }
    
    var textFieldTitle: String {
        switch self {
        case .phoneNumber:
            return "key0018".localized()
        case .emailAddress:
            return "key0020".localized()
        }
    } 
    
    var textFieldPlaceholder: String {
        switch self {
        case .phoneNumber:
            return "key0018".localized()
        case .emailAddress:
            return "key0020".localized()
        }
    }
    
    var keyboardType: UIKeyboardType {
        switch self {
        case .phoneNumber:
            return .phonePad
        case .emailAddress:
            return .emailAddress
        }
    }
    
    var typeQueryValue: String {
        switch self {
        case .phoneNumber:
            return "MOBILE"
        case .emailAddress:
            return "EMAIL"
        }
    }
}
