//
//  SuspendAccountViewModel.swift
//  AppSetting
//
//  Created by <PERSON><PERSON> on 4/3/2567 BE.
//

import APILayer
import Core
import CUIModule
import RxCocoa
import RxSwift
import RxRelay
import XCoordinator

public class SuspendAccountViewModel: AnyViewModel {
    
    private let router: UnownedRouter<AppSettingsRoute>
    
    public struct Input {
        let onViewAppear: Observable<Void>
        let validateForm: Observable<(reason: String, pin: Bool)>
        let onCloseAccount: Observable<(reason: String, password: String)>
    }
    
    public struct Output {
        let loading: Driver<Bool>
        let error: Driver<Error>
        let displayData: Driver<SuspendAccountAction>
        let enableButton: Driver<Bool>
        let didCloseAccount: Driver<Void>
    }
    
    private(set) var accountState: SuspendAccountAction
    
    init(router: UnownedRouter<AppSettingsRoute>,
         state: SuspendAccountAction) {
        self.router = router
        self.accountState = state
    }
    
    public func transform(input: Input) -> Output {
        let activityIndicator = ActivityIndicator()
        let errorTracker = ErrorTracker()
        
        let displayData = input.onViewAppear
            .map { self.accountState }
        
        let formValid = input.validateForm
            .map {
                !$0.reason.isEmpty && $0.pin
            }
        
        let closeAccount = input.onCloseAccount
            .flatMap { [unowned self] reason, password in
                self.closeAccount(with: reason, password: password)
                    .track(activityIndicator, error: errorTracker)
            }
        
        return Output(loading: activityIndicator.asDriver(),
                      error: errorTracker.asDriver(),
                      displayData: displayData.asDriverOnErrorNever(),
                      enableButton: formValid.asDriverOnErrorNever(),
                      didCloseAccount: closeAccount.asDriverOnErrorNever())
    }
    
    func navigate(to route: AppSettingsRoute) {
        router.trigger(route)
    }
}

// MARK: - API
private extension SuspendAccountViewModel {
    
    func closeAccount(with reason: String, password: String) -> Observable<Void> {
        UpdateCustomerStatusEndPoint.service.call(with: .pendingClosure,
                                                  reason: reason,
                                                  password: password)
        .mapToVoid()
    }
}
