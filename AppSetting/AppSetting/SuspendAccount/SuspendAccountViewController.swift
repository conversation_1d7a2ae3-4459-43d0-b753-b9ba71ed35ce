//
//  SuspendAccountViewController.swift
//  AppSetting
//
//  Created by <PERSON><PERSON> on 4/3/2567 BE.
//

import Core
import CUIModule
import APILayer
import RxSwift
import Storage

final class SuspendAccountViewController: BaseViewController, VMView {
    
    private lazy var container = {
        let view = UIView(backgroundColor: Color.bgDefault)
        view.roundCorners(radius: 12, corners: [.topLeft,
                                                .topRight])
        view.addSubviews([titleStackView,
                          scrollView,
                          passwordView,
                          buttonStackView])
        // Title Stack View
        titleStackView.snp.makeConstraints { make in
            make.top.right.equalToSuperview()
            make.left.equalTo(12)
        }
        
        scrollView.snp.makeConstraints { make in
            make.top.equalTo(titleStackView.snp.bottom).offset(16)
            make.left.right.equalToSuperview()
            make.bottom.equalTo(passwordView.snp.top).offset(-16)
        }
        
        /// Password View
        passwordView.snp.makeConstraints { make in
            make.bottom.equalTo(buttonStackView.snp.top).offset(-12)
            make.left.equalTo(16)
            make.right.equalTo(-16)
        }
        /// Cancel Button, Proceed Button
        buttonStackView.snp.makeConstraints { make in
            make.bottom.equalTo(-((keyWindow?.safeAreaInsets.bottom ?? 0) + 20))
            make.left.equalTo(16)
            make.right.equalTo(-16)
        }
        
        return view
    }()
    
    private lazy var scrollView = {
        let scrollView = UIScrollView(backgroundColor: .clear,
                                      showsVerticalScrollIndicator: false)
        scrollView.delaysContentTouches = true
        scrollView.addSubview(contentView)
        contentView.snp.makeConstraints { make in
            make.edges.width.equalToSuperview()
        }
        
        return scrollView
    }()
    
    private lazy var contentView = {
        let view = UIView(backgroundColor: .clear)
        view.addSubview(stackView)
        stackView.snp.makeConstraints { make in
            make.top.bottom.equalToSuperview()
            make.right.equalTo(-16)
            make.left.equalTo(16)
        }
        
        return view
    }()
    
    private lazy var stackView = {
        let stackView = UIStackView(axis: .vertical,
                                    spacing: 12)
        stackView.addArrangedSubviews([warningLabel,
                                       descriptionLabel,
                                       subDescriptionLabel,
                                       howToView])
        /// How To View
        howToView.snp.makeConstraints { make in
            make.height.equalTo(44)
        }
        
        return stackView
    }()
    
    // ---------- Title ----------
    private lazy var titleStackView = {
        let stackView = UIStackView(alignment: .center,
                                    spacing: 4)
        stackView.addArrangedSubviews([titleImageView,
                                       titleLabel,
                                       UIView(),
                                       closeButton])
        closeButton.snp.makeConstraints { $0.width.height.equalTo(44) }
        
        return stackView
    }()
    
    private lazy var titleImageView = UIImageView(image: viewModel.accountState.image)
    
    private lazy var titleLabel = {
        let label = UILabel(font: Font.regular.of(size: 14),
                            text: "key0102".localized() + viewModel.accountState.title,
                            textColor: Color.txtParagraph)
        label.highlight("key0102".localized(),
                        highlightFont: Font.semiBold.of(size: 14),
                        highlightColor: Color.txtTitle)
        
        return label
    }()
    
    private lazy var closeButton = UIButton(normalImage: .image(named: "merit_ic_cross"))

    // ---------- Description ----------
    private lazy var descriptionLabel = UILabel(numberOfLines: 0)
    
    private lazy var subDescriptionLabel = UILabel(font: Font.light.of(size: 14),
                                                   numberOfLines: 0,
                                                   textColor: Color.txtParagraph)
    
    private lazy var buttonStackView = {
        let stackView = UIStackView(distribution: .fillEqually,
                                    spacing: 8)
        stackView.addArrangedSubviews([cancelButton,
                                       actionButton])
        cancelButton.snp.makeConstraints { $0.height.equalTo(44) }
        actionButton.snp.makeConstraints { $0.height.equalTo(44) }
        
        return stackView
    }()
    
    private lazy var actionButton = UIButton(type: .system,
                                            backgroundColor: Color.btnDisabled,
                                            titleFont: Font.semiBold.of(size: 14),
                                            title: viewModel.accountState.title,
                                            normalTitleColor: Color.txtInverted,
                                            cornerRadius: 22,
                                            isEnable: false)
    
    private lazy var cancelButton = UIButton(type: .system,
                                             backgroundColor: Color.bgDefault,
                                             titleFont: Font.medium.of(size: 14),
                                             title: "key0273".localized(),
                                             normalTitleColor: Color.txtTitle,
                                             cornerRadius: 22,
                                             borderColor: Color.btn2nd,
                                             borderWidth: 1)
    
    private lazy var howToView = {
        let view = UIView(backgroundColor: Color.bgCaution,
                          cornerRadius: 4)
        
        view.addSubviews([questionIconView,
                          howToLabel,
                          arrowImageView])
        questionIconView.snp.makeConstraints { make in
            make.left.equalTo(8)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(14)
        }
        /// How To Label
        howToLabel.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.left.equalTo(questionIconView.snp.right).offset(6)
        }
        arrowImageView.snp.makeConstraints { make in
            make.right.equalTo(-8)
            make.centerY.equalToSuperview()
        }
        return view
    }()
    
    private lazy var questionIconView = UIImageView(image: .image(named: "ic_question"))
    
    private lazy var howToLabel = UILabel(font: Font.medium.of(size: 14),
                                          text: "key0796".localized(),
                                          textColor: Color.txtCaution)
    
    private lazy var arrowImageView = UIImageView(image: .image(named: "ic_arrow_yellow"))
    
    private lazy var passwordView = PasswordInputView()
    
    private lazy var warningLabel = CUIPaddingLabel(
        backgroundColor: Color.themeCustomThird6,
        font: Font.semiBold.of(size: 12),
        numberOfLines: 0,
        textColor: Color.txtNegative,
        cornerRadius: 4
    )
    
    private lazy var specifyReasonTextField = CUITextField()

    var viewModel: SuspendAccountViewModel!
    var viewAppeared = false

    let selectedReason = PublishSubject<String>()

    private lazy var buttonArrays: [UIButton] = []
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        bindActions()
        setUpAnimation()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        if viewAppeared { return }
        viewAppeared = true

        cancelButton.layer.borderColor = Color.txtTitle.cgColor
        cancelButton.layer.borderWidth = 1

        appearAnimation()
    }

    override func dismiss(animated flag: Bool, completion: (() -> Void)? = nil) {
        disappearAnimation()
        super.dismiss(animated: flag, completion: completion)
    }
    
    func setupUI() {
        view.backgroundColor = Color.bgDefault
        view.addSubview(container)
        container.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide.snp.top).offset(34)
            make.right.left.bottom.equalToSuperview()
        }

        specifyReasonTextField.snp.makeConstraints { make in
            make.height.equalTo(44)
        }
    }
    
    func bindActions() {
        closeButton.rx.tap.subscribe(onNext: { [weak self] in
            self?.dismiss(animated: true)
        }).disposed(by: disposeBag)

        let howToTap = UITapGestureRecognizer()
        howToView.isUserInteractionEnabled = true
        howToView.addGestureRecognizer(howToTap)
        howToTap.rx.event.bind(onNext: { [weak self] _ in
            self?.dismiss(animated: true)
            self?.viewModel.navigate(to: .faqs)
        }).disposed(by: disposeBag)
        
        specifyReasonTextField.editingBegin.subscribe(onNext: { [weak self] _ in
            self?.selectedReason.onNext("")
            self?.selectReason(nil)
            self?.selectSpecifyReason(true)
        }).disposed(by: disposeBag)
        
        specifyReasonTextField.textChanged.subscribe(onNext: { [weak self] in
            self?.selectedReason.onNext($0)
        }).disposed(by: disposeBag)
        
        cancelButton.rx.tap.subscribe(onNext: { [weak self] in
            self?.dismiss(animated: true)
        }).disposed(by: disposeBag)
    }
    
    func bind(viewModel: SuspendAccountViewModel) {
        self.viewModel = viewModel
        
        let validateForm = Observable.combineLatest(selectedReason,
                                                    passwordView.rx.isValidated)
            .map { (reason: $0, pin: $1) }
        
        let reasonPassword = Observable.combineLatest(selectedReason,
                                                      passwordView.rx.password)
            .map { reason, password in
                (reason: reason, password: password)
            }
        
        let input = SuspendAccountViewModel.Input(
            onViewAppear: rx.viewWillAppear.take(1).mapToVoid(),
            validateForm: validateForm,
            onCloseAccount: actionButton.rx.tap.asObservable().withLatestFrom(reasonPassword)
        )
        
        let output = viewModel.transform(input: input)
        
        output.loading
            .drive(onNext: { [unowned self] isLoading  in
                isLoading ? showLoading() : hiddenLoading()
            }).disposed(by: disposeBag)
        
        output.error
            .drive(onNext: { [unowned self] error in
                var errorMessage = error.localizedDescription
                if let apiError = error as? APIError {
                    errorMessage = apiError.message
                }
                
                showAlert(title: "key0895".localized(), message: errorMessage) { [unowned self] in
                    dismiss(animated: true)
                }
            }).disposed(by: disposeBag)
        
        output.displayData.drive(onNext: { [weak self] in
            self?.displayUI($0)
        }).disposed(by: disposeBag)
        
        output.enableButton.drive(onNext: { [unowned self] isEnabled in
            actionButton.isEnabled = isEnabled
            actionButton.backgroundColor = isEnabled ? Color.btnNegative : Color.btnDisabled
        }).disposed(by: disposeBag)
        
        output.didCloseAccount
            .drive(onNext: { [unowned self] in
                dismiss(animated: true)
                viewModel.navigate(to: .suspendAccountSuccess(state: viewModel.accountState))
            }).disposed(by: disposeBag)
    }
}

// MARK: - Update Data {
extension SuspendAccountViewController {
    
    private func displayUI(_ state: SuspendAccountAction) {
        switch state {
        case .closeAccount:
            warningLabel.isHidden = true
            
        case .delete(let hasFunds):
            warningLabel.isHidden = !hasFunds
            warningLabel.text = state.warning
            warningLabel.padding(8, 8, 8, 8)
        }
        
        // swiftlint: disable line_length
        subDescriptionLabel.text = "key0795".localized()
        
        descriptionLabel.highlight(state.desc,
                                   normalColor: Color.txtParagraph,
                                   normalFont: Font.light.of(size: 14),
                                   highlightText: ["key0793".localized()],
                                   highlightColor: Color.txtTitle,
                                   highlightFont: Font.light.of(size: 14))
        // swiftlint: enable line_length
        
        buttonArrays.removeAll()
        state.questions.enumerated().forEach { index, item in
            let button = UIButton(backgroundColor: Color.bgDefault,
                                  titleFont: Font.light.of(size: 14),
                                  title: item,
                                  normalTitleColor: Color.txtParagraph,
                                  contentHorizontalAlignment: .left,
                                  cornerRadius: 22,
                                  borderColor: Color.txtParagraph,
                                  borderWidth: 0,
                                  titleInsets: UIEdgeInsets(left: 12, right: -12),
                                  tag: index)
            button.snp.makeConstraints { make in
                make.height.equalTo(44)
            }
            button.rx.tap.subscribe(onNext: { [weak self] in
                self?.selectReason(index)
            }).disposed(by: disposeBag)
            buttonArrays.append(button)
            stackView.addArrangedSubview(button)
            stackView.setCustomSpacing(0, after: button)
        }
        specifyReasonTextField.updateDisplay(config: CUITextField.Config(
            font: Font.light.of(size: 14),
            textColor: Color.txtParagraph,
            placeholder: "key0802".localized(),
            placeholderColor: Color.txtDisabled,
            backgroundColor: Color.bgDefaultAlternate,
            border: (0, Color.txtParagraph),
            inputType: .text(.default)
        ))
        stackView.addArrangedSubview(specifyReasonTextField)
    }

    private func selectReason(_ selectedIndex: Int?) {
        for button in buttonArrays {
            if selectedIndex == button.tag {
                button.layer.borderWidth = 1
                selectedReason.onNext(button.title(for: .normal) ?? "")
                selectSpecifyReason(false)
            } else {
                button.layer.borderWidth = 0
            }
        }
    }

    private func selectSpecifyReason(_ isSelect: Bool) {
        specifyReasonTextField.updateBorder(width: isSelect ? 1 : 0)
        if !isSelect {
            self.view.endEditing(true)
            specifyReasonTextField.updateText(nil, withNotifyAction: false)
        }
    }
}

// MARK: - Animation
private extension SuspendAccountViewController {
    func setUpAnimation() {
        view.backgroundColor = .clear
        container.transform = CGAffineTransform.identity.translatedBy(
            x: 0, y: UIScreen.main.bounds.height
        )
    }

    func appearAnimation() {
        UIView.animate(
            withDuration: 0.3, delay: 0, options: [.curveEaseOut],
            animations: { [weak self] in
                self?.view.backgroundColor = UIColor.black.withAlphaComponent(0.3)
                self?.container.transform = CGAffineTransform.identity
            }
        )
    }

    func disappearAnimation() {
        UIView.animate(
            withDuration: 0.3, delay: 0, options: [.curveEaseOut],
            animations: { [weak self] in
                self?.view.backgroundColor = .clear
                self?.container.transform = CGAffineTransform.identity.translatedBy(
                    x: 0, y: UIScreen.main.bounds.height
                )
            }
        )
    }
}
