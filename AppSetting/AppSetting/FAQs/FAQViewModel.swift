//
//  FAQViewModel.swift
//  AppSetting
//
//  Created by <PERSON> on 07/03/2024.
//

import Core
import APILayer
import RxSwift
import RxCocoa

final class FAQViewModel: AnyViewModel {
    
    struct Input {
        let onViewAppear: Observable<Void>
        let onToggleExpandingQuestion: Observable<IndexPath>
        let onSearchQuestion: Observable<String>
    }
    
    struct Output {
        let onError: Driver<Error>
        let onShowLoading: Driver<Bool>
        let updateFAQList: Driver<Void>
    }
    
    // MARK: Propeties
    private var categories: [String] = []
    private var faqModels: [String: [FAQModel]] = [:]
    
    private(set) var filteredCategories: [String] = []
    private(set) var filteredFaqs: [String: [FAQModel]] = [:]
    
    func transform(input: Input) -> Output {
        let activityIndicator = ActivityIndicator()
        let errorTracker = ErrorTracker()
        
        let onUpdateFAQList = input.onViewAppear
            .flatMap { [unowned self] in
                queryFaqs()
                    .track(activityIndicator, error: errorTracker)
            }
            .onNext { [unowned self] response in
                mapFaqs(from: response)
                filter(by: "")
            }
            .mapToVoid()
        
        let toggleExpandingQuestion = input.onToggleExpandingQuestion
            .map { self.updateExpandState(at: $0) }
            .mapToVoid()
        
        let onSearchQuestion = input.onSearchQuestion
            .map { self.filter(by: $0) }
            .mapToVoid()
        
        let reloadFAQDisplay = Observable.merge(onUpdateFAQList,
                                                toggleExpandingQuestion,
                                                onSearchQuestion)
        
        return Output(onError: errorTracker.asDriver(),
                      onShowLoading: activityIndicator.asDriver(),
                      updateFAQList: reloadFAQDisplay.asDriverOnErrorNever())
    }
}

// MARK: - Private
private extension FAQViewModel {
    
    func updateExpandState(at indexPath: IndexPath) {
        let category = filteredCategories[indexPath.section]
        guard let faq = filteredFaqs[category]?[indexPath.row] else { return }
        
        // If is being expanded, close all others
        if !faq.isExpanded {
            filteredCategories.forEach { category in
                if filteredFaqs[category] != nil {
                    for index in 0..<filteredFaqs[category]!.count {
                        filteredFaqs[category]![index].isExpanded = false
                    }
                }
            }
        }
        
        filteredFaqs[category]?[indexPath.row].isExpanded = !faq.isExpanded
    }
    
    func filter(by keyword: String) {
        guard !keyword.isEmpty else {
            filteredCategories = categories
            filteredFaqs = faqModels
            return
        }
        
        filteredCategories.removeAll()
        filteredFaqs.removeAll()
        
        for category in self.categories {
            if let faqs = faqModels[category]?.filter({ $0.question.contain(keyword: keyword) }),
               !faqs.isEmpty {
                filteredCategories.append(category)
                filteredFaqs[category] = faqs
            }
        }
    }
    
    func mapFaqs(from response: FAQListEndPoint.Response) {
        guard
            let faqList = response.faqList?.sorted(by: { $0.id ?? 0 < $1.id ?? 0 })
        else { return }
        
        categories = faqList.compactMap { $0.title }
        
        for faqCategory in faqList {
            if let faqItems = faqCategory.list?.sorted(by: { $0.id ?? 0 < $1.id ?? 0}),
               !faqItems.isEmpty {
                let models = faqItems.map { FAQModel(question: $0.question ?? "",
                                                     answer: $0.answer?.title ?? "")}
                
                faqModels[faqCategory.title ?? ""] = models
            }
        }
    }
}

// MARK: - API
private extension FAQViewModel {
    
    func queryFaqs() -> Observable<FAQListEndPoint.Response> {
        FAQListEndPoint.service.fetch()
    }
}

// MARK: - extension String
private extension String {
    
    func contain(keyword: String) -> Bool {
        let trimmed = self.replacingOccurrences(of: " ", with: "").lowercased()
        let trimmedKeyword = keyword.replacingOccurrences(of: " ", with: "").lowercased()
        
        return trimmed.contains(trimmedKeyword)
    }
}
