//
//  FAQCell.swift
//  AppSetting
//
//  Created by <PERSON> on 07/03/2024.
//

import UIKit
import Core
import CUIModule
import RxSwift

final class FAQCell: UITableViewCell, AnyView {
    
    // MARK: UI properties
    private lazy var contentContainer = {
        let view = UIView(backgroundColor: Color.bgDefaultTone,
                          cornerRadius: 4)
        view.addSubviews([contentStackView,
                          questionOverlayButton])
        contentStackView.snp.makeConstraints { make in
            make.top.equalTo(8)
            make.left.equalTo(12)
            make.bottom.equalTo(-8)
            make.right.equalTo(-12)
        }
        
        questionOverlayButton.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.bottom.equalTo(questionStackView)
        }
        
        return view
    }()
    
    private lazy var contentStackView = {
        let stackView = UIStackView(axis: .vertical,
                                    spacing: 8)
        stackView.addArrangedSubviews([questionStackView,
                                       answerLabel])
        return stackView
    }()
    
    // Question
    private lazy var questionStackView = {
        let stackView = UIStackView(alignment: .center,
                                    spacing: 4)
        
        stackView.addArrangedSubviews([questionLabel,
                                       caretButton])
        caretButton.snp.makeConstraints { $0.width.height.equalTo(16) }
        
        return stackView
    }()
    
    private lazy var questionLabel = UILabel(font: Font.medium.of(size: 12),
                                             numberOfLines: 0,
                                             textColor: Color.txtParagraph)

    private lazy var caretButton = ThemeableButton(normalImage: .image(named: "ic_caret_down_faq"),
                                                   selectedImage: .image(named: "ic_caret_up_faq"),
                                                   isUserInteractionEnabled: false,
                                                   themeType: .textHighlight(icon: true, text: true))

    fileprivate lazy var questionOverlayButton = UIButton()
    
    // Answer
    private lazy var answerLabel = UILabel(font: Font.medium.of(size: 12),
                                           numberOfLines: 0,
                                           textColor: Color.txtParagraph)
    
    // MARK: Properties
    private(set) var disposeBag = DisposeBag()
    
    // MARK: Life cycle
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override func prepareForReuse() {
        super.prepareForReuse()
        
        disposeBag = DisposeBag()
    }
    
    func setupUI() {
        selectionStyle = .none
        backgroundColor = .clear
        contentView.backgroundColor = .clear
        
        contentView.addSubview(contentContainer)
        contentContainer.snp.makeConstraints { make in
            make.top.equalTo(4)
            make.bottom.equalTo(-4)
            make.left.right.equalToSuperview()
        }
    }
    
    func updateUI(with displayModel: UIDisplayModel) {
        guard let displayModel = displayModel as? FAQModel else { return }
        
        questionLabel.text = "Q. " + displayModel.question
        questionLabel.setLineSpacing(lineHeightMultiple: 1.23)
        questionLabel.highlight("Q. ",
                                highlightFont: Font.bold.of(size: 12),
                                highlightColor: Color.orangeOrange)
        
        answerLabel.font = Font.medium.of(size: 12)
        answerLabel.textColor = Color.txtParagraph
        answerLabel.text = "A. " + displayModel.answer
        answerLabel.setLineSpacing(lineHeightMultiple: 1.23)
        answerLabel.highlight("A. ",
                              highlightFont: Font.bold.of(size: 12),
                              highlightColor: Color.skyBlue)
        
        answerLabel.isHidden = !displayModel.isExpanded
        contentContainer.backgroundColor = displayModel.isExpanded ? Color.bgDefaultToneDarker : Color.bgDefaultTone
        caretButton.isSelected = displayModel.isExpanded
    }
}

// MARK: - extension Reactive
extension Reactive where Base: FAQCell {
    
    var tapQuestion: Observable<Void> {
        base.questionOverlayButton.rx.tap.asObservable()
    }
}
