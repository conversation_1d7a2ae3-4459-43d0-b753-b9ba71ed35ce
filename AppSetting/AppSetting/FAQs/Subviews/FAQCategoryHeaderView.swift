//
//  FAQCategoryHeaderView.swift
//  AppSetting
//
//  Created by <PERSON> on 07/03/2024.
//

import UIKit
import Core
import CUIModule

final class FAQCategoryHeaderView: UITableViewHeaderFooterView, AnyView {
    
    static var reuseIdentifier: String {
        return String(describing: type(of: self))
    }
    
    // MARK: UI properties
    private lazy var contentStackView = {
        let stackView = UIStackView(alignment: .center,
                                    spacing: 2)
        stackView.addArrangedSubviews([iconImgView,
                                       titleLabel,
                                       UIView()])
        
        return stackView
    }()
    
    private lazy var iconImgView = ThemeableImageView(themeType: .textHighlight)
    private lazy var titleLabel = UILabel(font: Font.semiBold.of(size: 12),
                                          textColor: Color.txtLabel)
    
    // MARK: Properties
    
    // MARK: Life cycle
    override init(reuseIdentifier: String?) {
        super.init(reuseIdentifier: reuseIdentifier)
        
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func setupUI() {
        contentView.addSubview(contentStackView)
        contentStackView.snp.makeConstraints { make in
            make.top.equalTo(12).priority(.high)
            make.bottom.equalTo(-4).priority(.high)
            make.left.right.equalToSuperview()
        }
    }
    
    func updateUI(with displayModel: UIDisplayModel) {
        guard let displayModel = displayModel as? DisplayModel else { return }
        
        if displayModel.index < 8 {
            iconImgView.image = .image(named: "faq_cat_\(displayModel.index + 1)")
        }
        titleLabel.text = displayModel.category.uppercased()
    }
}

// MARK: - Element
extension FAQCategoryHeaderView {
    
    struct DisplayModel: UIDisplayModel {
        let index: Int
        let category: String
    }
}
