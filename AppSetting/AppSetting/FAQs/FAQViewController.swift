//
//  FAQViewController.swift
//  AppSetting
//
//  Created by <PERSON> on 07/03/2024.
//

import UIKit
import Core
import CUIModule
import RxSwift
import APILayer
import Storage

final class FAQViewController: BaseViewController, VMView {
    
    // MARK: UI properties
    private lazy var headerView = {
        let displayModel = CUINavigationBar.DisplayModel(leftIcon: .image(named: "iconback"),
                                                         titleView: titleStackView)
        let view = CUINavigationBar(displayModel: displayModel)
        
        return view
    }()
    
    private lazy var titleStackView = {
        let stackView = UIStackView(spacing: 4)
        stackView.addArrangedSubviews([titleLogoImgView,
                                       titleLabel])
        
        return stackView
    }()
    
    private lazy var titleLogoImgView = ThemeableImageView(
        image: .image(named: "ic_question_18"),
        themeType: .textHighlight
    )
    private lazy var titleLabel = UILabel(font: Font.semiBold.of(size: 14),
                                          text: "FAQS",
                                          textColor: Color.txtTitle)
    // Search bar
    private lazy var searchBar = CUITextField(CUITextField.Config(textColor: Color.txtParagraph,
                                                                  placeholder: "Search your question ...",
                                                                  leftIcon: .image(named: "icon_search"),
                                                                  leftIconTintColor: CustomThemeManager.shared.getCurrentTheme()?.textHighlight.color,
                                                                  autocapitalizationType: .none,
                                                                  clearButtonMode: .whileEditing,
                                                                  clearButtonImage: .image(named: "iconcrosscircle")))
    // Table view
    private lazy var tableView = {
        let tableView = UITableView(style: .grouped,
                                    backgroundColor: .clear,
                                    showsVerticalScrollIndicator: false)
        
        tableView.register(FAQCell.self)
        tableView.register(FAQCategoryHeaderView.self)
        tableView.delegate = self
        tableView.dataSource = self
        
        return tableView
    }()
    
    // MARK: Properties
    var viewModel: FAQViewModel!
    private let toggleExpandingQuestion = PublishSubject<IndexPath>()
    
    // MARK: Life cycle
    override func viewDidLoad() {
        super.viewDidLoad()
        
        setupUI()
        bindActions()
        bindViewModelLogic()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        
        navigationBarHidden(true)
    }
    
    func setupUI() {
        view.backgroundColor = Color.bgDefault
        
        view.addSubviews([headerView,
                          searchBar,
                          tableView])
        
        headerView.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide)
            make.left.right.equalToSuperview()
        }
        
        searchBar.snp.makeConstraints { make in
            make.top.equalTo(headerView.snp.bottom).offset(16)
            make.left.equalTo(12)
            make.right.equalTo(-12)
            make.height.equalTo(44)
        }
        
        tableView.snp.makeConstraints { make in
            make.top.equalTo(searchBar.snp.bottom).offset(8)
            make.left.equalTo(12)
            make.bottom.equalToSuperview()
            make.right.equalTo(-12)
        }
    }
    
    func bindActions() {
        // Back
        headerView.rx.leftTap
            .subscribe(onNext: { [unowned self] in
                self.backPress()
            })
            .disposed(by: disposeBag)
    }
    
    func bindViewModelLogic() {
        let onSearch = searchBar.textChanged
            .distinctUntilChanged()
            .skip(1)
        
        let input = FAQViewModel.Input(onViewAppear: rx.viewWillAppear.take(1).mapToVoid(),
                                       onToggleExpandingQuestion: toggleExpandingQuestion.asObservable(),
                                       onSearchQuestion: onSearch)
        
        let output = viewModel.transform(input: input)
        
        output.onShowLoading
            .drive(onNext: { [unowned self] show in
                show ? showLoading() : hiddenLoading()
            })
            .disposed(by: disposeBag)
        
        output.onError
            .drive(onNext: { [unowned self] error in
                var errorMessage = error.localizedDescription
                if let apiError = error as? APIError {
                    errorMessage = apiError.message
                }
                showAlert(message: errorMessage)
            })
            .disposed(by: disposeBag)
        
        output.updateFAQList
            .drive(onNext: { [unowned self] in
                UIView.transition(with: self.tableView,
                                  duration: 0.3,
                                  options: .transitionCrossDissolve,
                                  animations: {
                    self.tableView.reloadData()
                })
                
            })
            .disposed(by: disposeBag)
    }
}

// MARK: - UITableViewDataSource, UITableViewDelegate
extension FAQViewController: UITableViewDataSource, UITableViewDelegate {
    
    func numberOfSections(in tableView: UITableView) -> Int {
        return viewModel.filteredCategories.count
    }
    
    func tableView(_ tableView: UITableView, viewForHeaderInSection section: Int) -> UIView? {
        let reuseIdentifier = FAQCategoryHeaderView.reuseIdentifier
        
        let header = tableView.dequeueReusableHeaderFooterView(withIdentifier: reuseIdentifier)
        if let headerView = header as? FAQCategoryHeaderView {
            let displayModel = FAQCategoryHeaderView.DisplayModel(index: section,
                                                                  category: viewModel.filteredCategories[section])
            headerView.updateUI(with: displayModel)
        }
        
        return header
    }
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        let category = viewModel.filteredCategories[section]
        return viewModel.filteredFaqs[category]?.count ?? 0
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell: FAQCell = tableView.dequeueReusableCell(forIndexPath: indexPath)
        let category = viewModel.filteredCategories[indexPath.section]
        guard let faq = viewModel.filteredFaqs[category]?[indexPath.row] else { return cell }
        
        cell.updateUI(with: faq)
        
        cell.rx.tapQuestion
            .subscribe(onNext: { [unowned self] in
                if !faq.isExpanded {
                    tableView.scrollToRow(at: indexPath,
                                          at: .top,
                                          animated: true)
                }
                self.toggleExpandingQuestion.onNext(indexPath)
            })
            .disposed(by: cell.disposeBag)
        
        return cell
    }
    
    func tableView(_ tableView: UITableView, heightForHeaderInSection section: Int) -> CGFloat {
        return 30
    }
        
    func tableView(_ tableView: UITableView, viewForFooterInSection section: Int) -> UIView? {
        return nil
    }
    
    func tableView(_ tableView: UITableView, heightForFooterInSection section: Int) -> CGFloat {
        return CGFloat.leastNonzeroMagnitude
    }
}
