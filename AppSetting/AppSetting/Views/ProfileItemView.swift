//
//  ProfileItemView.swift
//  AppSetting
//
//  Created by <PERSON><PERSON><PERSON> on 22/11/2566 BE.
//

import CUIModule
import RxSwift
import Core
import Storage
import SharedData

final class ProfileItemView: UIStackView, AnyView {
    
    // MARK: UI properties
    private lazy var imageContainer = UIView(backgroundColor: .clear)
    
    /// Profile Image View
    private lazy var imageView = UIImageView(contentMode: .scaleAspectFill,
                                             cornerRadius: 16,
                                             image: UIImage.image(named: "merit_ic_user"))
    /// Profile Info Stack View
    private lazy var infoStackView = UIStackView(axis: .vertical,
                                                 spacing: 2)
    /// Name Stack View
    private lazy var nameStackView = UIStackView(alignment: .center,
                                                 spacing: 4)
    
    /// Name Label
    private lazy var nameLabel = UILabel(font: Font.semiBold.of(size: 14),
                                         textColor: Color.txtTitle)
    /// Level Label
    private lazy var riskLevelBadge = RiskLevelBadge()
    
    /// Email Label
    private lazy var emailLabel = UILabel(font: Font.light.of(size: 14),
                                          textColor: Color.txtInactive)
    
    // MARK: Properties
    private let disposeBag = DisposeBag()
    
    // MARK: Life cycle
    init() {
        super.init(frame: .zero)
        setupUI()
    }
    
    required init(coder: NSCoder) {
        super.init(coder: coder)
    }
    
    func setupUI() {
        axis = .horizontal
        alignment = .center
        spacing = 8

        addArrangedSubviews([imageContainer,
                             infoStackView])
        imageContainer.snp.makeConstraints { make in
            make.width.equalTo(44)
        }
        imageContainer.addSubview(imageView)
        imageView.snp.makeConstraints { make in
            make.width.height.equalTo(32)
            make.top.bottom.centerX.equalToSuperview()
        }

        infoStackView.addArrangedSubviews([nameStackView, emailLabel])
        nameStackView.addArrangedSubviews([nameLabel, riskLevelBadge, UIView()])
        nameLabel.setContentHuggingPriority(.required, for: .horizontal)
    }
    
    func updateUI(with displayModel: UIDisplayModel) {
        guard let displayModel = displayModel as? DisplayModel else { return }
        
        nameLabel.text = displayModel.name
        emailLabel.text = displayModel.email
        if let profilePicture = LocalPreference.profilePicture {
            imageView.image = profilePicture
        } else {
            imageView.image = .image(named: displayModel.image)
        }
    }
}

// MARK: - Define elements
extension ProfileItemView {
    /// Display model
    struct DisplayModel: UIDisplayModel {
        let name: String
        let email: String
        let image: String
    }
}
