//
//  ThemeListEmptyView.swift
//  AppSetting
//
//  Created by <PERSON><PERSON><PERSON> on 2/22/67.
//
import CUIModule
import UIKit
import SnapKit
import RxSwift
import SharedData

public class ThemeListEmptyView: UIView {

    private lazy var stackView = UIStackView(
        axis: .vertical,
        spacing: 16
    )

    private lazy var titleLabel = UILabel(
        font: Font.semiBold.of(size: 16),
        text: "YOU HAVE NO THEMES.",
        textColor: Color.txtLabel
    )

    private lazy var descLabel = UILabel(
        font: Font.medium.of(size: 14),
        text: "Let’s start creating your theme!",
        textColor: Color.txtParagraph
    )

    fileprivate lazy var createButton = ThemeableButton(
        titleFont: Font.bold.of(size: 14),
        title: "CREATE YOUR OWN THEME",
        normalTitleColor: Color.pitchBlack,
        normalBgColor: Color.bgButtonDefault,
        cornerRadius: 22,
        themeType: .primary
    )

    public init() {
        super.init(frame: .zero)
        setupUI()
    }

    required init(coder: NSCoder) {
        super.init(frame: .zero)
        setupUI()
    }

    private func setupUI() {

        addSubview(stackView)
        stackView.snp.makeConstraints { make in
            make.top.bottom.equalToSuperview()
            make.width.equalTo(236)
            make.centerX.equalToSuperview()
        }

        stackView.addArrangedSubviews([titleLabel, descLabel, createButton])
        createButton.snp.makeConstraints { make in
            make.height.equalTo(44)
        }
    }
}
// MARK: - Reactive
extension Reactive where Base: ThemeListEmptyView {

    var createTheme: Observable<Void> {
        return base.createButton.rx.tap.mapToVoid()
    }
}
