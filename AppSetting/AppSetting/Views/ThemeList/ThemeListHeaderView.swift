//
//  ThemeListHeaderView.swift
//  AppSetting
//
//  Created by <PERSON><PERSON><PERSON> on 2/22/67.
//
import UIKit
import Core
import CUIModule

protocol ThemeListHeaderViewProtocol: AnyObject {
    func themeListHeaderView(onTap view: ThemeListHeaderView)
}

class ThemeListHeaderView: UITableViewHeaderFooterView, AnyView {
    static var reuseIdentifier: String {
        return String(describing: type(of: self))
    }

    lazy var createButton = ThemeableButton(
        titleFont: Font.bold.of(size: 14),
        title: "CREATE A NEW THEME",
        normalTitleColor: Color.txtParagraph,
        normalBgColor: Color.themeCustomThird1,
        normalImage: .image(named: "icon_plus_circle"),
        cornerRadius: 4,
        titleInsets: UIEdgeInsets(top: 0, left: 2, bottom: 0, right: -2),
        imageInsets: UIEdgeInsets(top: 0, left: -2, bottom: 0, right: 2),
        themeType: .textHighlight(icon: true, text: false)
    )

    var delegate: ThemeListHeaderViewProtocol?

    override init(reuseIdentifier: String?) {
        super.init(reuseIdentifier: reuseIdentifier)
        setupUI()

        createButton.addTarget(
            self,
            action: #selector(self.onTappedCreateTheme),
            for: .touchUpInside
        )
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    func setupUI() {
        contentView.addSubview(createButton)
        createButton.snp.makeConstraints { make in
            make.left.equalTo(12)
            make.right.equalTo(-12)
            make.height.equalTo(44)
            make.centerY.equalToSuperview()
        }
    }

    @objc func onTappedCreateTheme() {
        delegate?.themeListHeaderView(onTap: self)
    }
}
