//
//  ThemeListCell.swift
//  AppSetting
//
//  Created by <PERSON><PERSON><PERSON> on 2/22/67.
//
import SnapKit
import UIKit
import SharedData
import Storage
import CUIModule

public protocol ThemeListCellProtocol: AnyObject {
    func themeListCell(onSwitch value: Bool, indexPath: IndexPath, data: CustomThemeModel)
    func themeListCell(onSelect indexPath: IndexPath, data: CustomThemeModel)
}

public class ThemeListCell: UITableViewCell {

    private lazy var container = UIView(
        backgroundColor: Color.bgDefaultTone,
        cornerRadius: 4
    )

    private lazy var switchButton = CUISwitch(config: CUISwitch.Config(
        width: 28,
        height: 18,
        backgroundPositiveColor: Color.bgButtonPositive,
        backgroundNegativeColor: Color.txtLabelNeutral,
        cornerRadius: 9,
        itemPadding: 3
    ))

    private lazy var bgButton = UIButton(
        backgroundColor: .clear
    )

    private lazy var titleLabel = UILabel(
        font: Font.semiBold.of(size: 12),
        textColor: Color.txtParagraph
    )

    private lazy var tagLabel = CUIPaddingLabel(
        font: Font.semiBold.of(size: 10),
        textColor: Color.txtInverted
    )

    private lazy var dotImageView = ThemeableImageView(
        contentMode: .scaleAspectFit,
        image: .image(named: "icondot"),
        themeType: .textHighlight
    )

    private lazy var colorStackView = UIStackView(
        axis: .horizontal,
        distribution: .fillEqually,
        spacing: 0
    )

    private lazy var colorViews = [
        UIView(), UIView(), UIView(), UIView()
    ]

    public weak var delegate: ThemeListCellProtocol?
    private var indexPath = IndexPath(row: 0, section: 0)
    private var data: CustomThemeModel?

    public override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: .default, reuseIdentifier: reuseIdentifier)
        initUIComponents()
        bindAction()
    }

    public required init?(coder: NSCoder) {
        super.init(
            style: .default,
            reuseIdentifier: String(describing: ThemeListCell.self)
        )
        initUIComponents()
        bindAction()
    }

    private func initUIComponents() {
        selectionStyle = .none
        self.backgroundColor = .clear
        contentView.backgroundColor = .clear

        contentView.addSubview(container)
        container.snp.makeConstraints { make in
            make.top.equalTo(8)
            make.left.equalTo(12)
            make.right.equalTo(-12)
            make.bottom.equalToSuperview()
            make.height.equalTo(64)
        }

        container.addSubviews([
            titleLabel, tagLabel, dotImageView,
            colorStackView, bgButton, switchButton
        ])
        switchButton.snp.makeConstraints { make in
            make.top.equalTo(8)
            make.left.equalTo(12)
        }
        bgButton.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        titleLabel.snp.makeConstraints { make in
            make.centerY.equalTo(switchButton)
            make.left.equalTo(switchButton.snp.right).offset(8)
        }
        tagLabel.roundCorners(radius: 2)
        tagLabel.backgroundColor = Color.bgDefaultInverted
        tagLabel.padding(2, 2, 4, 4)
        tagLabel.snp.makeConstraints { make in
            make.centerY.equalTo(switchButton)
            make.left.equalTo(titleLabel.snp.right).offset(8)
            make.right.lessThanOrEqualTo(dotImageView)
        }
        dotImageView.snp.makeConstraints { make in
            make.height.width.equalTo(22)
            make.centerY.equalTo(switchButton)
            make.right.equalTo(-12)
        }
        colorStackView.roundCorners(radius: 5)
        colorStackView.clipsToBounds = true
        colorStackView.snp.makeConstraints { make in
            make.top.equalTo(switchButton.snp.bottom).offset(16)
            make.left.equalTo(10)
            make.right.equalTo(-10)
            make.bottom.lessThanOrEqualToSuperview()
            make.height.equalTo(10)
        }

        colorStackView.addArrangedSubviews(colorViews)
    }

    private func bindAction() {
        switchButton.valueChangeHandler = { [weak self] in
            guard let self = self, let data = self.data else { return }
            self.delegate?.themeListCell(
                onSwitch: $0,
                indexPath: self.indexPath,
                data: data
            )
        }

        bgButton.addTarget(self, action: #selector(self.onTapped), for: .touchUpInside)
    }

    public func configure(_ data: CustomThemeModel, index: IndexPath) {
        self.indexPath = index
        self.data = data

        switchButton.change(isOn: data.isEnable, animated: false)
        titleLabel.text = data.name
        tagLabel.isHidden = data.presetName.isEmpty
        tagLabel.text = "PRESET - \(data.presetName)"

        [
            data.primaryId, data.secondaryId,
            data.thirdId, data.textHighLightId
        ].enumerated().forEach { index, value in
            let themeColor = ThemeColor(rawValue: value)
            colorViews[index].backgroundColor = themeColor?.color ?? .clear
        }
    }

    @objc func onTapped() {
        guard let data = data else { return }
        delegate?.themeListCell(onSelect: indexPath, data: data)
    }
}
