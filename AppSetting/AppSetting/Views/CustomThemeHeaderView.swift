//
//  CustomThemeHeaderView.swift
//  AppSetting
//
//  Created by <PERSON><PERSON><PERSON> on 2/14/67.
//
import UIKit
import SnapKit
import RxSwift
import CUIModule

public class CustomThemeHeaderView: UIView {

    private lazy var stackView = UIStackView(
        axis: .horizontal,
        spacing: 4
    )

    private lazy var titleLabel = UILabel(
        font: Font.medium.of(size: 12),
        textColor: Color.txtLabel
    )

    private lazy var captionLabel = UILabel(
        font: Font.semiBold.of(size: 12),
        textColor: Color.txtTitle
    )

    private lazy var iconView = ThemeableImageView(
        contentMode: .scaleAspectFit,
        image: .image(named: "ic_edit_line"),
        themeType: .textHighlight
    )

    fileprivate let tapGesture = UITapGestureRecognizer()

    public init(title: String? = nil, caption: String? = nil) {
        super.init(frame: .zero)
        setupUI()
        bindActions()
        setData(title: title ?? "", caption: caption ?? "")
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    private func setupUI() {
        self.backgroundColor = Color.bgDefaultToneDarker
        self.roundCorners(radius: 4)

        addSubview(stackView)
        stackView.snp.makeConstraints { make in
            make.top.left.equalTo(12)
            make.bottom.right.equalTo(-12)
            make.height.equalTo(20)
        }

        stackView.addArrangedSubviews([titleLabel, captionLabel, iconView])
        titleLabel.setContentHuggingPriority(.required, for: .horizontal)
        titleLabel.setContentCompressionResistancePriority(.required, for: .horizontal)
        iconView.snp.makeConstraints { make in
            make.width.equalTo(16)
        }
    }

    private func bindActions() {
        self.isUserInteractionEnabled = true
        self.addGestureRecognizer(tapGesture)
    }

    public func setData(title: String, caption: String) {
        titleLabel.text = title
        captionLabel.text = caption
    }
}
// MARK: - Reactive
extension Reactive where Base: CustomThemeHeaderView {

    var tap: Observable<Void> { base.tapGesture.rx.event.mapToVoid() }

}
