//
//  ThemeColorItemView.swift
//  AppSetting
//
//  Created by <PERSON><PERSON><PERSON> on 2/14/67.
//
import UIKit
import SnapKit
import RxSwift
import CUIModule

class ThemeColorItemView: UIView {

    fileprivate let themeColor: ThemeColor
    fileprivate let tapGesture = UITapGestureRecognizer()

    private lazy var selectedIconView = UIImageView(
        contentMode: .scaleAspectFit,
        image: themeColor.selectedImage
    )

    init(themeColor: ThemeColor, isSelected: Bool) {
        self.themeColor = themeColor
        super.init(frame: .zero)
        self.backgroundColor = themeColor.color
        self.roundCorners(radius: 4)

        self.isUserInteractionEnabled = true
        self.addGestureRecognizer(tapGesture)

        addSubview(selectedIconView)
        selectedIconView.isHidden = !isSelected
        selectedIconView.snp.makeConstraints { make in
            make.width.height.equalTo(24)
            make.center.equalToSuperview()
        }
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
}
// MARK: - Reactive
extension Reactive where Base: ThemeColorItemView {

    var tap: Observable<ThemeColor> {
        base.tapGesture.rx.event.map { _ in
            base.themeColor
        }
    }
}
