//
//  ThemeSmartColorView.swift
//  AppSetting
//
//  Created by <PERSON><PERSON><PERSON> on 2/21/67.
//
import UIKit
import SnapKit
import RxSwift
import CUIModule
import Storage

public class ThemeSmartColorView: UIView {

    private lazy var stackView = UIStackView(
        axis: .horizontal,
        alignment: .center,
        spacing: 6
    )

    private lazy var titleLabel = UILabel(
        font: Font.semiBold.of(size: 10),
        text: "SMART COLORS",
        textColor: Color.txtParagraph
    )

    fileprivate lazy var switchButton = CUISwitch(
        config: CUISwitch.Config(
            width: 45,
            height: 20,
            backgroundPositiveColor: Color.bgButtonNeutral,
            backgroundNegativeColor: Color.bgSwitch,
            backgroundDisableColor: Color.bgButtonDisabled,
            cornerRadius: 10,
            itemPadding: 4,
            indicatorPositiveColor: Color.bgDefault,
            indicatorNegativeColor: Color.bgDefault,
            indicatorDisableColor: Color.txtDisabled,
            textConfig: CUISwitch.TextConfig()
        ),
        isOn: LocalPreference.customThemeSmartColor ?? true
    )

    public var isOn: Bool {
        return switchButton.isOn
    }

    private let disposeBag = DisposeBag()

    public init() {
        super.init(frame: .zero)
        setupUI()
        bindActions()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    private func setupUI() {
        self.backgroundColor = .clear
        self.roundCorners(radius: 4)
        self.layer.borderColor = Color.bgDefaultToneDarker.cgColor
        self.layer.borderWidth = 2

        addSubview(stackView)
        stackView.snp.makeConstraints { make in
            make.top.left.equalTo(8)
            make.bottom.right.equalTo(-8)
            make.height.equalTo(28)
        }
        stackView.addArrangedSubviews([titleLabel, switchButton])
    }

    private func bindActions() {
        switchButton.onValueChange.subscribe(onNext: {
            LocalPreference.customThemeSmartColor = $0
        }).disposed(by: disposeBag)
    }
}
