//
//  ThemeCustomStyleSelectionView.swift
//  AppSetting
//
//  Created by <PERSON><PERSON> on 2/14/67.
//
import UIKit
import SnapKit
import RxSwift
import CUIModule

public class ThemeCustomStyleSelectionView: UIView {

    private let iconDown = UIImage.image(named: "icondownarrowhead")
    private let iconUp = UIImage.image(named: "iconuparrowhead")

    private lazy var stackView = UIStackView(
        axis: .horizontal,
        alignment: .center,
        spacing: 6
    )

    private lazy var titleLabel = UILabel(
        font: Font.semiBold.of(size: 10),
        textColor: Color.txtParagraph
    )

    private lazy var colorView = UIView(
        backgroundColor: .clear,
        cornerRadius: 2
    )

    private lazy var arrowView = UIImageView(
        backgroundColor: .clear,
        contentMode: .scaleAspectFit,
        cornerRadius: 2,
        image: iconDown
    )

    private(set) var colorSet: ThemeColorSet
    fileprivate let tapGesture = UITapGestureRecognizer()
    fileprivate var isSelect = false

    public init(colorSet: ThemeColorSet) {
        self.colorSet = colorSet
        super.init(frame: .zero)
        setupUI()
        bindActions()
        setData(colorSet: colorSet)
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    private func setupUI() {
        self.backgroundColor = .clear
        self.roundCorners(radius: 4)
        self.layer.borderColor = Color.bgDefaultToneDarker.cgColor
        self.layer.borderWidth = 2

        addSubview(stackView)
        stackView.snp.makeConstraints { make in
            make.top.left.equalTo(8)
            make.bottom.right.equalTo(-8)
            make.height.equalTo(28)
        }

        stackView.addArrangedSubviews([titleLabel, colorView, arrowView])
        colorView.isHidden = true
        colorView.snp.makeConstraints { make in
            make.width.height.equalTo(20)
        }
        arrowView.snp.makeConstraints { make in
            make.width.height.equalTo(12)
        }
    }

    private func bindActions() {
        self.isUserInteractionEnabled = true
        self.addGestureRecognizer(tapGesture)
    }

    func setData(colorSet: ThemeColorSet) {
        titleLabel.text = colorSet.title
    }

    func setSelected(_ select: Bool) {
        self.isSelect = select
        titleLabel.textColor = select ? Color.txtLabel : Color.txtParagraph
        arrowView.image = select ? iconUp : iconDown
        self.layer.borderColor = select ? Color.txtLabel.cgColor : Color.bgDefaultToneDarker.cgColor
    }

    func updateSelectedColor(_ color: UIColor) {
        colorView.backgroundColor = color
        colorView.isHidden = false
    }
}
// MARK: - Reactive
extension Reactive where Base: ThemeCustomStyleSelectionView {

    var tap: Observable<ThemeColorSet?> {
        base.tapGesture.rx.event.map { _ in
            if base.isSelect { return nil }
            return base.colorSet
        }
    }
}
