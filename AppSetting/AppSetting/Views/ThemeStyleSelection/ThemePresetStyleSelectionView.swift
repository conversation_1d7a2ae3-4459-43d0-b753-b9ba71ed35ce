//
//  ThemePresetStyleSelectionView.swift
//  AppSetting
//
//  Created by <PERSON><PERSON><PERSON> on 2/14/67.
//
import UIKit
import SnapKit
import RxSwift
import CUIModule

public class ThemePresetStyleSelectionView: UIView {

    private lazy var titleLabel = UILabel(
        font: Font.semiBold.of(size: 10),
        textColor: Color.txtTitle
    )

    private lazy var colorStackView = UIStackView(
        axis: .horizontal,
        spacing: 4
    )

    let colorType: ThemeColorPresetType
    fileprivate let tapGesture = UITapGestureRecognizer()
    fileprivate var isSelect = false

    public init(colorType: ThemeColorPresetType) {
        self.colorType = colorType
        super.init(frame: .zero)
        setupUI()
        bindActions()
        setData(colorType: colorType)
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    private func setupUI() {
        self.backgroundColor = .clear
        self.roundCorners(radius: 4)
        self.layer.borderColor = Color.bgDefaultToneDarker.cgColor
        self.layer.borderWidth = 2

        addSubviews([titleLabel, colorStackView])
        titleLabel.snp.makeConstraints { make in
            make.top.left.equalTo(8)
            make.right.equalTo(-8)
        }

        colorStackView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(6)
            make.left.equalTo(8)
            make.right.lessThanOrEqualTo(-8)
            make.bottom.equalTo(-8)
            make.height.equalTo(28)
        }
    }

    private func bindActions() {
        self.isUserInteractionEnabled = true
        self.addGestureRecognizer(tapGesture)
    }

    func setData(colorType: ThemeColorPresetType) {

        titleLabel.text = colorType.title

        colorStackView.removeAllArrangedSubviews()
        let colorSet = colorType.colorSet
        [
            colorSet.primary, colorSet.secondary,
            colorSet.third, colorSet.textHighlight
        ].forEach {
            let colorView = UIView(
                backgroundColor: $0.color,
                cornerRadius: 2
            )
            colorStackView.addArrangedSubview(colorView)
            colorView.snp.makeConstraints { make in
                make.width.equalTo(colorStackView.snp.height)
            }
        }
    }

    func setSelected(_ select: Bool) {
        self.isSelect = select
        self.layer.borderColor = select ? Color.txtLabel.cgColor : Color.bgDefaultToneDarker.cgColor
    }
}
// MARK: - Reactive
extension Reactive where Base: ThemePresetStyleSelectionView {

    var tap: Observable<ThemeColorPresetType> {
        base.tapGesture.rx.event.map { _ in
            return base.colorType
        }
    }
}
