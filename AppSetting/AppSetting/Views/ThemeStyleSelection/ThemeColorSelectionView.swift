//
//  ThemeColorSelectionView.swift
//  AppSetting
//
//  Created by <PERSON><PERSON><PERSON> on 2/14/67.
//
import UIKit
import SnapKit
import RxSwift
import CUIModule

public class ThemeColorSelectionView: UIView {

    private lazy var stackView = UIStackView(
        axis: .vertical,
        spacing: 8
    )

    fileprivate var colorSet: ThemeColorSet?
    let disposeBag = DisposeBag()
    fileprivate let onSelect = PublishSubject<ThemeColor>()

    public init() {
        super.init(frame: .zero)
        setupUI()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    private func setupUI() {
        self.backgroundColor = Color.bgDefault
        self.roundCorners(radius: 4)
        self.dropShadow(
            alpha: 1,
            opacity: currentAppTheme == .light ? 0.1 : 0.75,
            offset: .init(width: 0, height: 0),
            radius: 6
        )

        addSubview(stackView)
        stackView.snp.makeConstraints { make in
            make.top.left.equalTo(8)
            make.bottom.right.equalTo(-8)
        }
    }

    func setData(colorSet: ThemeColorSet, selectedColor: ThemeColor?) {
        self.colorSet = colorSet

        stackView.removeAllArrangedSubviews()

        let itemCountInRow = 5
        var index = 0
        while index < colorSet.colors.count {
            var endIndex = index+(itemCountInRow-1)
            if endIndex >= colorSet.colors.count { endIndex = index }

            addItems(
                items: Array(colorSet.colors[index...endIndex]),
                startIndex: index,
                total: itemCountInRow,
                selectedColor: selectedColor
            )
            index += itemCountInRow
        }
    }

    private func addItems(
        items: [ThemeColor],
        startIndex: Int,
        total: Int,
        selectedColor: ThemeColor?
    ) {
        let stack = UIStackView()
        stack.axis = .horizontal
        stack.distribution = .fillEqually
        stack.spacing = 8

        stackView.addArrangedSubview(stack)

        for index in 0..<items.count {
            let colorView = ThemeColorItemView(
                themeColor: items[index],
                isSelected: items[index] == selectedColor
            )
            stack.addArrangedSubview(colorView)
            
            colorView.snp.makeConstraints { make in
                make.height.equalTo(colorView.snp.width)
            }

            colorView.rx.tap.bind(to: onSelect).disposed(by: disposeBag)
        }

        // Add empty view if items is less than total count
        for _ in 0..<(total - items.count) {
            let view = UIView()
            view.backgroundColor = .clear
            stack.addArrangedSubview(view)
        }
    }
}
// MARK: - Reactive
extension Reactive where Base: ThemeColorSelectionView {

    var select: Observable<(set: ThemeColorSet, color: ThemeColor)> {
        base.onSelect.map {
            (set: base.colorSet ?? .primary, color: $0)
        }
    }
}
