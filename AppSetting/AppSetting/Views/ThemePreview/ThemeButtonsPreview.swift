//
//  ThemeButtonsPreview.swift
//  AppSetting
//
//  Created by <PERSON><PERSON><PERSON> on 2/14/67.
//
import UIKit
import SnapKit
import RxSwift
import CUIModule

public class ThemeButtonsPreview: UIStackView {

    private lazy var primaryButtonView = ThemeButtonView(
        bgColor: ThemeColor.defaultPrimary.color,
        textColor: ThemeColor.defaultPrimary.foregroundColor
    )

    private lazy var secondaryButtonView = ThemeButtonView(
        bgColor: ThemeColor.defaultSecondary.color,
        textColor: ThemeColor.defaultSecondary.foregroundColor
    )

    public init() {
        super.init(frame: .zero)
        setupUI()
    }

    required init(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    private func setupUI() {
        axis = .vertical
        spacing = 6

        addArrangedSubviews([primaryButtonView, secondaryButtonView])
        primaryButtonView.snp.makeConstraints { make in
            make.height.equalTo(18)
        }
        secondaryButtonView.snp.makeConstraints { make in
            make.height.equalTo(18)
        }
    }

    public func updateColor(
        primaryColor: ThemeColor,
        secondaryColor: ThemeColor
    ) {
        updatePrimaryColor(primaryColor)
        updateSecondaryColor(secondaryColor)
    }

    public func updatePrimaryColor(_ color: ThemeColor) {
        primaryButtonView.setColors(
            bgColor: color.color,
            textColor: color.foregroundColor
        )

        primaryButtonView.updateCorner(color.primaryCorner != nil ? 2 : 9)
    }

    public func updateSecondaryColor(_ color: ThemeColor) {
        secondaryButtonView.setColors(
            bgColor: color.color,
            textColor: color.foregroundColor
        )

        secondaryButtonView.updateCorner(color.primaryCorner != nil ? 2 : 9)
    }
}
// MARK: - ThemeButtonView
private class ThemeButtonView: UIView {

    private lazy var contentView = UIView(
        backgroundColor: .clear,
        cornerRadius: 1
    )

    init(bgColor: UIColor, textColor: UIColor) {
        super.init(frame: .zero)
        setupUI()
        setColors(bgColor: bgColor, textColor: textColor)
    }

    required init(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    private func setupUI() {
        roundCorners(radius: 9)

        addSubview(contentView)
        contentView.snp.makeConstraints { make in
            make.height.equalTo(4)
            make.width.equalToSuperview().multipliedBy(0.54)
            make.center.equalToSuperview()
        }
    }

    func updateCorner(_ corner: CGFloat) {
        roundCorners(radius: corner)
    }

    func setColors(bgColor: UIColor, textColor: UIColor) {
        self.backgroundColor = bgColor
        contentView.backgroundColor = textColor
    }

}
