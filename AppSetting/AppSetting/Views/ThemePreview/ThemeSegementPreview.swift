//
//  ThemeSegementPreview.swift
//  AppSetting
//
//  Created by <PERSON><PERSON><PERSON> on 2/14/67.
//
import UIKit
import SnapKit
import RxSwift
import CUIModule

public class ThemeSegementPreview: UIStackView {

    public init() {
        super.init(frame: .zero)
        setupUI()
    }

    private lazy var segmentView = ThemeSegmentView(
        bgColor: Color.themeCustomThird1,
        fgColor: ThemeColor.textHighLight1.color
    )

    required init(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    private func setupUI() {
        axis = .horizontal
        distribution = .fillEqually
        spacing = 0
        
        self.snp.makeConstraints { make in
            make.height.equalTo(18)
        }

        addArrangedSubviews([
            segmentView,
            ThemeSegmentView(bgColor: .clear, fgColor: Color.bgButtonDisabled),
            ThemeSegmentView(bgColor: .clear, fgColor: Color.bgButtonDisabled)
        ])
    }

    public func updateColor(_ color: ThemeColor) {
        segmentView.setColor(fgColor: color.color)
    }
}
// MARK: - ThemeSegmentView
private class ThemeSegmentView: UIView {

    private lazy var contentView = UIView(
        backgroundColor: .clear,
        cornerRadius: 1
    )

    init(bgColor: UIColor, fgColor: UIColor) {
        super.init(frame: .zero)
        setupUI()
        setColors(bgColor: bgColor, fgColor: fgColor)
    }

    required init(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    private func setupUI() {
        roundCorners(radius: 1)

        addSubview(contentView)
        contentView.snp.makeConstraints { make in
            make.height.equalTo(4)
            make.width.equalToSuperview().multipliedBy(0.54)
            make.center.equalToSuperview()
        }
    }

    func setColors(bgColor: UIColor, fgColor: UIColor) {
        self.backgroundColor = bgColor
        setColor(fgColor: fgColor)
    }

    func setColor(fgColor: UIColor) {
        contentView.backgroundColor = fgColor
    }

}
