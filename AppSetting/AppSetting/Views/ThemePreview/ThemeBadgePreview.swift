//
//  ThemeBadgePreview.swift
//  AppSetting
//
//  Created by <PERSON><PERSON><PERSON> on 3/25/67.
//
import UIKit
import SnapKit
import RxSwift
import CUIModule

public class ThemeBadgePreview: UIView {

    private lazy var textView = UIView(
        backgroundColor: Color.txtLabel,
        cornerRadius: 1
    )

    public init() {
        super.init(frame: .zero)
        setupUI()
    }

    required init(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    private func setupUI() {
        backgroundColor = Color.bgButtonNeutralPale

        self.addSubview(textView)
        textView.snp.makeConstraints { make in
            make.height.equalTo(2)
            make.centerY.equalToSuperview()
            make.left.equalTo(2)
            make.right.equalTo(-2)
        }
    }

    public func updateColors(bgColor: ThemeColor, textColor: ThemeColor) {
        updateColor(bgColor: bgColor)
        updateColor(textColor: textColor)
    }

    public func updateColor(bgColor: ThemeColor) {
        self.backgroundColor = bgColor.color
    }

    public func updateColor(textColor: ThemeColor) {
        textView.backgroundColor = textColor.color
    }
}
