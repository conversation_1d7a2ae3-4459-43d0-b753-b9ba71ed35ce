//
//  ThemePreview.swift
//  AppSetting
//
//  Created by <PERSON><PERSON><PERSON> on 2/14/67.
//
import UIKit
import SnapKit
import RxSwift
import CUIModule
import Storage

public class ThemePreview: UIView {

    private lazy var stackView = UIStackView(
        axis: .vertical,
        spacing: 16
    )

    private lazy var segmentsView = ThemeSegementPreview()

    private lazy var textsView = ThemeTextPreview()

    private lazy var buttonsView = ThemeButtonsPreview()

    public init() {
        super.init(frame: .zero)
        setupUI()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    private func setupUI() {
        self.backgroundColor = Color.bgDefault
        self.roundCorners(radius: 10)
        self.layer.borderColor = Color.bgDefaultInverted.cgColor
        self.layer.borderWidth = 3

        addSubview(stackView)
        stackView.snp.makeConstraints { make in
            make.top.greaterThanOrEqualTo(36)
            make.left.equalTo(11)
            make.right.equalTo(-11)
            make.bottom.lessThanOrEqualTo(-36)
            make.centerY.equalToSuperview()
        }

        stackView.addArrangedSubviews([
            segmentsView, textsView, buttonsView
        ])
    }

    public func updateColors(_ data: CustomThemeModel) {
        segmentsView.updateColor(ThemeColor(rawValue: data.textHighLightId) ?? .defaultTextHighLight)
        updateBadgeColor(
            bgColor: ThemeColor(rawValue: data.thirdId) ?? .defaultThird,
            textColor: ThemeColor(rawValue: data.textHighLightId) ?? .defaultTextHighLight
        )
        updateTextColor(ThemeColor(rawValue: data.textHighLightId) ?? .defaultTextHighLight)
        updatePrimaryColor(ThemeColor(rawValue: data.primaryId) ?? .defaultPrimary)
        updateSecondaryColor(ThemeColor(rawValue: data.secondaryId) ?? .defaultSecondary)
    }

    public func updateTextColor(_ color: ThemeColor) {
        textsView.updateColors(color)
        segmentsView.updateColor(color)
        backgroundColor = color.bgDefaultColor
    }

    public func updateBadgeColor(bgColor: ThemeColor, textColor: ThemeColor) {
        textsView.updateBadgeColor(bgColor: bgColor, textColor: textColor)
    }

    public func updatePrimaryColor(_ color: ThemeColor) {
        buttonsView.updatePrimaryColor(color)
    }

    public func updateSecondaryColor(_ color: ThemeColor) {
        buttonsView.updateSecondaryColor(color)
    }
}
