//
//  ThemeTextPreview.swift
//  AppSetting
//
//  Created by <PERSON><PERSON><PERSON> on 2/14/67.
//
import UIKit
import SnapKit
import RxSwift
import CUIModule

public class ThemeTextPreview: UIStackView {

    private lazy var badgeView = ThemeBadgePreview()

    private var textsViews: [ThemeTextView] = []

    public init() {
        super.init(frame: .zero)
        setupUI()
    }

    required init(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    private func setupUI() {
        axis = .vertical
        spacing = 12

        addTitle()
        addParagraph()
        addParagraph()
        addParagraph()
    }

    private func addTitle() {
        let stackView = UIStackView(
            axis: .horizontal,
            spacing: 4
        )

        let contentView = UIView(
            backgroundColor: Color.bgButtonDisabled,
            cornerRadius: 1
        )

        stackView.addArrangedSubviews([contentView, badgeView, UIView()])
        contentView.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.74)
        }
        badgeView.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.17)
        }

        self.addArrangedSubview(stackView)
        stackView.snp.makeConstraints { make in
            make.height.equalTo(6)
        }
    }

    private func addParagraph() {

        let highlightColor = ThemeColor.textHighLight1.color

        let textsView = ThemeTextView(lines: [
            .init(
                highlightColor: highlightColor,
                normalColor: Color.bgButtonDisabled,
                highlightIndex: 1,
                firstLineRatios: 0.55
            ),
            .init(
                highlightColor: highlightColor,
                normalColor: Color.bgButtonDisabled,
                highlightIndex: 0,
                firstLineRatios: 0.26
            ),
            .init(
                highlightColor: .clear,
                normalColor: Color.bgButtonDisabled,
                highlightIndex: nil,
                firstLineRatios: 0.39
            )
        ])
        self.addArrangedSubview(textsView)
        textsViews.append(textsView)
    }

    public func updateColors(_ color: ThemeColor) {
        textsViews.forEach {
            $0.setColors(color.color)
        }
        badgeView.updateColor(textColor: color)
    }

    public func updateBadgeColor(bgColor: ThemeColor, textColor: ThemeColor) {
        badgeView.updateColors(bgColor: bgColor, textColor: textColor)
    }

}
// MARK: - ThemeButtonView
private class ThemeTextView: UIStackView {

    private var highlightViews: [UIView] = []

    init(lines: [SentenceModel]) {
        super.init(frame: .zero)
        setupUI()
        lines.forEach {
            addSentence(model: $0)
        }
    }

    required init(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    private func setupUI() {
        axis = .vertical
        spacing = 5
    }

    private func addSentence(model: SentenceModel) {
        let stackView = UIStackView(
            axis: .horizontal,
            spacing: 4
        )

        let colors = model.highlightIndex == 0 ? [model.highlightColor, model.normalColor] : [model.normalColor, model.highlightColor]

        colors.enumerated().forEach { index, value in

            let contentView = UIView(
                backgroundColor: value,
                cornerRadius: 1
            )
            stackView.addArrangedSubview(contentView)
            if index == 0 {
                contentView.snp.makeConstraints { make in
                    make.width.equalToSuperview().multipliedBy(model.firstLineRatios)
                }
            }

            if index == model.highlightIndex {
                highlightViews.append(contentView)
            }
        }

        self.addArrangedSubview(stackView)
        stackView.snp.makeConstraints { make in
            make.height.equalTo(4)
        }
    }

    public func setColors(_ color: UIColor) {
        highlightViews.forEach { $0.backgroundColor = color }
    }

}
extension ThemeTextView {

    struct SentenceModel {
        let highlightColor: UIColor
        let normalColor: UIColor
        let highlightIndex: Int?
        let firstLineRatios: CGFloat
    }
}
