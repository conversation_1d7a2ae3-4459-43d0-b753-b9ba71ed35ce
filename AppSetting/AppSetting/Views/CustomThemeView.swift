//
//  CustomThemeView.swift
//  AppSetting
//
//  Created by <PERSON><PERSON><PERSON> on 2/9/67.
//
import UIKit
import SnapKit
import RxSwift
import CUIModule

public class CustomThemeView: UIView {

    private lazy var container = UIView()

    private lazy var titleStackView = UIStackView(
        axis: .horizontal,
        spacing: 4
    )

    fileprivate lazy var switchButton = CUISwitch(config: CUISwitch.Config(
        width: 57, height: 23,
        backgroundPositiveColor: Color.bgButtonPositive,
        backgroundNegativeColor: Color.bgButtonNegative,
        backgroundDisableColor: Color.bgButtonDisabled,
        cornerRadius: 11.5,
        itemPadding: 5.5,
        indicatorPositiveColor: Color.bgDefault,
        indicatorNegativeColor: Color.bgDefault,
        indicatorDisableColor: Color.txtDisabled,
        textConfig: CUISwitch.TextConfig(),
        textPadding: 9.5
    ))

    private lazy var titleLabel = UILabel(
        font: Font.bold.of(size: 16),
        text: "CUSTOM THEME",
        textColor: UIColor(hexString: "#FFFFFF")
    )

    private lazy var descLabel = UILabel(
        font: Font.medium.of(size: 12),
        numberOfLines: 0,
        text: "Create a customized theme that fits with your style!",
        textColor: UIColor(hexString: "#FFFFFF")
    )

    fileprivate lazy var themeButton = UIButton(
        backgroundColor: .clear,
        titleFont: Font.semiBold.of(size: 12),
        title: "CREATE THEME",
        normalTitleColor: UIColor(hexString: "#FFFFFF"),
        cornerRadius: 16,
        borderColor: UIColor(hexString: "#FFFFFF"),
        borderWidth: 1
    )

    private var viewDidMoved = false
    private var state: State?
    fileprivate var hasCustomTheme: Bool?

    public init() {
        super.init(frame: .zero)
        setupUI()
        bindActions()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    public override func layoutSubviews() {
        super.layoutSubviews()
        if viewDidMoved { return }
        viewDidMoved = true
        setContainerStyle()
    }

    private func setupUI() {
        addSubviews([container, titleStackView, descLabel, themeButton])

        container.snp.makeConstraints { make in
            make.top.left.equalTo(3)
            make.bottom.right.equalTo(-3)
        }

        titleStackView.snp.makeConstraints { make in
            make.top.left.equalTo(12)
            make.right.equalTo(-12)
        }

        titleStackView.addArrangedSubviews([titleLabel, switchButton])
        switchButton.layer.borderColor = UIColor(hexString: "#FFFFFF").cgColor
        switchButton.layer.borderWidth = 1.5

        descLabel.snp.makeConstraints { make in
            make.top.equalTo(titleStackView.snp.bottom).offset(6)
            make.left.equalTo(12)
            make.right.equalTo(-12)
        }

        themeButton.snp.makeConstraints { make in
            make.top.equalTo(descLabel.snp.bottom).offset(16)
            make.centerX.equalToSuperview()
            make.bottom.equalTo(-12)
            make.width.equalTo(130)
            make.height.equalTo(32)
        }
    }

    private func setContainerStyle() {
        // Border Gradient
        self.roundCorners(radius: 4)
        self.addGradient(
            colors: [UIColor(hexString: "#63ADF1"), UIColor(hexString: "#783DD8")],
            locations: [0, 1],
            isLeftToRight: false,
            startPoint: CGPoint(x: 0, y: 0.5),
            endPoint: CGPoint(x: 1, y: 0.5)
        )
        // Container Gradient
        container.roundCorners(radius: 2)
        container.addGradient(
            colors: [UIColor(hexString: "#63ADF1"), UIColor(hexString: "#783DD8")],
            locations: [0, 1],
            isLeftToRight: true,
            startPoint: CGPoint(x: 0, y: 0.5),
            endPoint: CGPoint(x: 1, y: 0.5)
        )
    }

    private func bindActions() {
        
    }

    public func setTitle(hasCustomTheme: Bool) {
        self.hasCustomTheme = hasCustomTheme
        themeButton.setTitle(hasCustomTheme ? "EDIT THEME" : "CREATE THEME", for: .normal)
    }

    public func setState(state: State) {
        self.state = state
        switch state {
        case .turnOn:
            switchButton.setEnable(true)
            switchButton.change(isOn: true)
        case .turnOff:
            switchButton.setEnable(true)
            switchButton.change(isOn: false)
        case .disable:
            switchButton.setEnable(false)
        }
    }
}
// MARK: - Display Model
extension CustomThemeView {
    public enum State {
        case turnOn
        case turnOff
        case disable
    }
}
// MARK: - Reactive
extension Reactive where Base: CustomThemeView {

    var createTheme: Observable<Void> {
        base.themeButton.rx.tap
            .filter { 
                !(base.hasCustomTheme ?? false)
            }
            .mapToVoid()
    }

    var editTheme: Observable<Void> {
        base.themeButton.rx.tap
            .filter {
                base.hasCustomTheme ?? false
            }
            .mapToVoid()
    }

    var activate: Observable<Bool> {
        base.switchButton.onValueChange
    }
}
