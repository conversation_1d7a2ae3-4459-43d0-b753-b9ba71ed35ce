//
//  CustomThemeOptionButton.swift
//  AppSetting
//
//  Created by <PERSON><PERSON><PERSON> on 2/25/67.
//
import CUIModule
import UIKit
import SnapKit
import RxSwift
import SharedData

class CustomThemeOptionButton: UIView {

    private lazy var stackView = UIStackView(
        axis: .horizontal,
        alignment: .center,
        spacing: 4
    )

    private lazy var switchButton = CUISwitch(config: CUISwitch.Config(
        width: 28,
        height: 18,
        backgroundPositiveColor: Color.bgButtonPositive,
        backgroundNegativeColor: Color.txtLabelNeutral,
        cornerRadius: 9,
        itemPadding: 3
    ))

    private lazy var iconView = ThemeableImageView(
        contentMode: .scaleAspectFit,
        themeType: .textHighlight
    )

    private lazy var titleLabel = UILabel(
        font: Font.semiBold.of(size: 12)
    )

    private var option: OptionType
    fileprivate let tapGesture = UITapGestureRecognizer()

    public init(option: OptionType) {
        self.option = option
        super.init(frame: .zero)
        setupUI()
        setData(option: option)
    }

    required init(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    private func setupUI() {

        self.isUserInteractionEnabled = true
        self.addGestureRecognizer(tapGesture)

        self.backgroundColor = Color.bgDefaultTone
        self.roundCorners(radius: 2)

        addSubview(stackView)
        stackView.snp.makeConstraints { make in
            make.top.bottom.equalToSuperview()
            make.left.equalTo(16)
            make.right.equalTo(16)
        }

        stackView.addArrangedSubviews([switchButton, iconView, titleLabel])
        iconView.snp.makeConstraints { make in
            make.width.height.equalTo(20)
        }
        switchButton.isUserInteractionEnabled = false
    }

    func setData(option: OptionType) {

        switch option {
        case .activate:
            stackView.spacing = 8
            stackView.snp.updateConstraints { make in
                make.left.equalTo(12)
                make.right.equalTo(-12)
            }
            switchButton.isHidden = false
            iconView.isHidden = true

        case .edit, .delete:
            stackView.spacing = 4
            stackView.snp.updateConstraints { make in
                make.left.equalTo(16)
                make.right.equalTo(-16)
            }
            switchButton.isHidden = true
            iconView.isHidden = false
            iconView.image = option.icon
        }

        titleLabel.text = option.title
        titleLabel.textColor = option.titleColor
    }

    func setActivate(_ isActivated: Bool) {
        switchButton.change(isOn: isActivated)
    }
}
// MARK: - Display Model
extension CustomThemeOptionButton {

    enum OptionType {
        case activate
        case edit
        case delete
        
        var icon: UIImage? {
            switch self {
            case .activate: return nil
            case .edit: return .image(named: "ic_edit")
            case .delete: return .image(named: "icontrash")
            }
        }

        var title: String {
            switch self {
            case .activate: return "ACTIVE"
            case .edit: return "EDIT"
            case .delete: return "DELETE"
            }
        }

        var titleColor: UIColor {
            switch self {
            case .activate: return Color.txtPositive
            case .edit, .delete: return Color.txtParagraph
            }
        }
    }
}
// MARK: - Reactive
extension Reactive where Base: CustomThemeOptionButton {

    var tap: Observable<Void> {
        return base.tapGesture.rx.event.mapToVoid()
    }
}
