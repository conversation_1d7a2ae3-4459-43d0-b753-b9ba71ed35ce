//
//  CustomThemeOptionBottomSheet.swift
//  AppSetting
//
//  Created by <PERSON><PERSON> on 2/25/67.
//
import UIKit
import RxCocoa
import RxSwift
import CUIModule
import FittedSheets
import SharedData
import Storage

public class CustomThemeOptionBottomSheet: UIViewController, BaseBottomSheet {

    private lazy var mainStackView = UIStackView(
        axis: .vertical,
        spacing: 8
    )

    private lazy var titleStackView = UIStackView(
        axis: .horizontal,
        spacing: 4
    )

    private lazy var titleLabel = UILabel(
        font: Font.semiBold.of(size: 14),
        textColor: Color.txtLabel
    )

    fileprivate lazy var activateButton = CustomThemeOptionButton(option: .activate)
    fileprivate lazy var editButton = CustomThemeOptionButton(option: .edit)
    fileprivate lazy var deleteButton = CustomThemeOptionButton(option: .delete)

    private lazy var closeButton = UIButton(
        normalImage: .image(named: "iconcrosssquare")
    )

    public var disposeBagForDismiss = DisposeBag()
    public var onDismiss = PublishSubject<(() -> Void)?>()
    private let disposeBag = DisposeBag()

    fileprivate var data: CustomThemeModel?

    public override func viewDidLoad() {
        super.viewDidLoad()
        setUpViews()
        bindAction()
    }

    private func bindAction() {
        closeButton.rx.tap.subscribe(onNext: { [weak self] in
            self?.onDismiss.onNext(nil)
        }).disposed(by: disposeBag)
    }
}
// MARK: - SetUp Layout
private extension CustomThemeOptionBottomSheet {

    func setUpViews() {
        view.backgroundColor = Color.bgDefault
        view.addSubview(mainStackView)

        mainStackView.snp.makeConstraints { make in
            make.top.left.equalTo(16)
            make.right.equalTo(-16)
            make.bottom.equalTo(-34)
        }

        mainStackView.addArrangedSubviews([
            titleStackView, activateButton,
            editButton, deleteButton
        ])
        mainStackView.setCustomSpacing(16, after: titleStackView)
        [activateButton, editButton, deleteButton].forEach {
            $0.snp.makeConstraints { make in
                make.height.equalTo(44)
            }
        }

        titleStackView.addArrangedSubviews([titleLabel, closeButton])
        closeButton.snp.makeConstraints { make in
            make.width.height.equalTo(24)
        }
    }
}
// MARK: - Update Data
extension CustomThemeOptionBottomSheet {

    public func setData(data: CustomThemeModel) {
        self.data = data
        titleLabel.text = data.name
        setActivate(data.isEnable)
    }

    public func setActivate(_ isActivated: Bool) {
        data?.isEnable = isActivated
        activateButton.setActivate(isActivated)
    }
}
// MARK: - Sheet Config
extension CustomThemeOptionBottomSheet {
    public func customSheetOptions() -> SheetOptions? {
        return .init(SheetOptions(
            pullBarHeight: .zero,
            shrinkPresentingViewController: false
        ))
    }

    public func customSheetConfig() -> SheetConfig {
        return SheetConfig(
            gripSize: .zero,
            cornerRadius: 8,
            dismissOnOverlayTap: true,
            allowPullingPastMaxHeight: false,
            overlayColor: UIColor.black.withAlphaComponent(currentAppTheme == .light ? 0.25 : 0.5)
        )
    }
}
// MARK: - Reactive
extension Reactive where Base: CustomThemeOptionBottomSheet {

    var activate: Observable<CustomThemeModel> {
        return base.activateButton.rx.tap
            .compactMap { base.data }
            .onNext { base.setActivate(!$0.isEnable) }
    }
    var edit: Observable<CustomThemeModel> {
        return base.editButton.rx.tap
            .compactMap { base.data }
            .onNext { _ in base.onDismiss.onNext(nil) }
    }
    var delete: Observable<CustomThemeModel> {
        return base.deleteButton.rx.tap
            .compactMap { base.data }
            .onNext { _ in base.onDismiss.onNext(nil) }
    }
}
