//
//  SettingItemView.swift
//  AppSetting
//
//  Created by <PERSON><PERSON><PERSON> on 5/27/67.
//
import CUIModule
import RxSwift
import Core
import Storage
import SharedData

final class SettingItemView: UIView, AnyView {

    private lazy var stackView = UIStackView(
        axis: .horizontal,
        alignment: .center,
        spacing: 6
    )

    private lazy var iconView = UIImageView(
        contentMode: .scaleAspectFit
    )

    private lazy var titleLabel = UILabel(
        font: Font.medium.of(size: 14),
        textColor: Color.txtTitle
    )

    private lazy var arrowIconView = UIImageView(
        contentMode: .scaleAspectFit, 
        image: .image(named: "merit_arrow_14")
    )

    fileprivate let tapGesture = UITapGestureRecognizer()

    init(icon: UIImage, title: String) {
        super.init(frame: .zero)
        setupUI()
        bindActions()
        setData(icon: icon, title: title)
    }

    required init(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    func setupUI() {
        addSubview(stackView)
        stackView.snp.makeConstraints { make in
            make.top.bottom.equalToSuperview()
            make.left.equalTo(4)
            make.right.equalTo(-4)
        }

        stackView.addArrangedSubviews([iconView, titleLabel, arrowIconView])
        stackView.setCustomSpacing(8, after: titleLabel)
        iconView.snp.makeConstraints { make in
            make.width.equalTo(14)
        }
        arrowIconView.snp.makeConstraints { make in
            make.width.equalTo(14)
        }
    }

    func bindActions() {
        isUserInteractionEnabled = true
        addGestureRecognizer(tapGesture)
    }

    public func setData(icon: UIImage, title: String) {
        iconView.image = icon
        titleLabel.text = title
    }
}
// MARK: - Reactive
extension Reactive where Base: SettingItemView {

    var tap: Observable<Void> {
        base.tapGesture.rx.event.mapToVoid()
    }
}
