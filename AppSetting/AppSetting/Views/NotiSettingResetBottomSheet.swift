//
//  NotiSettingResetBottomSheet.swift
//  AppSetting
//
//  Created by <PERSON><PERSON> on 2/20/67.
//
import UIKit
import RxSwift
import RxCocoa
import Core
import Utils
import FittedSheets
import SharedData
import CUIModule

public class NotiSettingResetBottomSheet: UIViewController, AnyView, BaseBottomSheet {
    // MARK: UI properties

    /// Content Stack View
    private lazy var contentStackView = UIStackView(
        axis: .vertical,
        spacing: 16.0
    )

    /// Title Label
    private lazy var titleLabel = UILabel(
        font: Font.semiBold.of(size: 14.0),
        text: "RESET TO DEFAULT?",
        textColor: Color.txtTitle,
        textAlignment: .left
    )

    /// Description Label
    private lazy var descriptionLabel = UILabel(
        font: Font.medium.of(size: 12.0),
        numberOfLines: 0,
        text: "You are about to reset all notifications settings back to default. Please note that this action is irreversible. \n\nWould you like to proceed?",
        textColor: Color.txtParagraph
    )

    /// Button Stack View
    private lazy var buttonsStackView = UIStackView(
        distribution: .fillEqually,
        spacing: 8.0
    )

    /// Cancel Button
    private lazy var cancelButton = ThemeableButton(
        titleFont: Font.bold.of(size: 16),
        title: "key0273".localized(),
        normalTitleColor: Color.txtTitle,
        normalBgColor: Color.bgDefault,
        cornerRadius: 22,
        borderColor: Color.txtTitle,
        borderWidth: 1.0,
        isEnable: true
    )

    /// Clear Button
    private lazy var resetButton = ThemeableButton(
        titleFont: Font.bold.of(size: 16),
        title: "RESET",
        normalTitleColor: Color.txtInverted,
        normalBgColor: Color.bgButtonNeutral,
        cornerRadius: 22,
        isEnable: true,
        themeType: .secondary
    )

    let disposeBag = DisposeBag()

    public var disposeBagForDismiss = DisposeBag()
    public var onDismiss = PublishSubject<(() -> Void)?>()
    public var onTapReset = PublishSubject<Void>()

    public override func viewDidLoad() {
        super.viewDidLoad()
        self.setupUI()
        self.bindActions()
    }

    public func setupUI() {
        view.backgroundColor = Color.bgDefault
        view.addSubview(contentStackView)
        // Content Stack View
        contentStackView.addArrangedSubviews([
            titleLabel,
            descriptionLabel,
            buttonsStackView
        ])
        contentStackView.snp.makeConstraints { make in
            make.top.left.equalTo(16.0)
            make.right.equalTo(-16.0)
            make.bottom.equalTo(view.safeAreaLayoutGuide)
        }
        // Button Stack View
        buttonsStackView.addArrangedSubviews([cancelButton, resetButton])
        buttonsStackView.snp.makeConstraints { make in
            make.height.equalTo(44)
        }
    }

    public func bindActions() {
        cancelButton.rx.tap.subscribe(onNext: { [weak self] in
            self?.onDismiss.onNext(nil)
        }).disposed(by: disposeBag)
        resetButton.rx.tap.subscribe(onNext: { [weak self] in
            self?.onTapReset.onNext(())
            self?.onDismiss.onNext(nil)
        }).disposed(by: disposeBag)
    }
}
// MARK: - Sheet Config
extension NotiSettingResetBottomSheet {
    public func customSheetOptions() -> SheetOptions? {
        return .init(SheetOptions(
            pullBarHeight: .zero,
            shrinkPresentingViewController: false
        ))
    }

    public func customSheetConfig() -> SheetConfig {
        return SheetConfig(
            gripSize: .zero,
            cornerRadius: 8,
            dismissOnOverlayTap: true,
            allowPullingPastMaxHeight: false,
            overlayColor: UIColor.black.withAlphaComponent(currentAppTheme == .light ? 0.25 : 0.5)
        )
    }
}
