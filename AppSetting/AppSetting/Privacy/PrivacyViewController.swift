//
//  PrivacyViewController.swift
//  AppSetting
//
//  Created by <PERSON><PERSON><PERSON> on 2/8/67.
//
import Core
import CUIModule
import APILayer
import RxSwift
import Storage
import SharedData

final class PrivacyViewController: BaseViewController, AnyView {

    private lazy var headerView = {
        let displayModel = CUINavigationBar.DisplayModel(leftIcon: .image(named: "merit_ic_back"),
                                                         titleView: titleStackView)
        let headerView = CUINavigationBar(displayModel: displayModel)
        
        return headerView
    }()
    
    private lazy var titleStackView = {
        let stackView = UIStackView(alignment: .center,
                                    spacing: 4)
        stackView.addArrangedSubviews([iconView,
                                       titleLabel])
        
        return stackView
    }()
    
    private lazy var iconView = UIImageView(image: AppSetting.privacyPolicy.icon)
    
    private lazy var titleLabel = UILabel(font: Font.semiBold.of(size: 14),
                                          text: AppSetting.privacyPolicy.title,
                                          textColor: Color.txtTitle)
    
    private lazy var scrollView = {
        let scrollView = UIScrollView(backgroundColor: .clear,
                                      showsVerticalScrollIndicator: false)
        scrollView.addSubview(contentView)
        contentView.snp.makeConstraints { make in
            make.width.edges.equalToSuperview()
        }
        
        return scrollView
    }()
    
    private lazy var contentView = {
        let view = UIView(backgroundColor: .clear)
        view.addSubview(stackView)
        stackView.snp.makeConstraints { make in
            make.top.equalTo(12)
            make.left.equalTo(12)
            make.right.equalTo(-12)
            make.bottom.equalTo(-34)
        }
        
        return view
    }()
    
    private let stackView = UIStackView(axis: .vertical,
                                        spacing: 8)

    override func viewDidLoad() {
        super.viewDidLoad()
        
        setupUI()
        bindActions()
    }

    func setupUI() {
        view.backgroundColor = Color.bgDefault
        // Header View, Scroll View
        view.addSubviews([scrollView,
                          headerView])
        // Header View
        headerView.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide.snp.top)
            make.left.right.equalToSuperview()
            make.height.equalTo(44)
        }
        scrollView.snp.makeConstraints { make in
            make.top.equalTo(headerView.snp.bottom)
            make.bottom.left.right.equalToSuperview()
        }
        
        Policy.allCases.forEach {
            stackView.addArrangedSubview($0.displayStackView)
        }
    }

    func bindActions() {
        headerView.rx.leftTap.subscribe(onNext: { [weak self] in
            self?.navigationController?.popViewController(animated: true)
        }).disposed(by: disposeBag)
    }
}

// MARK: - Display Model
// swiftlint: disable all
extension PrivacyViewController {
    
    enum Policy: CaseIterable {
        case description
        case infoCollection
        case dataUsage
        case securityMeasures
        case consent
        case dataRetention
        case thirdPartyServices
        case updates

        var title: String {
            switch self {
            case .description:
                return ""
            case .infoCollection:
                return "Information Collection"
            case .dataUsage:
                return "Data Usage"
            case .securityMeasures:
                return "Security Measures"
            case .consent:
                return "Consent"
            case .dataRetention:
                return "Data Retention"
            case .thirdPartyServices:
                return "Third-party Services"
            case .updates:
                return "Updates"
            }
        }
        
        var titleLabel: UILabel? {
            guard !title.isEmpty else { return nil }
            
            return UILabel(font: Font.medium.of(size: 14),
                           text: title,
                           textColor: Color.txtTitle)
        }
        
        var desc: String {
            switch self {
            case .description:
                return "Our trading application is committed to protecting the privacy and security of our customer' personal information. Here's what you need to know about how we handle your data:"
            case .infoCollection:
                return "We collect personal information such as name, contact details, and financial information solely for the purpose of providing our services and complying with regulatory requirements."
            case .dataUsage:
                return "Your information is used to facilitate transactions, provide customer support, and improve our services. We do not sell or rent your personal information to third parties for marketing purposes."
            case .securityMeasures:
                return "We employ industry-standard security measures to protect your data from unauthorized access, alteration, or disclosure."
            case .consent:
                return "By using our trading application, you consent to the collection and use of your personal information as described in this privacy policy."
            case .dataRetention:
                return "We retain your information only for as long as necessary to fulfill the purposes outlined in this policy or as required by law."
            case .thirdPartyServices:
                return "Some features of our application may involve third-party services, and their privacy policies may differ from ours. We encourage you to review their policies before using such features."
            case .updates:
                return "We may update this privacy policy from time to time to reflect changes in our practices or legal requirements. Any updates will be announced accordingly.\n\nBy continuing to use our trading application, you agree to this privacy policy. If you have any questions or concerns about our privacy practices, please contact our customer support."
            }
        }
        
        var descLabel: UILabel {
            UILabel(font: Font.light.of(size: 14),
                    numberOfLines: 0,
                    text: desc,
                    textColor: Color.txtParagraph)
        }
        
        var displayStackView: UIStackView {
            let stackView = UIStackView(axis: .vertical,
                                        spacing: 8)
            if let titleLabel = titleLabel {
                stackView.addArrangedSubview(titleLabel)
            }
            
            stackView.addArrangedSubview(descLabel)
            
            if self != .updates {
                stackView.addArrangedSubview(CUIDivider(canvasHeight: 8))
            }
            
            return stackView
        }
    }
}
// swiftlint: enable all
