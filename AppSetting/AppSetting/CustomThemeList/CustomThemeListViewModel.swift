//
//  CustomThemeListViewModel.swift
//  AppSetting
//
//  Created by <PERSON><PERSON> on 2/22/67.
//
import APILayer
import Core
import RxCocoa
import RxSwift
import RxRelay
import XCoordinator
import SharedData
import Storage
import CUIModule
import DGCharts

public class CustomThemeListViewModel: AnyViewModel {
    let router: UnownedRouter<AppSettingsRoute>

    public struct Input {
        let onViewAppear: Observable<Void>
        let onActivateTheme: Observable<(activate: Bool, data: CustomThemeModel)>
        let onDeleteTheme: Observable<CustomThemeModel>
    }

    public struct Output {
        let displayData: Driver<[CustomThemeModel]>
        let themeCreated: Driver<CustomThemeModel>
        let themeUpdated: Driver<CustomThemeModel>
        let themeActivated: Driver<CustomThemeModel>
    }

    // Input from Coordinator
    let onThemeCreated = PublishSubject<CustomThemeModel>()
    let onThemeUpdated = PublishSubject<CustomThemeModel>()

    init(router: UnownedRouter<AppSettingsRoute>) {
        self.router = router
    }

    public func transform(input: Input) -> Output {

        let viewAppear = input.onViewAppear

        let onActivateTheme = input.onActivateTheme
            .onNext {
                CustomThemeManager.shared.activateTheme(
                    activate: $0.activate,
                    data: $0.data
                )
            }
            .share()

        let themeActivated = onActivateTheme
            .filter { $0.activate }
            .map { $0.data }

        let deleteTheme = input.onDeleteTheme
            .onNext(CustomThemeManager.shared.deleteTheme)
            .mapToVoid()

        let displayData = Observable.merge(
            viewAppear, onActivateTheme.mapToVoid(), deleteTheme
        ).map(CustomThemeManager.shared.getSavedThemes)

        let themeCreated = onThemeCreated
            .delay(.milliseconds(300), scheduler: MainScheduler.instance)

        let themeUpdated = onThemeUpdated
            .delay(.milliseconds(300), scheduler: MainScheduler.instance)

        return Output(
            displayData: displayData.asDriverOnErrorNever(),
            themeCreated: themeCreated.asDriverOnErrorNever(),
            themeUpdated: themeUpdated.asDriverOnErrorNever(),
            themeActivated: themeActivated.asDriverOnErrorNever()
        )
    }

    func navigate(to route: AppSettingsRoute) {
        router.trigger(route)
    }

    func navigateToThemeCreate(data: CustomThemeModel?) {
        navigate(to: .customThemeCreate(
            data: data,
            onCreated: onThemeCreated,
            onUpdated: onThemeUpdated
        ))
    }
}
