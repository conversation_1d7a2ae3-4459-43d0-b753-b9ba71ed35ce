//
//  CustomThemeListViewController.swift
//  AppSetting
//
//  Created by <PERSON><PERSON> on 2/22/67.
//
import Core
import CUIModule
import RxSwift
import RxCocoa
import Utils
import SharedData
import Storage

final class CustomThemeListViewController: BaseViewController, VMView {

    private lazy var navView: CUINavigationBar = {
        let view = CUINavigationBar(
            displayModel: CUINavigationBar.DisplayModel(
                leftIcon: .image(named: "iconback"),
                titleView: titleLabel
            )
        )
        return view
    }()

    private lazy var titleLabel = UILabel(
        font: Font.semiBold.of(size: 14),
        text: "CUSTOM THEME",
        textColor: Color.txtTitle
    )

    private lazy var listTableView: UITableView = {
        let tableView = UITableView(frame: .zero, style: .grouped)
        tableView.backgroundColor = .clear
        tableView.register(ThemeListCell.self)
        tableView.register(ThemeListHeaderView.self)
        tableView.separatorStyle = .none
        tableView.showsVerticalScrollIndicator = false
        tableView.layer.masksToBounds = true
        tableView.layer.cornerRadius = 8
        tableView.contentInset = .init(
            top: 10, left: 0, bottom: -40, right: 0
        )
        tableView.delegate = self
        tableView.dataSource = self
        return tableView
    }()

    private lazy var emptyView = ThemeListEmptyView()

    private lazy var optionBottomSheet = CustomThemeOptionBottomSheet()

    var viewModel: CustomThemeListViewModel!
    private var data: [CustomThemeModel] = []
    private let activateTheme = PublishSubject<(activate: Bool, data: CustomThemeModel)>()

    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        bindActions()
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        navigationBarHidden(true)
    }

    func setupUI() {
        view.backgroundColor = Color.bgDefault

        emptyView.isHidden = true
        view.addSubviews([navView, listTableView, emptyView])
        navView.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide.snp.top)
            make.left.right.equalToSuperview()
        }

        listTableView.snp.makeConstraints { make in
            make.top.equalTo(navView.snp.bottom)
            make.left.right.bottom.equalToSuperview()
        }

        emptyView.snp.makeConstraints { make in
            make.top.equalTo(navView.snp.bottom).offset(UIScreen.main.bounds.height * 0.09)
            make.left.right.equalToSuperview()
        }
    }

    func bindActions() {
        navView.rx.leftTap.subscribe(onNext: { [weak self] in
            self?.navigationController?.popViewController(animated: true)
        }).disposed(by: disposeBag)

        emptyView.rx.createTheme.subscribe(onNext: { [weak self] in
            self?.viewModel.navigateToThemeCreate(data: nil)
        }).disposed(by: disposeBag)

        optionBottomSheet.rx.activate.subscribe(onNext: { [weak self] in
            self?.activateTheme.onNext((activate: !$0.isEnable, data: $0))
        }).disposed(by: disposeBag)

        optionBottomSheet.rx.edit.subscribe(onNext: { [weak self] in
            self?.viewModel.navigateToThemeCreate(data: $0)
        }).disposed(by: disposeBag)
    }

    func bind(viewModel: CustomThemeListViewModel) {
        self.viewModel = viewModel

        let input = CustomThemeListViewModel.Input(
            onViewAppear: rx.viewWillAppear.mapToVoid(),
            onActivateTheme: activateTheme, 
            onDeleteTheme: optionBottomSheet.rx.delete
        )

        let output = viewModel.transform(input: input)

        output.displayData.drive(onNext: { [weak self] in
            self?.data = $0
            self?.displayUI()
        }).disposed(by: disposeBag)

        output.themeCreated.drive(onNext: { [weak self] in
            self?.displayToast(
                title: "A NEW THEME CREATED!",
                desc: "\($0.name) is created successfully."
            )
        }).disposed(by: disposeBag)

        output.themeUpdated.drive(onNext: { [weak self] in
            self?.displayToast(
                title: "A THEME UPDATED!",
                desc: "\($0.name) is updated successfully."
            )
        }).disposed(by: disposeBag)

        output.themeActivated.drive(onNext: { [weak self] in
            self?.displayToast(
                title: "THEME ACTIVATED!",
                desc: "\($0.name) is activated successfully."
            )
        }).disposed(by: disposeBag)
    }
}
// MARK: - Update UI
extension CustomThemeListViewController {

    private func displayUI() {
        listTableView.reloadSections(IndexSet(integer: 0), with: .none)

        listTableView.isHidden = data.isEmpty
        emptyView.isHidden = !data.isEmpty

        navView.backgroundColor = Color.bgDefault
        view.backgroundColor = Color.bgDefault
    }

    private func displayToast(title: String, desc: String) {
        CUIToastView.show(
            data: CUIToastView.DisplayModel(
                title: title, desc: desc,
                titleStyle: CUIToastView.TextStyle(
                    textColor: Color.txtLabel,
                    font: Font.semiBold.of(size: 12)
                ),
                descStyle: CUIToastView.TextStyle(
                    textColor: Color.txtParagraph,
                    font: Font.semiBold.of(size: 10)
                )
            ),
            inView: self.view,
            underView: self.navView
        )
    }
}
// MARK: - UITableViewDataSource, UITableViewDelegate
extension CustomThemeListViewController: UITableViewDataSource, UITableViewDelegate {

    func numberOfSections(in tableView: UITableView) -> Int {
        return 1
    }

    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return data.count
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell: ThemeListCell = tableView.dequeueReusableCell(forIndexPath: indexPath)
        cell.configure(data[indexPath.row], index: indexPath)
        cell.delegate = self
        return cell
    }

    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 72
    }

    func tableView(_ tableView: UITableView, viewForHeaderInSection section: Int) -> UIView? {
        let reuseIdentifier = ThemeListHeaderView.reuseIdentifier
        let header = tableView.dequeueReusableHeaderFooterView(withIdentifier: reuseIdentifier)
        if let header = header as? ThemeListHeaderView {
            header.delegate = self
        }
        return header
    }

    func tableView(_ tableView: UITableView, heightForHeaderInSection section: Int) -> CGFloat {
        return 60
    }
}
// MARK: - ThemeListHeaderViewProtocol
extension CustomThemeListViewController: ThemeListHeaderViewProtocol, ThemeListCellProtocol {

    func themeListHeaderView(onTap view: ThemeListHeaderView) {
        viewModel.navigateToThemeCreate(data: nil)
    }

    func themeListCell(onSwitch value: Bool, indexPath: IndexPath, data: CustomThemeModel) {
        activateTheme.onNext((activate: value, data: data))
    }

    func themeListCell(onSelect indexPath: IndexPath, data: CustomThemeModel) {
        optionBottomSheet.setData(data: data)
        optionBottomSheet.present()
    }
}
