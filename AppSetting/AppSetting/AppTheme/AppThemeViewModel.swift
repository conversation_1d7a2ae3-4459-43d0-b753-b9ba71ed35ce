//
//  AppThemeViewModel.swift
//  AppSetting
//
//  Created by <PERSON><PERSON><PERSON> on 5/2/2567 BE.
//

import APILayer
import Core
import CUIModule
import RxCocoa
import RxSwift
import RxRelay
import XCoordinator
import Storage

public class AppThemeViewModel: AnyViewModel {
    
    private let router: UnownedRouter<AppSettingsRoute>
    
    public struct Input {
        let onViewAppear: Observable<Void>
        let checkCustomTheme: Observable<Void>
    }
    
    public struct Output {
        let loading: Driver<Bool>
        let error: Driver<Error>
        let displayData: Driver<[AppTheme]>
        let updateCustomThemeUI: Driver<(state: CustomThemeView.State, hasThemes: Bool)>
    }

    init(router: UnownedRouter<AppSettingsRoute>) {
        self.router = router
    }
    
    public func transform(input: Input) -> Output {
        let activityIndicator = ActivityIndicator()
        let errorTracker = ErrorTracker()
        
        let displayData = input.onViewAppear
            .map { self.settings }

        let checkCustomTheme = input.checkCustomTheme
            .map {
                var state = CustomThemeView.State.disable
                if let enableCustomTheme = CustomThemeManager.shared.isEnable {
                    state = enableCustomTheme ? .turnOn : .turnOff
                }
                
                return (state: state, hasThemes: CustomThemeManager.shared.hasTheme)
            }

        return Output(
            loading: activityIndicator.asDriver(),
            error: errorTracker.asDriver(),
            displayData: displayData.asDriverOnErrorNever(),
            updateCustomThemeUI: checkCustomTheme.asDriverOnErrorNever()
        )
    }

    func navigate(to route: AppSettingsRoute) {
        router.trigger(route)
    }
}

// MARK: - Localization & Data
extension AppThemeViewModel {
    
    var settings: [AppTheme] {
        return AppTheme.allCases
    }
    
    var versionTitle: String {
        return "VERSION \(Bundle.main.releaseVersionNumber ?? "").\(Bundle.main.buildVersionNumber ?? "")"
    }
}
