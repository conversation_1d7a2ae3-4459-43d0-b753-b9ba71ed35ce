//
//  NotificaitonSettingView.swift
//  Notification
//
//  Created by <PERSON><PERSON><PERSON> on 2/20/67.
//
import UIKit
import Core
import CUIModule
import RxSwift

class NotificaitonSettingView: UIView, AnyView {

    private lazy var stackView = UIStackView(
        axis: .horizontal,
        alignment: .center,
        spacing: 4
    )

    private lazy var titleLabel = UILabel(
        font: Font.semiBold.of(size: 12),
        textColor: Color.txtLabel,
        lines: 0
    )

    fileprivate lazy var switchButton = CUISwitch(config: CUISwitch.Config(
        width: 50,
        height: 20,
        backgroundPositiveColor: Color.bgButtonNeutral,
        backgroundNegativeColor: Color.bgSwitch,
        backgroundDisableColor: Color.bgButtonDisabled,
        cornerRadius: 10,
        itemPadding: 4,
        indicatorPositiveColor: Color.bgDefault,
        indicatorNegativeColor: Color.bgDefault,
        indicatorDisableColor: Color.txtDisabled,
        textConfig: CUISwitch.TextConfig()
    ))

    init(title: String, isOn: Bool?) {
        super.init(frame: .zero)
        setupUI()
        setData(title: title, isOn: isOn)
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    func setupUI() {
        self.backgroundColor = Color.bgDefaultTone
        self.roundCorners(radius: 4)

        addSubview(stackView)
        self.snp.makeConstraints { make in
             make.height.greaterThanOrEqualTo(44)
         }
        stackView.snp.makeConstraints { make in
            make.top.left.equalTo(12)
            make.bottom.right.equalTo(-12)
        }

        stackView.addArrangedSubviews([titleLabel, switchButton])
    }

    public func setData(title: String, isOn: Bool?) {
        titleLabel.text = title
        updateData(isOn: isOn)
    }

    public func updateData(isOn: Bool?) {
        if let isOn = isOn {
            titleLabel.textColor = Color.txtLabel
            switchButton.change(isOn: isOn)
            switchButton.setEnable(true)
        } else {
            titleLabel.textColor = Color.txtDisabled
            switchButton.change(isOn: false)
            switchButton.setEnable(false)
        }
    }
}
// MARK: - Reactive
extension Reactive where Base: NotificaitonSettingView {

    var tap: Observable<Bool> {
        base.switchButton.onValueChange
    }
}
