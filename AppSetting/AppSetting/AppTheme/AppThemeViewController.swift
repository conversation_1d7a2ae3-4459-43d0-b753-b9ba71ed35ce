//
//  AppThemeViewController.swift
//  AppSetting
//
//  Created by <PERSON><PERSON> on 5/2/2567 BE.
//

import Core
import CUIModule
import APILayer
import RxSwift
import Storage

final class AppThemeViewController: BaseViewController, VMView {

    private lazy var headerView = CUINavigationBar()

    private lazy var titleButton = ThemeableButton(
        titleFont: Font.semiBold.of(size: 14),
        title: "THEME",
        normalTitleColor: Color.txtTitle,
        normalImage: .image(named: "iconlightbulb"),
        titleInsets: UIEdgeInsets(top: 0, left: 2, bottom: 0, right: -2),
        imageInsets: UIEdgeInsets(top: 0, left: -2, bottom: 0, right: 2),
        isUserInteractionEnabled: false,
        themeType: .textHighlight(icon: true, text: false)
    )

    private lazy var scrollView = UIScrollView(
        backgroundColor: .clear,
        showsVerticalScrollIndicator: false
    )
    
    private lazy var contentView = UIView(backgroundColor: .clear)

    private let stackView = UIStackView(
        axis: .vertical,
        spacing: 16
    )

    private let buttonStackView = UIStackView(
        axis: .vertical,
        spacing: 4
    )
    
    private lazy var infoView = CUIInfoView()
    
    private lazy var darkModeDescLabel = UILabel()
    
    private lazy var darkModeLabel = UILabel(
        font: Font.semiBold.of(size: 14),
        text: "DARK MODE",
        textColor: Color.txtLabel)
    
    private lazy var devicethemeDescLabel = UILabel()

    private lazy var customThemeView = CustomThemeView()

    private lazy var buttonArrays: [CUICheckboxButton] = []
    
    private lazy var buttonContainerView = UIView(backgroundColor: Color.bgDefault)
    
    private lazy var resetButton = ThemeableButton(
        titleFont: Font.bold.of(size: 16),
        title: "RESET TO DEFAULT",
        normalTitleColor: Color.txtInverted,
        normalBgColor: Color.bgButtonNeutral,
        cornerRadius: 22,
        isEnable: true,
        themeType: .secondary
    )

    var viewModel: AppThemeViewModel!

    init() {
        super.init(nibName: nil, bundle: nil)
    }

    required init?(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        bindActions()
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        navigationBarHidden(true)
        setUpCustomThemeView()
    }

    public override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        buttonContainerView.layer.shadowOpacity = currentAppTheme == .light ? 0.07 : 0.5
        headerView.setupDropShadow()
    }
    
    func setupUI() {
        view.backgroundColor = Color.bgDefault
        // Header View, Scroll View
        view.addSubviews([scrollView, headerView, buttonContainerView])
        // Header View
        headerView.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide)
            make.left.right.equalToSuperview()
        }
        
        scrollView.snp.makeConstraints { make in
            make.top.equalTo(headerView.snp.bottom).offset(16)
            make.left.right.equalToSuperview()
            make.bottom.equalTo(buttonContainerView.snp.top)
        }
        // Content View
        scrollView.addSubview(contentView)
        contentView.snp.makeConstraints { make in
            make.width.edges.equalToSuperview()
        }
        contentView.addSubview(stackView)
        // Stack View
        stackView.addArrangedSubviews(
            [infoView, darkModeDescLabel,
             darkModeLabel, buttonStackView,
             devicethemeDescLabel, customThemeView])
        stackView.snp.makeConstraints { make in
            make.left.equalTo(12)
            make.right.equalTo(-12)
            make.top.bottom.equalToSuperview()
        }
        // Info View
        infoView.snp.makeConstraints { make in
            make.height.equalTo(13)
        }
        // Button Container View
        buttonContainerView.addSubview(resetButton)
        buttonContainerView.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.bottom.equalToSuperview()
        }
        buttonContainerView.dropShadow(
            alpha: 1, opacity: 0.07,
            offset: .init(width: 0, height: -4), radius: 4
        )
        // Reset Button
        resetButton.snp.makeConstraints { make in
            make.top.left.equalTo(12)
            make.right.equalTo(-12)
            make.height.equalTo(44)
            make.bottom.equalTo(buttonContainerView.safeAreaLayoutGuide.snp.bottom).offset(-16)
        }
    }

    func bindActions() {
        headerView.rx.leftTap.subscribe(onNext: { [weak self] in
            self?.backPress()
        }).disposed(by: disposeBag)

        customThemeView.rx.createTheme.subscribe(onNext: { [weak self] in
            if CustomThemeManager.shared.hasTheme {
                self?.viewModel.navigate(to: .customThemeList)
            } else {
                self?.viewModel.navigate(to: .customThemeTutorial)
            }
        }).disposed(by: disposeBag)

        customThemeView.rx.editTheme.subscribe(onNext: { [weak self] in
            self?.viewModel.navigate(to: .customThemeList)
        }).disposed(by: disposeBag)

        customThemeView.rx.activate.subscribe(onNext: { [weak self] in
            // If no themes are activated, route to theme list
            if CustomThemeManager.shared.activatedTheme == nil {
                self?.customThemeView.setState(state: $0 ? .turnOff : .turnOn)
                self?.viewModel.navigate(to: .customThemeList)
            } else {
                CustomThemeManager.shared.enable($0)
            }
        }).disposed(by: disposeBag)
    }

    func bind(viewModel: AppThemeViewModel) {
        self.viewModel = viewModel

        let input = AppThemeViewModel.Input(
            onViewAppear: rx.viewWillAppear.take(1).mapToVoid(),
            checkCustomTheme: rx.viewWillAppear.mapToVoid()
        )

        let output = viewModel.transform(input: input)

        output.displayData.drive(onNext: { [weak self] in
            self?.displayUI($0)
        }).disposed(by: disposeBag)

        resetButton.rx.tap.subscribe(onNext: { [weak self] in
            self?.updateButton(.device)
        }).disposed(by: disposeBag)
    }

    private func updateButton(_ theme: AppTheme) {
        LocalPreference.appTheme = theme.rawValue
        keyWindow?.set(theme)
        buttonArrays.forEach { button in
            let titleColor: UIColor = button.tag == theme.rawValue ? Color.txtTitle : Color.txtLabel
            let icon = button.tag == theme.rawValue ? "icon" : "icoff"
            button.updateData(titleColor: titleColor, icon: icon)
        }
    }
    
    private func displayUI(_ items: [AppTheme]) {
        let displayModel = CUINavigationBar.DisplayModel(leftIcon: .image(named: "iconback"),
                                                         titleView: titleButton,
                                                         rightIcon: UIImage())
        headerView.updateUI(with: displayModel)

        let infoDisplayModel = CUIInfoView.DisplayModel(
            titleTextConfig: TextConfig(
                text: "What is Dark Mode?",
                font: Font.semiBold.of(size: 12),
                textColor: Color.txtLabel),
            image: UIImage.image(named: "icquestion"),
            backgroundColor: .clear,
            cornerRadius: 4
        )
        infoView.updateUI(displayModel: infoDisplayModel)

        // swiftlint: disable line_length
        darkModeDescLabel.highlight(
            "Dark mode is a display setting in software applications, websites, or operating systems where the background is primarily dark or black, and text and other elements are displayed in light colors.\n\nDark mode is beneficial to reduce your eye strain, conserve the device’s battery, and accessibility in cases.",
            normalColor: Color.txtParagraph,
            normalFont: Font.medium.of(size: 12),
            highlightText: ["reduce your eye strain, conserve the device’s battery", "accessibility"],
            highlightColor: Color.txtTitle,
            highlightFont: Font.semiBold.of(size: 12),
            lineSpacing: 1.07)
        // swiftlint: enable line_length
        devicethemeDescLabel.highlight(
            "The \"SAME AS DEVICE THEME\" option will adjust the app’s theme based on your device settings.",
            normalColor: Color.txtParagraph,
            normalFont: Font.medium.of(size: 12),
            highlightText: ["\"SAME AS DEVICE THEME\""],
            highlightColor: Color.txtLabel,
            highlightFont: Font.semiBold.of(size: 12),
            lineSpacing: 1.07)
        buttonStackView.removeArrangedSubViews()
        items.forEach { item in
            let currentTheme = AppTheme(rawValue: LocalPreference.appTheme ?? AppTheme.default.rawValue)
            let button = CUICheckboxButton()
            let icon = currentTheme == item ? "icon" : "icoff"
            button.setData(id: item.rawValue,
                           title: item.title,
                           titleColor: currentTheme == item ? Color.txtTitle : Color.txtLabel,
                           description: "",
                           icon: icon)
            button.snp.makeConstraints { make in
                make.height.equalTo(44)
            }
            button.action.subscribe(onNext: { [unowned self] in
                LocalPreference.appTheme = item.rawValue
                keyWindow?.set(item)
                updateButton(item)
            }).disposed(by: disposeBag)
            buttonArrays.append(button)
            buttonStackView.addArrangedSubview(button)
        }
    }

    private func setUpCustomThemeView() {
        if CustomThemeManager.shared.hasTheme {
            let isCustomThemeOn = CustomThemeManager.shared.isEnable ?? false
            customThemeView.setState(state: isCustomThemeOn ? .turnOn : .turnOff)
            customThemeView.setTitle(hasCustomTheme: true)
        } else {
            customThemeView.setState(state: .disable)
            customThemeView.setTitle(hasCustomTheme: false)
        }
    }
}
