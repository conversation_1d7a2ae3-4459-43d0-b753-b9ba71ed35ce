//
//  AddEditBankAccountViewController.swift
//  AppSetting
//
//  Created by <PERSON> on 19/12/24.
//

import UIKit
import Core
import CUIModule
import SharedData
import RxSwift
import APILayer

final class AddEditBankAccountViewController: BaseViewController, VMView {
    
    // MARK: UI properties
    private lazy var headerView = {
        let displayModel = CUINavigationBar.DisplayModel(leftIcon: .image(named: "merit_ic_back"),
                                                         titleView: titleStackView)
        let view = CUINavigationBar(displayModel: displayModel)
        
        return view
    }()
    
    private lazy var titleStackView = {
        let stackView = UIStackView(alignment: .center,
                                    spacing: 4)
        stackView.addArrangedSubviews([titleImgView,
                                       titleLabel])
        
        return stackView
    }()
    
    private lazy var titleImgView = UIImageView(image: .image(named: "ic_plus_title"))
    
    private lazy var titleLabel = UILabel(font: Font.semiBold.of(size: 12),
                                          text: "key0939".localized(),
                                          textColor: Color.txtTitle)
    
    
    private lazy var scrollView = {
        let scrollView = UIScrollView(showsVerticalScrollIndicator: false)
        scrollView.addSubview(contentView)
        contentView.snp.makeConstraints { make in
            make.edges.width.equalToSuperview()
            make.width.equalTo(UIScreen.main.bounds.width)
        }
        
        return scrollView
    }()
    
    private lazy var contentView = {
        let view = UIView()
        view.addSubview(contentStackView)
        contentStackView.snp.makeConstraints { make in
            make.top.left.equalTo(12)
            make.bottom.right.equalTo(-12).priority(.high)
        }
        
        return view
    }()
    
    private lazy var contentStackView = UIStackView(axis: .vertical,
                                                    spacing: 12)
    
    private lazy var saveButton = {
        return UIButton(type: .system,
                        backgroundColor: Color.btn4th,
                        titleFont: Font.medium.of(size: 14),
                        title: "key0753".localized(),
                        normalTitleColor: Color.txtInverted,
                        disabledTitleColor: Color.txtDisabled,
                        cornerRadius: 22,
                        titleInsets: UIEdgeInsets(left: 2, right: -2),
                        imageInsets: UIEdgeInsets(left: -2, right: 2),
                        isEnable: false)
    }()
    
    // MARK: Properties
    var viewModel: AddEditBankAccountViewModel!
    private var bankAccount = BankAccountModel()
    
    private let updateBankAccount = PublishSubject<BankAccountModel>()
    
    // MARK: Life cycle
    override func viewDidLoad() {
        super.viewDidLoad()
        
        setupUI()
        bindActions()
        bindViewModelLogic()
    }
    
    func setupUI() {
        view.backgroundColor = Color.bgDefault
        
        view.addSubviews([scrollView,
                          headerView,
                          saveButton])
        
        headerView.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide)
            make.left.right.equalToSuperview()
        }
        
        saveButton.snp.makeConstraints { make in
            make.left.equalTo(12)
            make.right.equalTo(-12)
            make.bottom.equalTo(view.safeAreaLayoutGuide).offset(-16)
            make.height.equalTo(44)
        }
        
        scrollView.snp.makeConstraints { make in
            make.top.equalTo(headerView.snp.bottom)
            make.left.right.equalToSuperview()
            make.bottom.equalTo(saveButton.snp.top).offset(-12).priority(.high)
        }
        
        for field in InputField.allCases {
            let itemView = field.view()
            contentStackView.addArrangedSubview(itemView)
        }
    }
    
    func bindActions() {
        handleFieldsInput()
        
        headerView.rx.leftTap
            .subscribe(onNext: { [unowned self] in
                backPress()
            }).disposed(by: disposeBag)
        
        saveButton.rx.tap
            .subscribe(onNext: { [unowned self] in
                updateBankAccount.onNext(bankAccount)
            }).disposed(by: disposeBag)
    }
    
    func bindViewModelLogic() {
        let input = AddEditBankAccountViewModel.Input(onViewAppear: rx.viewWillAppear.take(1).mapToVoid(),
                                                      updateBankAccount: updateBankAccount.asObservable())
        
        let output = viewModel.transform(input: input)
        
        disposeBag.insert([
            // Loading
            output.onLoading
                .drive(onNext: { [unowned self] isLoading in
                    isLoading ? showLoading() : hiddenLoading()
                }),
            
            // Error
            output.onError
                .drive(onNext: { [unowned self] error in
                    var errorMessage = error.localizedDescription
                    if let apiError = error as? APIError {
                        errorMessage = apiError.message
                    }
                    showAlert(message: errorMessage)
                }),
            
            // Bank account type list
            output.onBankAccTypeList
                .withLatestFrom(output.onPrefillBankAccount) { ($0, $1) }
                .subscribe(onNext: { [unowned self] bankAccTypeList, prefillBankAccount in
                    guard let itemView = view(for: .accountType) as? OptionSelectDropdownView<BankAccountType> else { return }
                    
                    itemView.updateItemList(bankAccTypeList)
                    
                    if let bankAccount = prefillBankAccount {
                        itemView.setSelectedItems(by: [bankAccount.accountType])
                    }
                }),
            
            // Currency list
            output.onCurrencyList
                .withLatestFrom(output.onPrefillBankAccount) { ($0, $1) }
                .subscribe(onNext: { [unowned self] currencyList, prefillBankAccount in
                    guard let itemView = view(for: .currency) as? OptionSelectDropdownView<BankCurrency> else { return }
                    
                    itemView.updateItemList(currencyList)
                    
                    if let bankAccount = prefillBankAccount {
                        itemView.setSelectedItems(by: bankAccount.currency)
                    }
                }),
            
            // Country list
            output.onCountryList
                .withLatestFrom(output.onPrefillBankAccount) { ($0, $1) }
                .subscribe(onNext: { [unowned self] countryList, prefillBankAccount in
                    guard let itemView = view(for: .bankCountry) as? OptionSelectDropdownView<Country> else { return }
                    
                    itemView.updateItemList(countryList)
                    
                    if let bankAccount = prefillBankAccount {
                        itemView.setSelectedItems(by: [bankAccount.bankAddress?.countryRegion ?? ""])
                    }
                }),
            
            // Prefill bank account (Editting)
            output.onPrefillBankAccount
                .subscribe(onNext: { [unowned self] in
                    guard let bankAccount = $0 else { return }
                    
                    self.bankAccount = bankAccount
                    
                    titleImgView.image = .image(named: "ic_edit_14")
                    titleLabel.text = "\("key0155".localized()) \(bankAccount.accountName)"
                    
                    saveButton.setTitle("key0754".localized(), for: .normal)
 
                    // CAInputTextField
                    (view(for: InputField.bankName) as? CAInputTextField)?.set(text: bankAccount.bankName)
                    (view(for: InputField.accountName) as? CAInputTextField)?.set(text: bankAccount.accountName)
                    (view(for: InputField.accountNumber) as? CAInputTextField)?.set(text: bankAccount.accountNumber)
                    (view(for: InputField.swiftCode) as? CAInputTextField)?.set(text: bankAccount.swiftCode)
                    (view(for: InputField.bankPostalCode) as? CAInputTextField)?.set(text: bankAccount.bankAddress?.postCode)
                    (view(for: InputField.bankAddressLine) as? CAInputTextField)?.set(text: bankAccount.bankAddress?.address)
                    
                    validateInput()
                }),
            
            // Did update bank account
            output.onUpdatedBankAccount
                .subscribe(onNext: { [unowned self] in
                    backPress()
                })
        ])
    }
    
    func handleFieldsInput() {
        // Text input
        contentStackView.arrangedSubviews.compactMap { $0 as? CAInputTextField }
            .forEach { inputTF in
                inputTF.rx.onInputText
                    .subscribe(onNext: { [unowned self] text, tag in
                        guard let field = InputField(rawValue: tag) else { return }
                        
                        switch field {
                        case .bankName:
                            bankAccount.bankName = text
                            
                        case .accountName:
                            bankAccount.accountName = text
                            
                        case .accountNumber:
                            bankAccount.accountNumber = text
                            
                        case .swiftCode:
                            bankAccount.swiftCode = text
                            
                        case .bankPostalCode:
                            if bankAccount.bankAddress == nil {
                                bankAccount.bankAddress = BankAddress()
                            }
                            bankAccount.bankAddress?.postCode = text
                            
                        case .bankAddressLine:
                            if bankAccount.bankAddress == nil {
                                bankAccount.bankAddress = BankAddress()
                            }
                            bankAccount.bankAddress?.address = text
    
                        default:
                            break
                        }
                        
                        // Validate input
                        validateInput()
                    })
                    .disposed(by: disposeBag)
            }
        
        // Bank Account type dropdown selection
        if let accTypeView = view(for: InputField.accountType) as? OptionSelectDropdownView<BankAccountType> {
            accTypeView.beginShowOptionList
                .subscribe(onNext: { [unowned self] in
                    exitEditting()
                }).disposed(by: disposeBag)
            
            
            accTypeView.onSelectedItem.compactMap { $0 }
                .subscribe(onNext: { [unowned self] accType in
                    bankAccount.accountType = accType.code
                    
                    // Validate input
                    validateInput()
                })
                .disposed(by: disposeBag)
        }
        
        // Currency dropdown selection (Multiple)
        if let currencyView = view(for: InputField.currency) as? OptionSelectDropdownView<BankCurrency> {
            currencyView.beginShowOptionList
                .subscribe(onNext: { [unowned self] in
                    exitEditting()
                }).disposed(by: disposeBag)
            
            currencyView.onSelectedItems
                .subscribe(onNext: { [unowned self] currencies in
                    bankAccount.currency = currencies.map { $0.code }
                    
                    // Validate input
                    validateInput()
                })
                .disposed(by: disposeBag)
        }
        
        // Bank country/region dropdown selection
        if let countryView = view(for: InputField.bankCountry) as? OptionSelectDropdownView<Country> {
            countryView.beginShowOptionList
                .subscribe(onNext: { [unowned self] in
                    exitEditting()
                }).disposed(by: disposeBag)
            
            countryView.onSelectedItem.compactMap { $0 }
                .subscribe(onNext: { [unowned self] country in
                    if bankAccount.bankAddress == nil {
                        bankAccount.bankAddress = BankAddress()
                    }
                    bankAccount.bankAddress?.countryRegion = country.code
                    
                    // Validate input
                    validateInput()
                })
                .disposed(by: disposeBag)
        }
    }
}

// MARK: - Private
private extension AddEditBankAccountViewController {
    
    func view(for field: InputField) -> UIView? {
        guard !contentStackView.arrangedSubviews.isEmpty else { return nil }
        
        return contentStackView.arrangedSubviews.first(where: { $0.tag == field.tag })
    }
    
    func exitEditting() {
        contentStackView.arrangedSubviews.compactMap { $0 as? CAInputTextField }
            .forEach { inputTF in
                inputTF.endEditting()
            }
    }
    
    func validateInput() {
        saveButton.isEnabled = bankAccount.isValid()
        saveButton.backgroundColor = saveButton.isEnabled ? Color.btn2nd : Color.btnDisabled
    }
}

// MARK: - Element
extension AddEditBankAccountViewController {
    
    enum InputField: Int, CaseIterable {
        case accountType = 1
        case bankName
        case accountName
        case accountNumber
        case swiftCode
        case currency
        case bankCountry
        case bankAddressLine
        case bankPostalCode
        
        var tag: Int { rawValue }
        
        func view() -> UIView {
            switch self {
            case .accountType:
                return OptionSelectDropdownView<BankAccountType>(title: "key0342".localized(),
                                                                 isRequired: true,
                                                                 placeholder: "key0851".localized(),
                                                                 tag: tag)
            case .bankName:
                return CAInputTextField(displayModel: .init(titleAttribute: .init(text: "key0738".localized(),
                                                                                  isRequired: true),
                                                            placeholder: "key0746".localized()),
                                        tag: tag)
                
            case .accountName:
                return CAInputTextField(displayModel: .init(titleAttribute: .init(text: "key0739".localized(),
                                                                                  isRequired: true),
                                                            placeholder: "key0747".localized()),
                                        tag: tag)
                
            case .accountNumber:
                return CAInputTextField(displayModel: .init(titleAttribute: .init(text: "key0740".localized(),
                                                                                  isRequired: true),
                                                            placeholder: "key0748".localized()),
                                        tag: tag)
                
            case .swiftCode:
                return CAInputTextField(displayModel: .init(titleAttribute: .init(text: "key0741".localized(),
                                                                                  isRequired: true),
                                                            placeholder: "key0749".localized()),
                                        tag: tag)
                
            case .currency:
                return OptionSelectDropdownView<BankCurrency>(title: "key0513".localized(),
                                                              badge: "key0882".localized(),
                                                              isRequired: true,
                                                              placeholder: "key0755".localized(),
                                                              multipleSelection: true,
                                                              tag: tag)
                
            case .bankCountry:
                return OptionSelectDropdownView<Country>(title: "key0742".localized(),
                                                         isRequired: true,
                                                         placeholder: "key0750".localized(),
                                                         searchPlaceholder: "key0316".localized(),
                                                         tag: tag)
                
            case .bankAddressLine:
                return CAInputTextField(displayModel: .init(placeholder: "key0751".localized()),
                                        tag: tag)
                
            case .bankPostalCode:
                return CAInputTextField(displayModel: .init(placeholder: "key0752".localized(),
                                                            keyboardType: .numberPad),
                                        tag: tag)
                
            }
        }
    }
}
