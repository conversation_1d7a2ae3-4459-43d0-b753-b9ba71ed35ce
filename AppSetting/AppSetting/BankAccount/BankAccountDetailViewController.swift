//
//  BankAccountDetailViewController.swift
//  AppSetting
//
//  Created by <PERSON> C<PERSON> on 19/12/24.
//

import UIKit
import Core
import CUIModule
import APILayer
import RxSwift

final class BankAccountDetailViewController: BaseViewController, VMView {
    
    // MARK: UI properties
    private lazy var headerView = {
        let displayModel = CUINavigationBar.DisplayModel(leftIcon: .image(named: "merit_ic_back"),
                                                         titleView: titleStackView)
        let view = CUINavigationBar(displayModel: displayModel)
        
        return view
    }()
    
    private lazy var titleStackView = {
        let stackView = UIStackView(alignment: .center,
                                    spacing: 4)
        stackView.addArrangedSubviews([titleImgView,
                                       titleLabel])
        
        return stackView
    }()
    
    private lazy var titleImgView = UIImageView(image: AppSetting.bankAccountDetails.icon)
    
    private lazy var titleLabel = UILabel(font: Font.semiBold.of(size: 14),
                                          text: AppSetting.bankAccountDetails.title,
                                          textColor: Color.txtTitle)
    
    private lazy var tableView = {
        let tableView = UITableView(style: .grouped,
                                    backgroundColor: Color.bgDefault,
                                    separatorStyle: .none,
                                    showsVerticalScrollIndicator: false)
        tableView.dataSource = self
        tableView.delegate = self
        
        tableView.register(BankAccountDetailCell.self)
        tableView.register(BankAccountDetailHeaderView.self)
        tableView.allowsSelection = false
        
        return tableView
    }()
    
    private lazy var addButton = UIButton(type: .system,
                                          backgroundColor: Color.btn4th,
                                          titleFont: Font.semiBold.of(size: 14),
                                          title: "key0743".localized(),
                                          normalTitleColor: Color.txtParagraph,
                                          normalImage: .image(named: "merit_ic_add").withRenderingMode(.alwaysOriginal),
                                          cornerRadius: 22,
                                          titleInsets: UIEdgeInsets(left: 2, right: -2),
                                          imageInsets: UIEdgeInsets(left: -2, right: 2))
    
    // MARK: Properties
    var viewModel: BankAccountDetailViewModel!
    private let onDeleteBankAccount = PublishSubject<QueryUserBankListEndPoint.BankAccount>()
    private let onSetPrimary = PublishSubject<QueryUserBankListEndPoint.BankAccount>()
    
    // MARK: Life cycle
    override func viewDidLoad() {
        super.viewDidLoad()
        
        setupUI()
        bindActions()
        bindViewModelLogic()
    }
    
    func setupUI() {
        view.backgroundColor = Color.bgDefault
        
        view.addSubviews([tableView,
                          headerView,
                          addButton])
        
        headerView.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide)
            make.left.right.equalToSuperview()
        }
        
        addButton.snp.makeConstraints { make in
            make.left.equalTo(12)
            make.right.equalTo(-12)
            make.bottom.equalTo(view.safeAreaLayoutGuide).offset(-16)
            make.height.equalTo(44)
        }
        
        tableView.snp.makeConstraints { make in
            make.top.equalTo(headerView.snp.bottom)
            make.left.equalTo(12)
            make.right.equalTo(-12)
            make.bottom.equalTo(addButton.snp.top).offset(-12)
        }
    }
    
    func bindActions() {
        headerView.rx.leftTap
            .subscribe(onNext: { [unowned self] in
                backPress()
            }).disposed(by: disposeBag)
        
        addButton.rx.tap
            .subscribe(onNext: { [unowned self] in
                viewModel.navigate(to: .addEditBankAccount(initialBankAccount: nil))
            }).disposed(by: disposeBag)
    }
    
    func bindViewModelLogic() {
        let input = BankAccountDetailViewModel.Input(onViewAppear: rx.viewWillAppear.mapToVoid(),
                                                     onDeleteBankAccount: onDeleteBankAccount.asObservable(),
                                                     onSetPrimary: onSetPrimary.asObservable())
        let output = viewModel.transform(input: input)
        
        disposeBag.insert([
            // Loading
            output.onLoading
                .drive(onNext: { [unowned self] isLoading in
                    isLoading ? showLoading() : hiddenLoading()
                }),
            
            // Error
            output.onError
                .drive(onNext: { [unowned self] error in
                    var errorMessage = error.localizedDescription
                    if let apiError = error as? APIError {
                        errorMessage = apiError.message
                    }
                    showAlert(message: errorMessage)
                }),
            
            // Display bank account list
            output.displayBankAccountList
                .drive(onNext: { [unowned self] in
                    tableView.reloadData()
                })
        ])
    }
}

// MARK: - UITableViewDataSource, UITableViewDelegate
extension BankAccountDetailViewController: UITableViewDataSource, UITableViewDelegate {
    
    func tableView(_ tableView: UITableView, viewForHeaderInSection section: Int) -> UIView? {
        let reuseIdentifier = BankAccountDetailHeaderView.reuseIdentifier
        let header = tableView.dequeueReusableHeaderFooterView(withIdentifier: reuseIdentifier)
        
        return header
    }
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        viewModel.bankAccountList.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell: BankAccountDetailCell = tableView.dequeueReusableCell(forIndexPath: indexPath)
        let bankAccountDetail = viewModel.bankAccountList[indexPath.row]
        
        cell.update(bankIndex: indexPath.row + 1)
        cell.updateUI(with: bankAccountDetail)
        
        cell.rx.edit
            .subscribe(onNext: { [unowned self] in
                viewModel.navigate(to: .addEditBankAccount(initialBankAccount: bankAccountDetail.bankAccount))
            }).disposed(by: cell.disposeBag)
        
        cell.rx.delete
            .subscribe(onNext: { [unowned self] in
                let bottomSheet = BankAccountActionBottomSheet(action: .delete)
                
                bottomSheet.rx.apply
                    .subscribe(onNext: { [unowned self] in
                        onDeleteBankAccount.onNext(bankAccountDetail.bankAccount)
                    }).disposed(by: cell.disposeBag)
                
                bottomSheet.present()
            }).disposed(by: cell.disposeBag)
        
        cell.rx.switchOnPrimary
            .subscribe(onNext: { [unowned self] in
                let bottomSheet = BankAccountActionBottomSheet(action: .edit)
                
                bottomSheet.rx.apply
                    .subscribe(onNext: { [unowned self] in
                        onSetPrimary.onNext(bankAccountDetail.bankAccount)
                    }).disposed(by: disposeBag)
                
                bottomSheet.present()
            }).disposed(by: cell.disposeBag)
        
        return cell
    }
    
    func tableView(_ tableView: UITableView, viewForFooterInSection section: Int) -> UIView? {
        return nil
    }
    
    func tableView(_ tableView: UITableView, heightForFooterInSection section: Int) -> CGFloat {
        return CGFloat.leastNonzeroMagnitude
    }
}
