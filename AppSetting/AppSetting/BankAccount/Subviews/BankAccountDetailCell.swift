//
//  BankAccountDetailCell.swift
//  AppSetting
//
//  Created by <PERSON> on 19/12/24.
//

import UIKit
import Core
import CUIModule
import APILayer
import RxSwift
import SharedData

final class BankAccountDetailCell: UITableViewCell, AnyView {
    
    // MARK: UI properties
    private lazy var contentStackView = {
        let stackView = UIStackView(axis: .vertical,
                                    spacing: 8)
        stackView.addArrangedSubviews([CUIDivider(canvasHeight: 16),
                                       titlleContainer,
                                       itemStackView,
                                       buttonStackView])
        
        return stackView
    }()
    
    private lazy var titlleContainer = {
        let view = UIView()
        view.addSubviews([titleStackView,
                          switchOverlayButton])
        titleStackView.snp.makeConstraints { make in
            make.top.equalTo(4)
            make.bottom.equalTo(-4)
            make.left.right.equalToSuperview()
        }
        
        switchOverlayButton.snp.makeConstraints { make in
            make.top.bottom.equalToSuperview()
            make.left.right.equalTo(switchControl)
        }
        
        return view
    }()
    
    private lazy var titleStackView = {
        let stackView = UIStackView(alignment: .center,
                                    spacing: 4)
        stackView.addArrangedSubviews([titleLabel,
                                       statusBadge,
                                       UIView(),
                                       primaryLabel,
                                       switchControl])
        switchControl.snp.makeConstraints { make in
            make.width.equalTo(42)
            make.height.equalTo(20)
        }
        
        return stackView
    }()
    
    private lazy var titleLabel = UILabel(font: Font.semiBold.of(size: 14),
                                          textColor: Color.txtTitle)
    
    private lazy var statusBadge = {
        let view = UIView(cornerRadius: 2)
        view.addSubview(statusLabel)
        statusLabel.snp.makeConstraints { make in
            make.top.equalTo(2)
            make.left.equalTo(4)
            make.bottom.equalToSuperview()
            make.right.equalTo(-4)
        }
        return view
    }()
    
    private lazy var statusLabel = UILabel(font: Font.medium.of(size: 13))
    
    private lazy var primaryLabel = UILabel(font: Font.medium.of(size: 14),
                                            text: "key0383".localized(),
                                            textColor: Color.txtTitle)
    
    fileprivate lazy var switchControl = {
        let control = YapSwitch()
        control.shape = .rounded
        control.titleFont = Font.medium.of(size: 10)
        control.onText = "key0830".localized()
        control.offText = "key0831".localized()
        control.onTextColor = Color.txtInverted
        control.offTextColor = Color.txtInverted
        control.onTintColor = UIColor(hexString: "#000000")
        control.offTintColor = UIColor(hexString: "#9F9F9F")
        control.onThumbTintColor = Color.txtInverted
        control.offThumbTintColor = Color.txtInverted
        control.thumbRadiusPadding = 4
        control.isUserInteractionEnabled = false
        
        return control
    }()
    
    fileprivate lazy var switchOverlayButton = UIButton()
    
    private lazy var itemStackView = UIStackView(axis: .vertical)
    
    private lazy var buttonStackView = {
        let stackView = UIStackView(distribution: .fillEqually)
        stackView.addArrangedSubviews([deleteButtonContainer,
                                       editButtonContainer])
        
        return stackView
    }()
    
    private lazy var deleteButtonContainer = {
        let view = UIView()
        view.addSubview(deleteButton)
        deleteButton.snp.makeConstraints { make in
            make.top.bottom.centerX.equalToSuperview()
            make.height.equalTo(44)
        }
        
        return view
    }()
    
    fileprivate lazy var deleteButton = {
        let button = UIButton(type: .system,
                              normalImage: .image(named: "ic_delete_bankacc").withRenderingMode(.alwaysOriginal),
                              disabledImage: .image(named: "ic_delete_bankacc_disabled").withRenderingMode(.alwaysOriginal),
                              semanticContentAttribute: .forceRightToLeft,
                              titleInsets: UIEdgeInsets(left: -2, right: 2),
                              imageInsets: UIEdgeInsets(left: 2, right: -2))
        
        button.setHighlightText("key0605".localized(),
                                normalColor: Color.txtTitle,
                                normalFont: Font.semiBold.of(size: 12),
                                highlightText: ["key0605".localized()],
                                highlightColor: Color.txtTitle,
                                highlightFont: Font.semiBold.of(size: 12),
                                underline: true,
                                state: .normal)
        button.setHighlightText("key0605".localized(),
                                normalColor: Color.txtDisabled,
                                normalFont: Font.semiBold.of(size: 12),
                                highlightText: ["key0605".localized()],
                                highlightColor: Color.txtDisabled,
                                highlightFont: Font.semiBold.of(size: 12),
                                underline: true,
                                state: .disabled)
        
        return button
    }()
        
    private lazy var editButtonContainer = {
        let view = UIView()
        view.addSubview(editButton)
        editButton.snp.makeConstraints { make in
            make.top.bottom.centerX.equalToSuperview()
            make.height.equalTo(44)
        }
        
        return view
    }()
    
    fileprivate lazy var editButton = {
        let button = UIButton(type: .system,
                              normalImage: .image(named: "ic_edit_bankacc").withRenderingMode(.alwaysOriginal),
                              disabledImage: .image(named: "ic_edit_bankacc_disabled").withRenderingMode(.alwaysOriginal),
                              semanticContentAttribute: .forceRightToLeft,
                              titleInsets: UIEdgeInsets(left: -2, right: 2),
                              imageInsets: UIEdgeInsets(left: 2, right: -2))
        
        button.setHighlightText("key0155".localized(),
                                normalColor: Color.txtLabel,
                                normalFont: Font.semiBold.of(size: 12),
                                highlightText: ["key0155".localized()],
                                highlightColor: Color.txtLabel,
                                highlightFont: Font.semiBold.of(size: 12),
                                underline: true,
                                state: .normal)
        button.setHighlightText("key0155".localized(),
                                normalColor: Color.txtDisabled,
                                normalFont: Font.semiBold.of(size: 12),
                                highlightText: ["key0155".localized()],
                                highlightColor: Color.txtDisabled,
                                highlightFont: Font.semiBold.of(size: 12),
                                underline: true,
                                state: .disabled)
        
        return button
    }()
    
    // MARK: Properties
    private(set) var disposeBag = DisposeBag()
    
    // MARK: Life cycle
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override func prepareForReuse() {
        super.prepareForReuse()
        
        disposeBag = DisposeBag()
    }
    
    func setupUI() {
        contentView.addSubview(contentStackView)
        
        contentStackView.snp.makeConstraints { make in
            make.top.equalTo(8)
            make.left.bottom.right.equalToSuperview()
        }
        
        for field in BankAccountField.allCases {
            let itemView = SingleTitleContentView(title: field.titleConfig)
            itemView.tag = field.rawValue
            
            itemStackView.addArrangedSubview(itemView)
        }
    }
    
    func update(bankIndex: Int) {
        titleLabel.text = "key1031".localized() + " \(bankIndex)"
    }
    
    func updateUI(with displayModel: any UIDisplayModel) {
        guard let bankAccountDetail = displayModel as? BankAccountDetail else { return }
        
        let bankAccount = bankAccountDetail.bankAccount
        let bankAccountType = bankAccountDetail.accountTypes.first(where: { $0.code == bankAccount.accountType })
        
        
        statusLabel.text = bankAccount.accountStatus.title
        statusLabel.textColor = bankAccount.accountStatus.textColor
        statusBadge.backgroundColor = bankAccount.accountStatus.badgeColor
        statusBadge.isHidden = !bankAccount.accountStatus.shouldDisplay
        
        primaryLabel.textColor = bankAccount.accountStatus.isEnabled ? Color.txtTitle : Color.txtDisabled
        switchControl.setOn(bankAccount.isPrimary ?? false, animated: false)
        switchControl.isEnabled = bankAccount.accountStatus.isEnabled
        
        deleteButton.isEnabled = bankAccount.isPrimary != true && bankAccount.accountStatus.isEnabled
        switchOverlayButton.isEnabled = bankAccount.isPrimary != true && bankAccount.accountStatus.isEnabled
        
        editButton.isEnabled = bankAccount.accountStatus.isEnabled
        
        for field in BankAccountField.allCases {
            if let itemView = itemStackView.arrangedSubviews.first(where: { $0.tag == field.rawValue }) as? SingleTitleContentView {
                let displayModel = mapDisplayData(from: bankAccount, 
                                                  accountType: bankAccountType,
                                                  for: field)
                itemView.updateUI(with: displayModel)
            }
        }
        
        // Hide actions when only 1 account
        primaryLabel.isHidden = bankAccountDetail.totalAccount < 2
        switchControl.isHidden = bankAccountDetail.totalAccount < 2
        switchOverlayButton.isHidden = bankAccountDetail.totalAccount < 2
        deleteButtonContainer.isHidden = bankAccountDetail.totalAccount < 2
    }
}

// MARK: - Mapping
private extension BankAccountDetailCell {
    
    func mapDisplayData(from bankAccount: QueryUserBankListEndPoint.BankAccount,
                        accountType: BankAccountType?,
                        for field: BankAccountField) -> SingleTitleContentView.DisplayModel {
        let text: String
        switch field {
        case .accountType:
            text = accountType?.name ?? "-"
        case .bankName:
            text = bankAccount.bankName ?? "-"
        case .accountName:
            text = bankAccount.accountName ?? "-"
        case .accountNumber:
            text = bankAccount.accountNumber ?? "-"
        case .swiftCode:
            text = bankAccount.swiftCode ?? "-"
        case .currency:
            text = (bankAccount.currency ?? []).joined(separator: ", ")
        case .bankAddress:
            text = bankAccount.bankAddress?.address ?? "-"
        }
        
        let textColor = bankAccount.accountStatus.isEnabled ? Color.txtTitle : Color.txtDisabled
        
        return SingleTitleContentView.DisplayModel(contents: [TextConfig(text: text,
                                                                         font: Font.regular.of(size: 14),
                                                                         textColor: textColor)])
    }
}

// MARK: - Elements
enum BankAccountField: Int, CaseIterable {
    case accountType = 1
    case bankName
    case accountName
    case accountNumber
    case swiftCode
    case currency
    case bankAddress
    
    var titleConfig: TextConfig {
        let text: String
        switch self {
        case .accountType:
            text = "key0342".localized()
        case .bankName:
            text = "key0738".localized()
        case .accountName:
            text = "key0739".localized()
        case .accountNumber:
            text = "key0740".localized()
        case .swiftCode:
            text = "key0741".localized()
        case .currency:
            text = "key0513".localized()
        case .bankAddress:
            text = "key0742".localized()
        }
        
        return TextConfig(text: text,
                          font: Font.light.of(size: 14),
                          textColor: Color.txtParagraph)
    }
}

// MARK: - Reactive
extension Reactive where Base: BankAccountDetailCell {
    
    var edit: Observable<Void> {
        base.editButton.rx.tap.mapToVoid()
    }
    
    var delete: Observable<Void> {
        base.deleteButton.rx.tap.mapToVoid()
    }
    
    var switchOnPrimary: Observable<Void> {
        base.switchOverlayButton.rx.tap.asObservable()
    }
}

// MARK: - QueryUserBankListEndPoint.BankAccount
extension QueryUserBankListEndPoint.BankAccount: UIDisplayModel {}

// MARK: - BankAccountStatus
extension BankAccountStatus {
    
    var isEnabled: Bool {
        self == .approved
    }
}
