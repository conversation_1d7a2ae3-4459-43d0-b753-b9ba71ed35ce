//
//  BankAccountDetailHeaderView.swift
//  AppSetting
//
//  Created by <PERSON> on 19/12/24.
//

import UIKit
import Core
import CUIModule

final class BankAccountDetailHeaderView: UITableViewHeaderFooterView, AnyView {
    
    static var reuseIdentifier: String {
        return String(describing: type(of: self))
    }
    
    // MARK: UI properties
    private lazy var contentStackView = {
        let stackView = UIStackView(axis: .vertical,
                                    spacing: 8)
        stackView.addArrangedSubviews([descLabel,
                                       warningStackView])
        
        return stackView
    }()
    
    private lazy var descLabel = UILabel(font: Font.light.of(size: 14),
                                         numberOfLines: 0,
                                         text: "key0735".localized(),
                                         textColor: Color.txtParagraph)
    
    private lazy var warningStackView = {
        let stackView = UIStackView(alignment: .center,
                                    spacing: 4)
        stackView.addArrangedSubviews([warningIconView,
                                       warningLabel,
                                       UIView()])
        
        return stackView
    }()
    
    private lazy var warningIconView = UIImageView(image: .image(named: "merit_ic_warning_yellow"))
    
    private lazy var warningLabel = UILabel(font: Font.light.of(size: 14),
                                            text: "key0736".localized(),
                                            textColor: Color.txtCaution)
    
    // MARK: Life cycle
    override init(reuseIdentifier: String?) {
        super.init(reuseIdentifier: reuseIdentifier)
        
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func setupUI() {
        contentView.addSubview(contentStackView)
        contentStackView.snp.makeConstraints { make in
            make.top.equalTo(12)
            make.left.bottom.right.equalToSuperview()
        }
    }
}
