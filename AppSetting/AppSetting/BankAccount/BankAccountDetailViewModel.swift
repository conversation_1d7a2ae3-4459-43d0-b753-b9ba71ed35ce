//
//  BankAccountDetailViewMoel.swift
//  AppSetting
//
//  Created by <PERSON> on 19/12/24.
//

import Core
import APILayer
import RxSwift
import RxCocoa
import XCoordinator

final class BankAccountDetailViewModel: AnyViewModel {
    
    struct Input {
        let onViewAppear: Observable<Void>
        let onDeleteBankAccount: Observable<QueryUserBankListEndPoint.BankAccount>
        let onSetPrimary: Observable<QueryUserBankListEndPoint.BankAccount>
    }
    
    struct Output {
        let onLoading: Driver<Bool>
        let onError: Driver<Error>
        let displayBankAccountList: Driver<Void>
    }
    
    private let router: UnownedRouter<AppSettingsRoute>
    private(set) var bankAccountTypes: [BankAccountType] = []
    private(set) var bankAccountList: [BankAccountDetail] = []
    
    init(router: UnownedRouter<AppSettingsRoute>) {
        self.router = router
    }
    
    func transform(input: Input) -> Output {
        let activityIndicator = ActivityIndicator()
        let errorTracker = ErrorTracker()
        
        let onDeleteBankAccount = input.onDeleteBankAccount
            .flatMap { [unowned self] bankAccount in
                deleteBankAccount(bankAccount)
                    .track(activityIndicator, error: errorTracker)
            }
            .mapToVoid()
        
        let onSetPrimary = input.onSetPrimary
            .flatMap { [unowned self] bankAccount in
                setPrimary(for: bankAccount)
                    .track(activityIndicator, error: errorTracker)
            }
            .mapToVoid()
        
        let queryBankAccountTypes = input.onViewAppear
            .flatMap { [unowned self] in
                queryBankAccTypeList()
                    .track(activityIndicator, error: errorTracker)
            }
            .onNext { [unowned self] in
                bankAccountTypes = $0
            }
            .mapToVoid()
        
        let queryBankAccounts = Observable.merge(queryBankAccountTypes,
                                                 onDeleteBankAccount,
                                                 onSetPrimary)
            .flatMap { [unowned self] in
                queryBankAccountList()
                    .track(activityIndicator, error: errorTracker)
            }
            .onNext { [unowned self] in
                let totalAccounts = $0.bankAccountList?.count ?? 0
                bankAccountList = ($0.bankAccountList ?? []).map({
                    BankAccountDetail(bankAccount: $0,
                                      accountTypes: bankAccountTypes, 
                                      totalAccount: totalAccounts)
                })
            }
            .mapToVoid()
        
        return Output(onLoading: activityIndicator.asDriver(),
                      onError: errorTracker.asDriver(),
                      displayBankAccountList: queryBankAccounts.asDriverOnErrorNever())
    }
    
    func navigate(to route: AppSettingsRoute) {
        router.trigger(route)
    }
}

// MARK: - API
private extension BankAccountDetailViewModel {
    
    func queryBankAccountList() -> Observable<QueryUserBankListEndPoint.Response> {
        QueryUserBankListEndPoint.service.call()
    }
    
    func setPrimary(for bankAccount: QueryUserBankListEndPoint.BankAccount) -> Observable<Void> {
        let request = mapUpdateRequest(for: bankAccount,
                                       isPrimary: true,
                                       action: .edit)
        
        return UserBankAccountEditEndPoint.service.request(parameters: request)
            .mapToVoid()
    }
    
    func deleteBankAccount(_ bankAccount: QueryUserBankListEndPoint.BankAccount) -> Observable<Void> {
        let request = mapUpdateRequest(for: bankAccount, action: .delete)
        
        return UserBankAccountEditEndPoint.service.request(parameters: request)
            .mapToVoid()
    }
    
    // Query Educational levels
    func queryBankAccTypeList() -> Observable<[BankAccountType]> {
        let request = EnumerationEndPoint.Request(keys: [EnumerationKey.bankAccType])
        return EnumerationEndPoint.service.request(parameters: request)
            .map { [unowned self] in
                mapBankAccTypeList(from: $0)
            }
    }
    
}

// MARK: - Mapping
private extension BankAccountDetailViewModel {
    
    func mapUpdateRequest(for bankAccount: QueryUserBankListEndPoint.BankAccount,
                          isPrimary: Bool? = nil,
                          action: BankEditActionType) -> UserBankAccountEditEndPoint.Request {
        let address = UserBankAccountEditEndPoint.BankAddress(countryRegion: bankAccount.bankAddress?.countryRegion ?? "",
                                                              address: bankAccount.bankAddress?.address ?? "",
                                                              postCode: bankAccount.bankAddress?.postCode)
     
        return .init(bankAccountId: bankAccount.id,
              accountType: bankAccount.accountType ?? "",
              bankName: bankAccount.bankName ?? "",
              accountName: bankAccount.accountName ?? "",
              accountNumber: bankAccount.accountNumber ?? "",
              swiftCode: bankAccount.swiftCode ?? "",
              currency: bankAccount.currency ?? [],
              bankAddress: address,
              remark: nil,
                     isPrimary: (isPrimary ?? bankAccount.isPrimary ?? false).requestString,
              action: action.requestValue)
    }
    
    /// Map Country list from Enumeration response
    func mapBankAccTypeList(from response: EnumerationEndPoint.Response) -> [BankAccountType] {
        guard
            let objectList = response.list.first?.list,
            !objectList.isEmpty
        else { return [] }
        
        return objectList.map { object in
            BankAccountType(code: object.value,
                            name: object.desc,
                            id: object.sort)
        }
    }
}
