//
//  AddEditBankAccountViewModel.swift
//  AppSetting
//
//  Created by <PERSON> on 19/12/24.
//

import Core
import APILayer
import RxSwift
import RxCocoa
import SharedData

final class AddEditBankAccountViewModel: AnyViewModel {
    
    struct Input {
        let onViewAppear: Observable<Void>
        let updateBankAccount: Observable<BankAccountModel>
    }
    
    struct Output {
        let onLoading: Driver<Bool>
        let onError: Driver<Error>
        let onBankAccTypeList: Observable<[BankAccountType]>
        let onCurrencyList: Observable<[BankCurrency]>
        let onCountryList: Observable<[Country]>
        let onPrefillBankAccount: Observable<BankAccountModel?>
        let onUpdatedBankAccount: Observable<Void>
    }

    private let initialBankAccount: QueryUserBankListEndPoint.BankAccount?
    
    var isEditting: Bool {
        (initialBankAccount?.id ?? 0) > 0
    }
    
    init(initialBankAccount: QueryUserBankListEndPoint.BankAccount?) {
        self.initialBankAccount = initialBankAccount
    }
    
    func transform(input: Input) -> Output {
        let activityIndicator = ActivityIndicator()
        let errorTracker = ErrorTracker()
        
        
        let bankAccTypeList = input.onViewAppear
            .flatMap { [unowned self] in
                queryBankAccTypeList()
                    .track(activityIndicator, error: errorTracker)
            }
        
        let currencyList = input.onViewAppear
            .flatMap { [unowned self] in
                queryCurrencyList()
                    .track(activityIndicator, error: errorTracker)
            }
        
        let countryList = input.onViewAppear
            .flatMap { [unowned self] in
                queryCountryList()
                    .track(activityIndicator, error: errorTracker)
            }
        
        let prefillBankAccount = input.onViewAppear
            .map { [unowned self] () -> BankAccountModel? in
                if let bankAccount = initialBankAccount {
                    return BankAccountModel(with: bankAccount)
                }
                
                return nil
            }
        
        let updateBankAccount = input.updateBankAccount
            .flatMap({ [unowned self] in
                editBankAccount(from: $0, action: $0.bankAccountId != 0 ? .edit : .add)
                    .track(activityIndicator, error: errorTracker)
            })
            .mapToVoid()
        
        return Output(onLoading: activityIndicator.asDriver(),
                      onError: errorTracker.asDriver(),
                      onBankAccTypeList: bankAccTypeList,
                      onCurrencyList: currencyList,
                      onCountryList: countryList,
                      onPrefillBankAccount: prefillBankAccount,
                      onUpdatedBankAccount: updateBankAccount)
    }
}

// MARK: - API
private extension AddEditBankAccountViewModel {
    
    // Query Educational levels
    func queryBankAccTypeList() -> Observable<[BankAccountType]> {
        let request = EnumerationEndPoint.Request(keys: [EnumerationKey.bankAccType])
        return EnumerationEndPoint.service.request(parameters: request)
            .map { [unowned self] in
                mapBankAccTypeList(from: $0)
            }
    }
    
    // Query Educational levels
    func queryCurrencyList() -> Observable<[BankCurrency]> {
        let request = EnumerationEndPoint.Request(keys: [EnumerationKey.currency])
        return EnumerationEndPoint.service.request(parameters: request)
            .map { [unowned self] in
                mapCurrencyList(from: $0)
            }
    }
    
    // Query Country list
    func queryCountryList() -> Observable<[Country]> {
        let request = EnumerationEndPoint.Request(keys: [EnumerationKey.region])
        return EnumerationEndPoint.service.request(parameters: request)
            .map { [unowned self] in
                mapCountryList(from: $0)
            }
    }
    
    func editBankAccount(from object: BankAccountModel, action: BankEditActionType) -> Observable<Void> {
        let request = mapBankAccountRequest(from: object,
                                            action: action)
        
        return UserBankAccountEditEndPoint.service.request(parameters: request)
            .mapToVoid()
    }
}

// MARK: - Mapping
private extension AddEditBankAccountViewModel {
    
    /// Map Country list from Enumeration response
    func mapBankAccTypeList(from response: EnumerationEndPoint.Response) -> [BankAccountType] {
        guard
            let objectList = response.list.first?.list,
            !objectList.isEmpty
        else { return [] }
        
        return objectList.map { object in
            BankAccountType(code: object.value,
                            name: object.desc,
                            id: object.sort)
        }
        .sorted(by: { $0.id < $1.id })
    }
    
    /// Map Country list from Enumeration response
    func mapCurrencyList(from response: EnumerationEndPoint.Response) -> [BankCurrency] {
        guard
            let objectList = response.list.first?.list,
            !objectList.isEmpty
        else { return [] }
        
        return objectList.map { object in
            BankCurrency(code: object.value,
                         name: object.desc,
                         id: object.sort)
        }
        .sorted(by: { $0.id < $1.id })
    }
    
    /// Map Country list from Enumeration response
    func mapCountryList(from response: EnumerationEndPoint.Response) -> [Country] {
        guard
            let objectList = response.list.first?.list,
            !objectList.isEmpty
        else { return [] }
        
        return objectList.map { object in
            Country(code: object.value,
                    name: object.desc,
                    id: object.sort)
        }
        .sorted(by: { $0.id < $1.id })
    }
    
    func mapBankAccountRequest(from object: BankAccountModel,
                               action: BankEditActionType) -> UserBankAccountEditEndPoint.Request {
        let address = UserBankAccountEditEndPoint.BankAddress(countryRegion: object.bankAddress?.countryRegion ?? "",
                                                              address: object.bankAddress?.address ?? "",
                                                              postCode: object.bankAddress?.postCode)
        
        return .init(bankAccountId: action == .add ? nil : object.bankAccountId,
                     accountType: object.accountType,
                     bankName: object.bankName,
                     accountName: object.accountName,
                     accountNumber: object.accountNumber,
                     swiftCode: object.swiftCode,
                     currency: object.currency,
                     bankAddress: address,
                     remark: nil,
                     isPrimary: object.isPrimary.requestString,
                     action: action.requestValue)
    }
    
    func mapBankAccountList(from response: QueryUserBankListEndPoint.Response) -> [BankAccountModel] {
        guard
            let bankAccountList = response.bankAccountList,
            !bankAccountList.isEmpty
        else { return [] }
        
        return bankAccountList.map {
            BankAccountModel(with: $0)
        }
    }
}

// MARK: - extension Bool
extension Bool {
    
    func toString() -> String {
        self ? "key0099".localized() : "key0100".localized()
    }
    
    var requestString: String {
        self ? "TRUE" : "FALSE"
    }
}
