//
//  BankAccountActionBottomSheet.swift
//  AppSetting
//
//  Created by <PERSON> on 20/12/24.
//

import UIKit
import Core
import CUIModule
import RxSwift

final class BankAccountActionBottomSheet: UIViewController, AnyView, BaseBottomSheet {

    // MARK: UI properties
    private lazy var titleStackView = {
        let stackView = UIStackView(alignment: .center,
                                    spacing: 4)
        stackView.addArrangedSubviews([iconView,
                                       titleLabel,
                                       UIView()])
        
        return stackView
    }()
    
    private lazy var iconView = UIImageView(image: .image(named: "merit_ic_logout"))
    
    private lazy var titleLabel = {
        let label = UILabel(font: Font.regular.of(size: 14),
                            text: "\("key0102".localized()) " + action.bottomSheetTitle,
                            textColor: Color.txtParagraph)
        label.highlight("key0102".localized(),
                        highlightFont: Font.semiBold.of(size: 14),
                        highlightColor: Color.txtTitle)
        
        return label
    }()
    
    private lazy var closeButton = UIButton(normalImage: .image(named: "merit_ic_cross"))
    
    private lazy var descLabel = UILabel(font: Font.light.of(size: 14),
                                         numberOfLines: 0,
                                         text: action.desc,
                                         textColor: Color.txtParagraph)

    private lazy var buttonStackview = {
        let stackView = UIStackView(distribution: .fillEqually,
                                    spacing: 8)
        stackView.addArrangedSubviews([cancelButton,
                                       applyButton])
        
        [cancelButton, applyButton].forEach { button in
            button.snp.makeConstraints { $0.height.equalTo(44) }
        }
        
        return stackView
    }()
    
    private lazy var applyButton = UIButton(type: .system,
                                            backgroundColor: Color.btn2nd,
                                            titleFont: Font.semiBold.of(size: 14),
                                            title: action.applyTitle,
                                            normalTitleColor: Color.txtInverted,
                                            cornerRadius: 22)
    
    private lazy var cancelButton = UIButton(type: .system,
                                             backgroundColor: Color.bgDefault,
                                             titleFont: Font.medium.of(size: 14),
                                             title: "key0273".localized(),
                                             normalTitleColor: Color.txtTitle,
                                             cornerRadius: 22,
                                             borderColor: Color.btn2nd,
                                             borderWidth: 1)
    
    // MARK: Properties
    var disposeBagForDismiss = DisposeBag()
    var onDismiss = PublishSubject<(() -> Void)?>()
    
    private let action: BankEditActionType
    fileprivate let applyAction = PublishSubject<Void>()
    
    // MARK: Life cycle
    init(action: BankEditActionType) {
        self.action = action
        
        super.init(nibName: nil, bundle: nil)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        setupUI()
        bindActions()
    }
    
    func setupUI() {
        view.backgroundColor = Color.bgDefault
        
        view.addSubviews([titleStackView,
                          closeButton,
                          descLabel,
                          buttonStackview])
        
        closeButton.snp.makeConstraints { make in
            make.top.right.equalToSuperview()
            make.width.height.equalTo(44)
        }
        
        titleStackView.snp.makeConstraints { make in
            make.left.equalTo(12)
            make.right.equalTo(closeButton.snp.left)
            make.centerY.equalTo(closeButton)
        }
        
        buttonStackview.snp.makeConstraints { make in
            make.left.equalTo(12)
            make.right.equalTo(-12)
            make.bottom.equalTo(view.safeAreaLayoutGuide).offset(-16).priority(.high)
        }
        
        descLabel.snp.makeConstraints { make in
            make.top.equalTo(closeButton.snp.bottom).offset(12)
            make.left.equalTo(12)
            make.right.equalTo(-12)
            make.bottom.equalTo(buttonStackview.snp.top).offset(-20)
        }
    }
    
    func bindActions() {
        Observable.merge(closeButton.rx.tap.asObservable(),
                         cancelButton.rx.tap.asObservable())
        .subscribe(onNext: { [unowned self] in
            onDismiss.onNext(nil)
        }).disposed(by: disposeBagForDismiss)
        
        applyButton.rx.tap
            .subscribe(onNext: { [unowned self] in
                onDismiss.onNext(onApply)
            }).disposed(by: disposeBagForDismiss)
    }
    
    func onApply() {
        applyAction.onNext(())
    }
}

// MARK: - Reactive
extension Reactive where Base: BankAccountActionBottomSheet {
    
    var apply: Observable<Void> {
        base.applyAction.asObservable()
    }
}

// MARK: - BankEditActionType
private extension BankEditActionType {
    
    var bottomSheetTitle: String {
        switch self {
        case .edit:
            return "key0756".localized()
        case .delete:
            return "key0940".localized()
            
        default:
            return ""
        }
    }
       
    var applyTitle: String {
        switch self {
        case .edit:
            return "key0758".localized()
        case .delete:
            return "key0260".localized()
            
        default:
            return ""
        }
    }
       
    var desc: String {
        switch self {
        case .edit:
            return "key0757".localized()
            
        case .delete:
            return "key0941".localized()
            
        default:
            return ""
        }
    }
    
    
}
