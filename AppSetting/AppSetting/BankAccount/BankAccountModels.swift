//
//  BankAccountModels.swift
//  AppSetting
//
//  Created by <PERSON> on 20/12/24.
//

import CUIModule
import SharedData
import APILayer
import Core

// MARK: - Base protocol for Dropdown model
protocol BaseDropdownItem: OptionSelectDropdownItem {
    var code: String { get }
    var name: String { get }
    
    // Bottomsheet title
    static var sheetTitle: String { get }
    static var hilightSheetTitle: String { get }
}

extension BaseDropdownItem {
    public var stringId: String { code }
    public var title: String { name }
    
    public static var bottomSheetTitle: String { "" }
    
    public static var bottomSheetAttributedTitle: NSAttributedString? {
        let attributes: [NSAttributedString.Key: Any] = [
            .font: Font.regular.of(size: 14),
            .foregroundColor: Color.txtParagraph
        ]
        let attributeString = NSMutableAttributedString(string: sheetTitle,
                                                        attributes: attributes)
        
        let range: NSRange = attributeString.mutableString.range(of: hilightSheetTitle,
                                                                 options: .caseInsensitive)
        attributeString.addAttributes([NSAttributedString.Key.font: Font.semiBold.of(size: 14),
                                       NSAttributedString.Key.foregroundColor: Color.txtTitle],
                                      range: range)
        
        return attributeString
    }
}

// MARK: - Country
extension Country: BaseDropdownItem {
    
    static var sheetTitle: String = "\("key0102".localized()) \("key0834".localized())"
    static var hilightSheetTitle: String = "key0102".localized()
}

// MARK: - Bank account type
struct BankAccountType: BaseDropdownItem {
    let code: String
    let name: String
    let id: Int
    
    static var sheetTitle: String = "\("key0102".localized()) \("key0903".localized())"
    static var hilightSheetTitle: String = "key0102".localized()
}

// MARK: - Currency
struct BankCurrency: BaseDropdownItem {
    let code: String
    let name: String
    let id: Int
    
    static var sheetTitle: String = "\("key0102".localized()) \("key0755".localized())"
    static var hilightSheetTitle: String = "key0102".localized()
}

// MARK: - BankAccountDetail
struct BankAccountDetail: UIDisplayModel {
    let bankAccount: QueryUserBankListEndPoint.BankAccount
    let accountTypes: [BankAccountType]
    let totalAccount: Int
}

// MARK: - Bank Account (Edit Bank Account)
struct BankAccountModel {
    var bankAccountId: Int
    var accountType: String
    var bankName: String
    var accountName: String
    var accountNumber: String
    var swiftCode: String
    var currency: [String]
    var bankAddress: BankAddress?
    var isPrimary: Bool
    
    init(bankAccountId: Int = 0,
         accountType: String = "",
         bankName: String = "",
         accountName: String = "",
         accountNumber: String = "",
         swiftCode: String = "",
         currency: [String] = [],
         bankAddress: BankAddress? = nil,
         isPrimary: Bool = false) {
        self.bankAccountId = bankAccountId
        self.accountType = accountType
        self.bankName = bankName
        self.accountName = accountName
        self.accountNumber = accountNumber
        self.swiftCode = swiftCode
        self.currency = currency
        self.bankAddress = bankAddress
        self.isPrimary = isPrimary
    }
    
    init(with bankAccount: QueryUserBankListEndPoint.BankAccount) {
        self.bankAccountId = bankAccount.id ?? 0
        self.accountType = bankAccount.accountType ?? ""
        self.bankName = bankAccount.bankName ?? ""
        self.accountName = bankAccount.accountName ?? ""
        self.accountNumber = bankAccount.accountNumber ?? ""
        self.swiftCode = bankAccount.swiftCode ?? ""
        self.currency = bankAccount.currency ?? []
        self.bankAddress = BankAddress(countryRegion: bankAccount.bankAddress?.countryRegion ?? "",
                                       address: bankAccount.bankAddress?.address ?? "",
                                       postCode: bankAccount.bankAddress?.postCode)
        self.isPrimary = bankAccount.isPrimary ?? false
    }
    
    func isValid() -> Bool {
        guard !accountType.isEmpty else { return false }
        guard !bankName.isEmpty else { return false }
        guard !accountName.isEmpty else { return false }
        guard !accountNumber.isEmpty else { return false }
        guard !swiftCode.isEmpty else { return false }
        guard !currency.isEmpty else { return false }
        guard bankAddress?.isValid() == true else { return false }
        
        return true
    }
}

// MARK: - Bank Address
struct BankAddress {
    var countryRegion: String = ""
    var address: String = ""
    var postCode: String?
    
    func isValid() -> Bool {
        guard !countryRegion.isEmpty else { return false }
        guard !address.isEmpty else { return false }
        
        return true
    }
}

// MARK: - Edit action type
enum BankEditActionType {
    case add
    case edit
    case delete
    
    var requestValue: String {
        switch self {
        case .add:
            return "ADD"
        case .edit:
            return "EDIT"
        case .delete:
            return "DELETE"
        }
    }
}
