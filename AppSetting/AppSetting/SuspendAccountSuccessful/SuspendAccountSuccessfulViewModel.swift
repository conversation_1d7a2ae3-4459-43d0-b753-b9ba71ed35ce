//
//  ChangeAccountStatusDisplayViewModel.swift
//  AppSetting
//
//  Created by <PERSON><PERSON> on 8/3/2567 BE.
//

import APILayer
import Core
import CUIModule
import RxCocoa
import RxSwift
import RxRelay
import XCoordinator

public final class SuspendAccountSuccessfulViewModel: AnyViewModel {
    
    private let router: UnownedRouter<AppSettingsRoute>?
    
    public struct Input {
        let onViewAppear: Observable<Void>
    }
    
    public struct Output {
        let displayData: Driver<SuspendAccountAction>
    }
    
    let accountState: SuspendAccountAction
    
    public init(router: UnownedRouter<AppSettingsRoute>? = nil,
                state: SuspendAccountAction) {
        self.router = router
        self.accountState = state
    }
    
    public func transform(input: Input) -> Output {
        let displayData = input.onViewAppear
            .map { self.accountState }
        
        return Output(displayData: displayData.asDriverOnErrorNever())
    }
    
    func navigate(to route: AppSettingsRoute) {
        guard let router = router else { return }
        
        router.trigger(route)
    }
}

