//
//  SuspendAccountSuccessfulViewController.swift
//  AppSetting
//
//  Created by <PERSON><PERSON><PERSON> on 8/3/2567 BE.
//

import CUIModule
import UIKit
import SnapKit
import RxSwift
import Core

public final class SuspendAccountSuccessfulViewController: BaseViewController, VMView {
    
    public var viewModel: SuspendAccountSuccessfulViewModel!
    
    // MARK: UI properties
    private lazy var contentContainer = {
        let view = UIView()
        view.addSubview(contentStackView)
        contentStackView.snp.makeConstraints { make in
            make.left.right.centerY.equalToSuperview()
        }
        
        return view
    }()
    
    /// Content Stack View
    private lazy var contentStackView = UIStackView(axis: .vertical,
                                                    spacing: 16)
    
    private lazy var imageContainer = {
        let view = UIView()
        view.addSubview(imageView)
        imageView.snp.makeConstraints { make in
            make.top.bottom.centerX.equalToSuperview()
            make.width.height.equalTo(150)
        }
        
        return view
    }()
    
    /// Guest  image view
    private lazy var imageView = UIImageView(contentMode: .scaleAspectFill)
    
    /// Title Label
    private lazy var titleLabel = UILabel(font: Font.semiBold.of(size: 18),
                                          textColor: Color.txtTitle,
                                          textAlignment: .center)

    /// Description Label
    private lazy var descLabel1 = UILabel(font: Font.light.of(size: 14),
                                          numberOfLines: 0,
                                          textColor: Color.txtParagraph)
    
    private lazy var descLabel2 = UILabel(font: Font.light.of(size: 14),
                                          numberOfLines: 0,
                                          textColor: Color.txtParagraph)
    
    /// Learn More Button
    private lazy var learnMoreButton = UIButton(type: .system,
                                                backgroundColor: Color.btn2nd,
                                                tintColor: .clear,
                                                titleFont: Font.medium.of(size: 14),
                                                title: "key0807".localized(),
                                                normalTitleColor: Color.txtInverted,
                                                cornerRadius: 22)
    
    /// Got It Button
    fileprivate lazy var gotItButton = UIButton(type: .system,
                                                backgroundColor: Color.btn4th,
                                                tintColor: .clear,
                                                titleFont: Font.medium.of(size: 14),
                                                title: "key0808".localized(),
                                                normalTitleColor: Color.txtParagraph,
                                                cornerRadius: 22)
    
    public override func viewDidLoad() {
        super.viewDidLoad()
        
        setupUI()
        bindActions()
    }
    
    public func setupUI() {
        view.addSubviews([contentContainer,
//                          learnMoreButton,
                          gotItButton])
        // Content Stack View
        contentContainer.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide)
            make.left.equalTo(12)
            make.right.equalTo(-12)
            make.bottom.equalTo(gotItButton.snp.top)
        }
        contentStackView.addArrangedSubviews([imageContainer,
                                              titleLabel,
                                              descLabel1,
                                              descLabel2])
        contentStackView.setCustomSpacing(32, after: imageContainer)
        
        // Learn More Button
//        learnMoreButton.snp.makeConstraints { make in
//            make.bottom.equalTo(gotItButton.snp.top).offset(-8)
//            make.left.equalTo(12)
//            make.right.equalTo(-12)
//            make.height.equalTo(44)
//        }
        // Got It Button
        gotItButton.snp.makeConstraints { make in
            make.bottom.equalTo(-((keyWindow?.safeAreaInsets.bottom ?? 0) + 20))
            make.left.equalTo(12)
            make.right.equalTo(-12)
            make.height.equalTo(44)
        }
    }
    
    public func bind(viewModel: SuspendAccountSuccessfulViewModel) {
        self.viewModel = viewModel
        
        let input = SuspendAccountSuccessfulViewModel.Input(
            onViewAppear: rx.viewWillAppear.take(1).mapToVoid()
        )
        
        let output = viewModel.transform(input: input)
        
        output.displayData.drive(onNext: { [unowned self] in
            displayUI($0)
        }).disposed(by: disposeBag)
    }
    
    public func bindActions() {
        gotItButton.rx.tap.subscribe(onNext: { [unowned self] in
            viewModel.navigate(to: .logout)
        }).disposed(by: disposeBag)
        
        learnMoreButton.rx.tap.subscribe(onNext: { [unowned self] in
            viewModel.navigate(to: .faqs)
        }).disposed(by: disposeBag)
    }
    
    private func displayUI(_ state: SuspendAccountAction) {
        switch state {
        case .closeAccount:
            titleLabel.text = "key0804".localized()
            imageView.image = .image(named: "ic_goodbye")
            descLabel1.text = "key0805".localized()
            
            descLabel2.text = "key0806".localized()
            
        case .delete:
            break
        }
    }
}

// MARK: - Reactive
public extension Reactive where Base: SuspendAccountSuccessfulViewController {
    
    var tapGotIt: Observable<Void> {
        base.gotItButton.rx.tap.asObservable()
    }
}
