//
//  TermsViewController.swift
//  AppSetting
//
//  Created by <PERSON><PERSON><PERSON> on 2/8/67.
//
import Core
import CUIModule
import APILayer
import RxSwift
import Storage
import SharedData

final class TermsViewController: BaseViewController, AnyView {
    
    // MARK: UI properties
    private lazy var headerView = {
        let displayModel = CUINavigationBar.DisplayModel(leftIcon: .image(named: "merit_ic_back"),
                                                         titleView: titleStackView)
        let headerView = CUINavigationBar(displayModel: displayModel)
        
        return headerView
    }()
    
    private lazy var titleStackView = {
        let stackView = UIStackView(alignment: .center,
                                    spacing: 4)
        stackView.addArrangedSubviews([iconView,
                                       titleLabel])
        
        return stackView
    }()
    
    private lazy var iconView = UIImageView(image: AppSetting.termsAndConditions.icon)
    
    private lazy var titleLabel = UILabel(font: Font.semiBold.of(size: 14),
                                          text: AppSetting.termsAndConditions.title,
                                          textColor: Color.txtTitle)
    
    private lazy var scrollView = {
        let scrollView = UIScrollView(backgroundColor: .clear,
                                      showsVerticalScrollIndicator: false)
        scrollView.addSubview(contentView)
        contentView.snp.makeConstraints { make in
            make.width.edges.equalToSuperview()
        }
        
        return scrollView
    }()
    
    private lazy var contentView = {
        let view = UIView(backgroundColor: .clear)
        view.addSubview(stackView)
        stackView.snp.makeConstraints { make in
            make.top.equalTo(12)
            make.left.equalTo(12)
            make.right.equalTo(-12)
            make.bottom.equalTo(-34)
        }
        
        return view
    }()
    
    private let stackView = UIStackView(axis: .vertical,
                                        spacing: 8)
    
    // MARK: Life cycle
    override func viewDidLoad() {
        super.viewDidLoad()
        
        setupUI()
        bindActions()
    }
    
    func setupUI() {
        view.backgroundColor = Color.bgDefault
        // Header View, Scroll View
        view.addSubviews([scrollView,
                          headerView])
        // Header View
        headerView.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide.snp.top)
            make.left.right.equalToSuperview()
            make.height.equalTo(44)
        }
        scrollView.snp.makeConstraints { make in
            make.top.equalTo(headerView.snp.bottom)
            make.bottom.left.right.equalToSuperview()
        }
        
        Terms.allCases.forEach {
            stackView.addArrangedSubview($0.displayStackView)
        }
    }
    
    func bindActions() {
        headerView.rx.leftTap.subscribe(onNext: { [weak self] in
            self?.navigationController?.popViewController(animated: true)
        }).disposed(by: disposeBag)
    }
}

// MARK: - Display Model
// swiftlint: disable all
extension TermsViewController {
    enum Terms: CaseIterable {
        case description
        case useOfTheApp
        case registration
        case security
        case tradingRisks
        case noInvestmentAdvice
        case intellectualProperty
        case thirdPartyLinks
        case termination
        case changesToTerms
        case governingLaw

        var title: String {
            switch self {
            case .description:
                return ""
            case .useOfTheApp:
                return "USE OF THE APP"
            case .registration:
                return "REGISTRATION"
            case .security:
                return "SECURITY"
            case .tradingRisks:
                return "TRADING RISKS"
            case .noInvestmentAdvice:
                return "NO INVESTMENT ADVICE"
            case .intellectualProperty:
                return "INTELLECTUAL PROPERTY"
            case .thirdPartyLinks:
                return "THIRD-PARTY LINKS"
            case .termination:
                return "TERMINATION"
            case .changesToTerms:
                return "CHANGES TO TERMS"
            case .governingLaw:
                return "GOVERNING LAW"
            }
        }

        var titleLabel: UILabel? {
            guard !title.isEmpty else { return nil }

            return UILabel(font: Font.medium.of(size: 14),
                           text: title,
                           textColor: Color.txtTitle)
        }

        var desc: String {
            switch self {
            case .description:
                return "These Terms and Conditions (\"Terms\") govern your use of the securities trading application (\"App\") provided by TradeX, including any updates, enhancements, or new features added to the App. By accessing or using the App, you agree to be bound by these Terms. If you do not agree to these Terms, please do not use the App."

            case .useOfTheApp:
                return "You must be at least 18 years old to use the App. By using the App, you represent and warrant that you are at least 18 years old and have the legal capacity to enter into this agreement."

            case .registration:
                return "You may be required to register an account to use certain features of the App. You agree to provide accurate, current, and complete information during the registration process and to update such information to keep it accurate, current, and complete."

            case .security:
                return "You are responsible for maintaining the confidentiality of your account credentials and for all activities that occur under your account. You agree to notify us immediately of any unauthorized use of your account or any other breach of security."

            case .tradingRisks:
                return "You acknowledge and understand that trading securities involves risks, including the risk of loss of capital. You agree to conduct your own research and due diligence before making any investment decisions."

            case .noInvestmentAdvice:
                return "The information provided on the App is for informational purposes only and should not be construed as investment advice or a recommendation to buy or sell any security. You acknowledge that any investment decisions you make based on information available on the App are made at your own risk."

            case .intellectualProperty:
                return "The App and its content, including but not limited to text, graphics, logos, images, and software, are the property of TradeX or its licensors and are protected by copyright, trademark, and other intellectual property laws."

            case .thirdPartyLinks:
                return "The App may contain links to third-party websites or services that are not owned or controlled by TradeX. We have no control over, and assume no responsibility for, the content, privacy policies, or practices of any third-party websites or services. You agree that TradeX shall not be liable for any loss or damage arising from your use of any third-party website or service."

            case .termination:
                return "We reserve the right to terminate or suspend your access to the App at any time, with or without cause, and without prior notice or liability."

            case .changesToTerms:
                return "We reserve the right to update or modify these Terms at any time without prior notice. Your continued use of the App after any such changes constitutes your acceptance of the new Terms."
                
            case .governingLaw:
                return "These Terms shall be governed by and construed in accordance with the laws of [Jurisdiction], without regard to its conflict of law principles."
            }
        }
        
        var descLabel: UILabel {
            UILabel(font: Font.light.of(size: 14),
                    numberOfLines: 0,
                    text: desc,
                    textColor: Color.txtParagraph)
        }

        var displayStackView: UIStackView {
            let stackView = UIStackView(axis: .vertical,
                                        spacing: 8)
            if let titleLabel = titleLabel {
                stackView.addArrangedSubview(titleLabel)
            }

            stackView.addArrangedSubview(descLabel)
            
            if self != .governingLaw {
                stackView.addArrangedSubview(CUIDivider(canvasHeight: 8))
            }

            return stackView
        }
    }
}
// swiftlint: enable all
