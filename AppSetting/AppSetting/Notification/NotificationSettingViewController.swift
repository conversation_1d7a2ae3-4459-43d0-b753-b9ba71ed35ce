//
//  NotificationSettingViewController.swift
//  Notification
//
//  Created by <PERSON><PERSON><PERSON> on 2/20/67.
//
import Core
import CUIModule
import RxSwift
import RxCocoa
import Utils

final class NotificationSettingViewController: BaseViewController, VMView {

    private lazy var navView: CUINavigationBar = {
        let view = CUINavigationBar()
        let navTitle = ThemeableButton(
            titleFont: Font.semiBold.of(size: 14),
            title: "NOTIFICATIONS",
            normalTitleColor: Color.txtTitle,
            normalImage: .image(named: "icnoti"),
            titleInsets: UIEdgeInsets(top: 0, left: 2, bottom: 0, right: -2),
            imageInsets: UIEdgeInsets(top: 0, left: -2, bottom: 0, right: 2),
            isUserInteractionEnabled: false,
            themeType: .textHighlight(icon: true, text: false)
        )
        view.updateUI(with: CUINavigationBar.DisplayModel(
            leftIcon: .image(named: "iconback"),
            titleView: navTitle
        ))
        return view
    }()

    private lazy var scrollView = UIScrollView(
        backgroundColor: .clear,
        showsVerticalScrollIndicator: false
    )

    private lazy var contentView = UIView(backgroundColor: .clear)

    private let stackView = UIStackView(
        axis: .vertical,
        spacing: 8
    )

    private lazy var titleStackView: UIStackView = {
        let stackView = UIStackView(
            axis: .horizontal,
            spacing: 4
        )

        let iconView = ThemeableImageView(
            contentMode: .scaleAspectFit,
            image: .image(named: "icnoti"),
            themeType: .textHighlight
        )
        let titleLabel = UILabel(
            font: Font.semiBold.of(size: 14),
            text: "IN-APP NOTIFICATIONS",
            textColor: Color.txtLabel
        )

        stackView.addArrangedSubviews([iconView, titleLabel])
        iconView.snp.makeConstraints { make in
            make.width.equalTo(14)
        }
        return stackView
    }()

    // swiftlint: disable line_length
    private lazy var descLabel: UILabel = {
        let label = UILabel(
            font: Font.medium.of(size: 12),
            numberOfLines: 0,
            text: "Turning off notifications will disable the system feedback when a corresponding events has occurred. The notification icon in the top bar will still indicate if there are notifications to be read.",
            textColor: Color.txtParagraph
        )
        label.setLineHeight(lineHeight: 1.23)
        return label
    }()
    // swiftlint: enable line_length

    private lazy var settingStackView = UIStackView(
        axis: .vertical,
        spacing: 4
    )

    private lazy var buttonContainer = UIView(
        backgroundColor: Color.bgDefault
    )

    private lazy var resetButton = ThemeableButton(
        titleFont: Font.bold.of(size: 16),
        title: "RESET TO DEFAULT",
        normalTitleColor: Color.txtInverted,
        normalBgColor: Color.bgButtonNeutral,
        cornerRadius: 22,
        isEnable: true,
        themeType: .secondary
    )

    private lazy var resetBottomSheet = NotiSettingResetBottomSheet()

    var viewModel: NotificationSettingViewModel!
    var settingViews: [NotificaitonSettingView] = []
    var onChangeValue = PublishSubject<(
        setting: NotificationSettingViewModel.SettingType,
        value: Bool
    )>()

    private var dropShadowOpacity: Float {
        return currentAppTheme == .light ? 0.07 : 0.5
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        bindActions()
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        navigationBarHidden(true)
    }

    func setupUI() {
        view.backgroundColor = Color.bgDefault

        view.addSubviews([navView, scrollView, buttonContainer])
        navView.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide.snp.top)
            make.left.right.equalToSuperview()
            make.height.equalTo(44)
        }
        scrollView.snp.makeConstraints { make in
            make.top.equalTo(navView.snp.bottom)
            make.left.right.equalToSuperview()
            make.bottom.equalTo(buttonContainer.snp.top)
        }
        buttonContainer.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.bottom.equalTo(view.safeAreaLayoutGuide.snp.bottom)
        }

        scrollView.addSubview(contentView)
        contentView.snp.makeConstraints { make in
            make.edges.width.equalToSuperview()
        }

        contentView.addSubview(stackView)
        stackView.snp.makeConstraints { make in
            make.top.left.equalTo(16)
            make.bottom.right.equalTo(-16)
        }

        stackView.addArrangedSubviews([titleStackView, descLabel, settingStackView])
        titleStackView.snp.makeConstraints { make in
            make.height.equalTo(14)
        }

        // Mark all read button
        buttonContainer.dropShadow(
            alpha: 1, opacity: dropShadowOpacity,
            offset: .init(width: 0, height: -6),
            radius: 4
        )
        buttonContainer.addSubview(resetButton)
        resetButton.snp.makeConstraints { make in
            make.top.left.equalTo(12)
            make.bottom.right.equalTo(-12)
            make.height.equalTo(44)
        }
    }

    func bindActions() {
        navView.rx.leftTap.subscribe(onNext: { [weak self] in
            self?.navigationController?.popViewController(animated: true)
        }).disposed(by: disposeBag)

        resetButton.rx.tap.subscribe(onNext: { [weak self] in
            self?.resetBottomSheet.present()
        }).disposed(by: disposeBag)
    }

    func bind(viewModel: NotificationSettingViewModel) {
        self.viewModel = viewModel

        let input = NotificationSettingViewModel.Input(
            onViewAppear: rx.viewWillAppear.take(1).mapToVoid(), 
            onChangeSetting: onChangeValue,
            onResetSetting: resetBottomSheet.onTapReset
        )

        let output = viewModel.transform(input: input)

        output.displayData
            .drive(onNext: { [weak self] in
                self?.displayUI($0)
            })
            .disposed(by: disposeBag)

        output.changeSetting
            .drive(onNext: {
                // Setting change successfully
            })
            .disposed(by: disposeBag)

        output.updateUI
            .drive(onNext: { [weak self] in
                self?.updateUI($0)
            })
            .disposed(by: disposeBag)
    }
}

// MARK: - Update UI
extension NotificationSettingViewController {

    private func displayUI(_ displayModels: [NotificationSettingViewModel.DisplayModel]) {

        settingViews.removeAll()
        settingStackView.removeAllArrangedSubviews()
        displayModels.forEach { data in
            let settingView = NotificaitonSettingView(
                title: data.setting.title,
                isOn: data.isOn
            )
            settingView.tag = data.setting.rawValue
            settingStackView.addArrangedSubview(settingView)
            settingViews.append(settingView)

            settingView.rx.tap.subscribe(onNext: { [weak self] value in
                self?.onChangeValue.onNext((setting: data.setting, value: value))
            }).disposed(by: disposeBag)
        }
    }

    private func updateUI(_ displayModels: [NotificationSettingViewModel.DisplayModel]) {
        
        settingViews.forEach { settingView in
            if let data = displayModels.first(where: { $0.setting.rawValue == settingView.tag }) {
                settingView.updateData(isOn: data.isOn)
            }
        }
    }

}
