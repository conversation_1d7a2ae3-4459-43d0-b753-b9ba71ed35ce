//
//  NotificationSettingViewModel.swift
//  Notification
//
//  Created by <PERSON><PERSON><PERSON> on 2/20/67.
//
import APILayer
import Core
import CUIModule
import RxCocoa
import RxSwift
import RxRelay
import XCoordinator
import Storage

public class NotificationSettingViewModel: AnyViewModel {

    let router: UnownedRouter<AppSettingsRoute>

    public struct Input {
        let onViewAppear: Observable<Void>
        let onChangeSetting: Observable<(setting: SettingType, value: Bool)>
        let onResetSetting: Observable<Void>
    }

    public struct Output {
        let displayData: Driver<[DisplayModel]>
        let changeSetting: Driver<Void>
        let updateUI: Driver<[DisplayModel]>
    }

    init(router: UnownedRouter<AppSettingsRoute>) {
        self.router = router
    }

    public func transform(input: Input) -> Output {

        let displayData = input.onViewAppear
            .map { SettingType.allCases }
            .map(mapToDisplayModel)

        let changeSetting = input.onChangeSetting
            .onNext {
                self.saveChangeSetting(
                    setting: $0.setting, value: $0.value
                )
            }
            .mapToVoid()

        let resetSetting = input.onResetSetting
            .map(resetAllSetting)

        return Output(
            displayData: displayData.asDriverOnErrorNever(),
            changeSetting: changeSetting.asDriverOnErrorNever(),
            updateUI: resetSetting.asDriverOnErrorNever()
        )
    }

    func mapToDisplayModel(settings: [SettingType]) -> [DisplayModel] {
        return settings.map { setting in
            var isOn: Bool?
            switch setting {
            case .order:
                isOn = LocalPreference.notiSettingOrder ?? setting.defaultValue
            case .transaction:
                isOn = LocalPreference.notiSettingTransaction ?? setting.defaultValue
            case .news, .system:
                isOn = setting.defaultValue
            }
            return DisplayModel(setting: setting, isOn: isOn)
        }
    }

    func saveChangeSetting(setting: SettingType, value: Bool) {
        switch setting {
        case .order:
            LocalPreference.notiSettingOrder = value
        case .transaction:
            LocalPreference.notiSettingTransaction = value
        case .news, .system:
            break
        }
    }

    func resetAllSetting() -> [DisplayModel] {
        LocalPreference.notiSettingOrder = SettingType.order.defaultValue
        LocalPreference.notiSettingTransaction = SettingType.transaction.defaultValue

        return mapToDisplayModel(settings: SettingType.allCases)
    }
}

// MARK: - Localization & Data
extension NotificationSettingViewModel {

    struct DisplayModel {
        let setting: SettingType
        let isOn: Bool?
    }

    public enum SettingType: Int, CaseIterable {
        case order
        case transaction
        case news
        case system

        public var title: String {
            switch self {
            case .order:
                return "Notify me when my order is updated."
            case .transaction:
                return "Notify me when my transaction is updated."
            case .news:
                return "Notify me when the news feed is updated."
            case .system:
                return "Notify me when the system is updated."
            }
        }

        public var defaultValue: Bool? {
            switch self {
            case .order, .transaction:
                return true
            case .news, .system:
                return nil
            }
        }
    }
}
