//
//  CustomThemeCreateViewController.swift
//  AppSetting
//
//  Created by <PERSON><PERSON> on 2/12/67.
//
import Core
import CUIModule
import RxSwift
import RxCocoa
import Utils
import SharedData
import Storage

final class CustomThemeCreateViewController: BaseViewController, VMView {

    private lazy var navView: CUINavigationBar = {
        let view = CUINavigationBar(
            displayModel: CUINavigationBar.DisplayModel(
                leftIcon: .image(named: "iconback"),
                titleView: titleLabel
            )
        )
        return view
    }()

    private lazy var titleLabel = UILabel(
        font: Font.semiBold.of(size: 14),
        text: "CREATE A THEME",
        textColor: Color.txtTitle
    )

    private lazy var stepView = CUIStepIndicatorView(totalSteps: self.viewModel.steps.count)

    private lazy var pageContainerView = UIView(backgroundColor: .clear)

    private lazy var pageController: UIPageViewController = {
        let pageController = UIPageViewController(
            transitionStyle: .scroll,
            navigationOrientation: .horizontal,
            options: nil
        )
        pageController.view.backgroundColor = .clear
        return pageController
    }()

    var viewModel: CustomThemeCreateViewModel!
    private var nameViewModel: CustomThemeNameViewModel?
    private var styleViewModel: CustomThemeStyleViewModel?

    private let onTapBack = PublishSubject<Void>()
    private let onTapNext = PublishSubject<Void>()

    private var pages: [UIViewController] = []

    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        bindActions()
        setupPageController()
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        navigationBarHidden(true)
    }

    func setupUI() {
        view.backgroundColor = Color.bgDefault

        view.addSubviews([navView, stepView, pageContainerView])
        navView.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide.snp.top)
            make.left.right.equalToSuperview()
        }

        stepView.snp.makeConstraints { make in
            make.top.equalTo(navView.snp.bottom).offset(16)
            make.left.equalTo(12)
            make.right.equalTo(12)
            make.height.equalTo(30)
        }

        pageContainerView.snp.makeConstraints { make in
            make.top.equalTo(stepView.snp.bottom)
            make.left.right.equalToSuperview()
            make.bottom.equalTo(view.safeAreaLayoutGuide.snp.bottom).offset(-16)
        }
    }

    func bindActions() {

    }

    func bind(viewModel: CustomThemeCreateViewModel) {
        self.viewModel = viewModel

        let input = CustomThemeCreateViewModel.Input(
            onViewAppear: rx.viewWillAppear.mapToVoid(),
            onTapBack: Observable.merge(navView.rx.leftTap, onTapBack),
            onTapNext: onTapNext
        )

        let output = viewModel.transform(input: input)

        output.displayData.drive(onNext: { [weak self]  in
            self?.displayUI(data: $0)
        }).disposed(by: disposeBag)

        output.changeStep.drive(onNext: { [weak self] in

            switch $0.state {
            case .quit:
                self?.navigationController?.popViewController(animated: true)
            case .onGoing:
                self?.stepView.updateCurrentStep($0.step.rawValue + 1)
                self?.setPage(
                    index: $0.step.rawValue,
                    direction: $0.direction,
                    animated: true
                )
            case .complete:
                break
            }
        }).disposed(by: disposeBag)

    }

    func bindChildViewModels() {
        nameViewModel?.onTapNext
            .bind(to: onTapNext)
            .disposed(by: disposeBag)

        nameViewModel?.onNamedChanged.subscribe(onNext: { [weak self] in
            self?.styleViewModel?.themeName.onNext($0)
        }).disposed(by: disposeBag)

        styleViewModel?.onEditName
            .bind(to: onTapBack)
            .disposed(by: disposeBag)

        styleViewModel?.onCreateTheme
            .bind(to: viewModel.onThemeCreated)
            .disposed(by: disposeBag)

        styleViewModel?.onEditTheme
            .bind(to: viewModel.onThemeUpdated)
            .disposed(by: disposeBag)
    }
}
// MARK: - Update UI
extension CustomThemeCreateViewController {

    private func displayUI(data: CustomThemeModel?) {
        if let data = data { // Edit current theme
            titleLabel.text = "EDIT \(data.name)"
        } else {
            titleLabel.text = "CREATE A THEME"
        }
    }
}
// MARK: - Page View Controller
extension CustomThemeCreateViewController {
    func setupPageController() {
        self.addChild(pageController)
        pageContainerView.addSubview(pageController.view)
        pageController.view.frame = pageContainerView.bounds
        pageController.view.translatesAutoresizingMaskIntoConstraints = false
        pageController.didMove(toParent: self)
        setUpChildViewControllers()
    }

    func setUpChildViewControllers() {
        // If pages are already setup, return
        guard pages.isEmpty else { return }

        self.viewModel.steps.forEach {
            switch $0 {
            case .information:
                let viewModel = CustomThemeNameViewModel(
                    router: self.viewModel.router,
                    themeName: self.viewModel.data?.name
                )
                nameViewModel = viewModel
                let nameViewController = CustomThemeNameViewController()
                nameViewController.bind(viewModel: viewModel)
                pages.append(nameViewController)
            case .style:
                let viewModel = CustomThemeStyleViewModel(
                    router: self.viewModel.router,
                    data: self.viewModel.data
                )
                styleViewModel = viewModel
                let styleViewController = CustomThemeStyleViewController()
                styleViewController.bind(viewModel: viewModel)
                pages.append(styleViewController)
            }
        }
        setPage(index: viewModel.currentStep.rawValue, direction: .forward, animated: false)
        bindChildViewModels()
    }

    func setPage(
        index: Int,
        direction: UIPageViewController.NavigationDirection,
        animated: Bool
    ) {
        guard index < pages.count else { return }
        pageController.setViewControllers(
            [pages[index]],
            direction: direction,
            animated: animated
        )
    }
}
