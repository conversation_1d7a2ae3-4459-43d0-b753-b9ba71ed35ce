//
//  CustomThemeStyleViewController.swift
//  AppSetting
//
//  Created by <PERSON><PERSON> on 2/13/67.
//
import Core
import CUIModule
import RxSwift
import RxCocoa
import Utils
import SharedData
import Storage

final class CustomThemeStyleViewController: BaseViewController, VMView {

    private lazy var scrollView = UIScrollView(
        backgroundColor: .clear,
        showsVerticalScrollIndicator: false
    )

    private lazy var contentView = UIView(
        backgroundColor: .clear
    )

    private lazy var stackView = UIStackView(
        axis: .vertical,
        spacing: 16
    )

    private lazy var headerView = CustomThemeHeaderView()

    private lazy var titleLabel = UILabel()

    private lazy var segmentControl = CUISegmentControl()

    private lazy var styleStackView = UIStackView(
        axis: .horizontal,
        alignment: .top,
        spacing: 16
    )

    private lazy var themePreview = ThemePreview()

    private lazy var styleSelectionStackView = UIStackView(
        axis: .vertical,
        spacing: 8
    )

    private lazy var presetStyleSelectionViews = [
        ThemePresetStyleSelectionView(colorType: .preset2),
        ThemePresetStyleSelectionView(colorType: .preset3),
        ThemePresetStyleSelectionView(colorType: .preset4),
        ThemePresetStyleSelectionView(colorType: .preset5)
    ]

    private lazy var customSmartColorView = ThemeSmartColorView()

    private lazy var customStyleSelectionViews = [
        ThemeCustomStyleSelectionView(colorSet: .primary),
        ThemeCustomStyleSelectionView(colorSet: .secondary),
        ThemeCustomStyleSelectionView(colorSet: .third),
        ThemeCustomStyleSelectionView(colorSet: .textHighlight)
    ]

    private lazy var consentView = CUIConsentView(
        title: "Activate this theme upon creation."
    )

    private lazy var buttonContainer = UIView(
        backgroundColor: Color.bgDefault
    )

    private lazy var createButton = ThemeableButton(
        titleFont: Font.bold.of(size: 16),
        title: "CREATE",
        normalTitleColor: Color.pitchBlack,
        disabledTitleColor: Color.txtDisabled,
        normalBgColor: Color.bgButtonDefault,
        disabledBgColor: Color.bgButtonDisabled,
        cornerRadius: 22,
        isEnable: false,
        themeType: .primary
    )

    private lazy var colorSelectionView = ThemeColorSelectionView()
    private let selectPreset = PublishSubject<ThemeColorPresetType>()

    var viewModel: CustomThemeStyleViewModel!

    private var dropShadowOpacity: Float {
        return currentAppTheme == .light ? 0.07 : 0.5
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        bindActions()
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        navigationBarHidden(true)
    }

    func setupUI() {
        view.backgroundColor = .clear

        view.addSubviews([scrollView, buttonContainer])
        scrollView.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.bottom.equalTo(buttonContainer.snp.top)
        }

        scrollView.addSubview(contentView)
        contentView.snp.makeConstraints { make in
            make.edges.width.equalToSuperview()
        }

        colorSelectionView.isHidden = true
        contentView.addSubviews([stackView, colorSelectionView])
        stackView.snp.makeConstraints { make in
            make.top.left.equalTo(16)
            make.right.equalTo(-16)
            make.bottom.equalTo(-40)
        }

        colorSelectionView.snp.makeConstraints { make in
            make.top.equalTo(0)
            make.left.equalTo(12)
            make.right.equalTo(-12)
        }

        stackView.addArrangedSubviews([
            headerView, titleLabel, segmentControl,
            styleStackView, consentView
        ])

        styleStackView.clipsToBounds = false
        styleStackView.addArrangedSubviews([themePreview, styleSelectionStackView])
        themePreview.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.42)
        }

        styleSelectionStackView.addArrangedSubviews(
            presetStyleSelectionViews + [customSmartColorView] + customStyleSelectionViews
        )

        buttonContainer.dropShadow(
            alpha: 1, opacity: dropShadowOpacity,
            offset: .init(width: 0, height: -6),
            radius: 4
        )
        buttonContainer.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.bottom.equalTo(view.safeAreaLayoutGuide.snp.bottom)
        }

        buttonContainer.addSubview(createButton)
        createButton.snp.makeConstraints { make in
            make.top.left.equalTo(12)
            make.bottom.right.equalTo(-12)
            make.height.equalTo(44)
        }
    }

    func bindActions() {

        rx.viewWillAppear.take(1).subscribe(onNext: { [weak self] _ in
            self?.consentView.setConsent(isConsent: self?.viewModel.data?.isEnable ?? false)
        }).disposed(by: disposeBag)

        headerView.rx.tap
            .bind(to: viewModel.onEditName)
            .disposed(by: disposeBag)

        presetStyleSelectionViews.forEach { selectionView in
            selectionView.rx.tap.subscribe(onNext: { [weak self] in
                self?.selectPreset.onNext($0)
                self?.selectPresetColor(colorType: $0)
            }).disposed(by: disposeBag)
        }

        customStyleSelectionViews.forEach { selectionView in
            selectionView.rx.tap.subscribe(onNext: { [weak self] in
                self?.showColorSelection(colorSet: $0)
            }).disposed(by: disposeBag)
        }
    }

    func bind(viewModel: CustomThemeStyleViewModel) {
        self.viewModel = viewModel

        let input = CustomThemeStyleViewModel.Input(
            onViewAppear: rx.viewWillAppear.mapToVoid(),
            onChangeSegment: segmentControl.onTapped.compactMap { [weak self] index, _ in
                return self?.viewModel.segments[index]
            },
            onSelectColor: colorSelectionView.rx.select.map {
                (set: $0.set, color: $0.color,
                 smartColor: self.customSmartColorView.isOn)
            },
            onSelectPreset: selectPreset,
            onCreateTheme: createButton.rx.tap.map { self.consentView.isConsent }
        )

        let output = viewModel.transform(input: input)

        output.displayData.drive(onNext: { [weak self] in
            self?.displayUI(segment: $0.segment, data: $0.model, isEditing: $0.isEditing)
        }).disposed(by: disposeBag)

        output.changeSegment.drive(onNext: { [weak self] in
            self?.colorSelectionView.isHidden = true
            self?.customStyleSelectionViews.forEach { $0.setSelected(false) }
            self?.changeSegment(segment: $0.segment)
            self?.updatePreview(
                set: .primary,
                color: ThemeColor(rawValue: $0.model.primaryId) ?? .defaultPrimary
            )
            self?.updatePreview(
                set: .secondary,
                color: ThemeColor(rawValue: $0.model.secondaryId) ?? .defaultSecondary
            )
            self?.updatePreview(
                set: .third,
                color: ThemeColor(rawValue: $0.model.thirdId) ?? .defaultThird
            )
            self?.updatePreview(
                set: .textHighlight,
                color: ThemeColor(rawValue: $0.model.textHighLightId) ?? .defaultTextHighLight
            )
        }).disposed(by: disposeBag)

        output.colorSelected.drive(onNext: { [weak self] in
            self?.colorSelectionView.isHidden = true
            self?.updateCustomColorUI(set: $0.set, color: $0.color)
            if let smartSet = $0.smartColors {
                self?.updateAllCustomColors(smartSet: smartSet)
                self?.updatePreview(set: .primary, color: smartSet.primary)
                self?.updatePreview(set: .secondary, color: smartSet.secondary)
                self?.updatePreview(set: .third, color: smartSet.third)
                self?.updatePreview(set: .textHighlight, color: smartSet.textHighlight)
            } else {
                self?.updatePreview(set: $0.set, color: $0.color)
            }
        }).disposed(by: disposeBag)

        output.presetSelected.drive(onNext: { [weak self] in
            self?.updatePreview(set: .primary, color: $0.colorSet.primary)
            self?.updatePreview(set: .secondary, color: $0.colorSet.secondary)
            self?.updatePreview(set: .third, color: $0.colorSet.third)
            self?.updatePreview(set: .textHighlight, color: $0.colorSet.textHighlight)
        }).disposed(by: disposeBag)

        output.enableButton.drive(onNext: { [weak self] in
            self?.createButton.isEnabled = $0
        }).disposed(by: disposeBag)

        output.themeCreated.drive(onNext: { [weak self] in
            self?.navigationController?.popViewController(animated: true)
        }).disposed(by: disposeBag)
    }

    private func changeSegment(segment: CustomThemeStyleViewModel.SegmentType) {
        presetStyleSelectionViews.forEach {
            $0.isHidden = segment != .preset
        }

        customSmartColorView.isHidden = segment != .custom

        customStyleSelectionViews.forEach {
            $0.isHidden = segment != .custom
        }
    }

    private func selectPresetColor(colorType: ThemeColorPresetType?) {
        presetStyleSelectionViews.forEach {
            $0.setSelected(colorType == $0.colorType)
        }
    }

    private func showColorSelection(colorSet: ThemeColorSet?) {
        var selectedView: UIView?
        customStyleSelectionViews.forEach {
            $0.setSelected(colorSet == $0.colorSet)
            if colorSet == $0.colorSet {
                selectedView = $0
            }
        }

        guard let selectedView = selectedView else { 
            colorSelectionView.isHidden = true
            return
        }
        let superViewsPosY = [stackView.frame.origin.y, styleStackView.frame.origin.y].reduce(0, +)
        let posY = superViewsPosY + selectedView.frame.origin.y + selectedView.frame.height + 4
        colorSelectionView.snp.updateConstraints { make in
            make.top.equalTo(posY)
        }
        colorSelectionView.setData(
            colorSet: colorSet ?? .primary,
            selectedColor: viewModel.selectedColor(for: colorSet)
        )
        colorSelectionView.isHidden = false
    }

    private func updateCustomColorUI(set: ThemeColorSet?, color: ThemeColor) {
        guard let set = set,
              let selectionView = customStyleSelectionViews.filter({
                  $0.colorSet == set
              }).first else {
            return
        }
        selectionView.setSelected(false)
        selectionView.updateSelectedColor(color.color)
    }
}
// MARK: - Update UI
extension CustomThemeStyleViewController {

    private func displayUI(
        segment: CustomThemeStyleViewModel.SegmentType,
        data: CustomThemeModel,
        isEditing: Bool
    ) {

        headerView.setData(title: "NAME:", caption: data.name)

        titleLabel.highlight(
            "STEP 02: ASSIGN STYLE",
            normalColor: Color.txtLabel,
            normalFont: Font.semiBold.of(size: 12),
            highlightText: ["ASSIGN STYLE"],
            highlightColor: Color.txtTitle,
            highlightFont: Font.semiBold.of(size: 12)
        )

        segmentControl.setData(
            titles: viewModel.segments.map { $0.rawValue },
            selectedIndex: viewModel.segments.firstIndex(of: segment) ?? 0,
            config: CUISegmentControl.Config(
                backgroundColor: .clear,
                cornerRadius: 0,
                itemPadding: 0,
                itemSpacing: 8,
                indicatorColor: Color.bgDefaultAccent,
                indicatorCornerRadius: 4
            )
        )

        if isEditing {
            if let presetColor = ThemeColorPresetType(title: data.presetName) {
                selectPresetColor(colorType: presetColor)
            } else {
                updateAllCustomColors(smartSet: ThemeColorPreset(model: data))
            }
            createButton.isEnabled = true
            createButton.setTitle("UPDATE", for: .normal)
        } else {
            createButton.setTitle("CREATE", for: .normal)
        }
    }

    private func updateAllCustomColors(smartSet: ThemeColorPreset) {
        customStyleSelectionViews.forEach {
            switch $0.colorSet {
            case .primary: $0.updateSelectedColor(smartSet.primary.color)
            case .secondary: $0.updateSelectedColor(smartSet.secondary.color)
            case .third: $0.updateSelectedColor(smartSet.third.color)
            case .textHighlight: $0.updateSelectedColor(smartSet.textHighlight.color)
            }
        }
    }

    private func updatePreview(set: ThemeColorSet, color: ThemeColor) {
        switch set {
        case .primary:
            themePreview.updatePrimaryColor(color)
        case .secondary:
            themePreview.updateSecondaryColor(color)
        case .third:
            themePreview.updateBadgeColor(
                bgColor: color,
                textColor: viewModel.getColor(for: .textHighlight) ?? .defaultTextHighLight
            )
        case .textHighlight:
            themePreview.updateTextColor(color)
        }
    }
}
