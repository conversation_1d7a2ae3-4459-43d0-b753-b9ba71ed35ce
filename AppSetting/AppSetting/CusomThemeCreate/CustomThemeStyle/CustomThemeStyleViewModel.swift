//
//  CustomThemeStyleViewModel.swift
//  AppSetting
//
//  Created by <PERSON><PERSON> on 2/13/67.
//
import APILayer
import Core
import RxCocoa
import RxSwift
import RxRelay
import XCoordinator
import SharedData
import Storage
import CUIModule
import DGCharts
// swiftlint: disable large_tuple
public class CustomThemeStyleViewModel: AnyViewModel {
    let router: UnownedRouter<AppSettingsRoute>

    public struct Input {
        let onViewAppear: Observable<Void>
        let onChangeSegment: Observable<SegmentType>
        let onSelectColor: Observable<(set: ThemeColorSet, color: ThemeColor, smartColor: Bool)>
        let onSelectPreset: Observable<ThemeColorPresetType>
        let onCreateTheme: Observable<Bool>
    }

    public struct Output {
        let displayData: Driver<(segment: SegmentType, model: CustomThemeModel, isEditing: Bool)>
        let changeSegment: Driver<(segment: SegmentType, model: CustomThemeModel)>
        let colorSelected: Driver<(set: ThemeColorSet, color: ThemeColor, smartColors: ThemeColorPreset?)>
        let presetSelected: Driver<ThemeColorPresetType>
        let enableButton: Driver<Bool>
        let themeCreated: Driver<Void>
    }

    // Input from parent
    let themeName = PublishSubject<String>()

    // Output to parent
    let onEditName = PublishSubject<Void>()
    let onCreateTheme = PublishSubject<CustomThemeModel>()
    let onEditTheme = PublishSubject<CustomThemeModel>()

    let data: CustomThemeModel?
    private var currentSegment = SegmentType.preset

    var presetModel: CustomThemeModel?
    var customModel: CustomThemeModel?

    init(router: UnownedRouter<AppSettingsRoute>, data: CustomThemeModel?) {
        self.router = router
        self.data = data
    }

    public func transform(input: Input) -> Output {

        let displayData = input.onViewAppear.withLatestFrom(themeName)
            .map(setModels)
            .map {
                if $0.isEditing {
                    self.currentSegment = $0.data.presetName.isEmpty ? .custom : .preset
                }
                return (
                    segment: self.currentSegment,
                    model: $0.data,
                    isEditing: $0.isEditing
                )
            }
            .share()

        let initSegment = displayData
            .map {
                (segment: $0.segment, model: $0.model)
            }

        let changeSegment = input.onChangeSegment
            .filter { self.currentSegment != $0 }
            .onNext { self.currentSegment = $0 }
            .compactMap {
                switch $0 {
                case .preset: return self.presetModel
                case .custom: return self.customModel
                }
            }
            .map {
                (segment: self.currentSegment, model: $0)
            }.share()

        let selectColor = input.onSelectColor
            .map { selected -> (set: ThemeColorSet, color: ThemeColor, smartColors: ThemeColorPreset?) in
                if selected.smartColor, let smartSet = ThemeColorPreset.getSmartColors(themeColor: selected.color) {
                    self.setColor(set: .primary, color: smartSet.primary)
                    self.setColor(set: .secondary, color: smartSet.secondary)
                    self.setColor(set: .third, color: smartSet.third)
                    self.setColor(set: .textHighlight, color: smartSet.textHighlight)
                    return (set: selected.set, color: selected.color, smartColors: smartSet)
                } else {
                    self.setColor(set: selected.set, color: selected.color)
                    return (set: selected.set, color: selected.color, smartColors: nil)
                }
            }.share()

        let selectPreset = input.onSelectPreset
            .onNext {
                self.setPreset(type: $0)
            }.share()

        let validateStyle = Observable.merge(selectColor.mapToVoid(), selectPreset.mapToVoid())
            .map(validateForm)

        let enableButton = Observable.merge(
            validateStyle,
            changeSegment.map { _ in self.validateForm() }
        )

        let createTheme = input.onCreateTheme
            .compactMap(saveTheme)
            .mapToVoid()

        return Output(
            displayData: displayData.asDriverOnErrorNever(),
            changeSegment: Observable.merge(initSegment, changeSegment).asDriverOnErrorNever(),
            colorSelected: selectColor.asDriverOnErrorNever(),
            presetSelected: selectPreset.asDriverOnErrorNever(),
            enableButton: enableButton.asDriverOnErrorNever(),
            themeCreated: createTheme.asDriverOnErrorNever()
        )
    }
}
// MARK: - Business Logic
extension CustomThemeStyleViewModel {
    private func setModels(name: String) -> (data: CustomThemeModel, isEditing: Bool) {

        var model = self.data ?? CustomThemeModel(name: name)
        model.name = name
        if !model.presetName.isEmpty {
            if let presetType = ThemeColorPresetType(title: model.presetName) {
                self.setPreset(type: presetType)
            }

            presetModel = model
            customModel = CustomThemeModel(
                name: name, presetName: "",
                timeInterval: model.timeInterval
            )
        } else {
            self.setColors(
                primaryColor: ThemeColor(rawValue: model.primaryId),
                secondaryColor: ThemeColor(rawValue: model.secondaryId),
                thirdColor: ThemeColor(rawValue: model.thirdId),
                textHighlightColor: ThemeColor(rawValue: model.textHighLightId)
            )

            presetModel = CustomThemeModel(
                name: name, presetName: model.presetName,
                timeInterval: model.timeInterval
            )
            customModel = model
        }

        return (data: model, isEditing: self.data != nil)
    }

    private func setPreset(type: ThemeColorPresetType) {
        presetModel?.primaryId = type.colorSet.primary.rawValue
        presetModel?.secondaryId = type.colorSet.secondary.rawValue
        presetModel?.thirdId = type.colorSet.third.rawValue
        presetModel?.textHighLightId = type.colorSet.textHighlight.rawValue
        presetModel?.presetName = type.title
    }

    private func setColor(set: ThemeColorSet, color: ThemeColor?) {
        switch set {
        case .primary: customModel?.primaryId = color?.rawValue ?? -1
        case .secondary: customModel?.secondaryId = color?.rawValue ?? -1
        case .third: customModel?.thirdId = color?.rawValue ?? -1
        case .textHighlight: customModel?.textHighLightId = color?.rawValue ?? -1
        }
    }

    private func setColors(
        primaryColor: ThemeColor?,
        secondaryColor: ThemeColor?,
        thirdColor: ThemeColor?,
        textHighlightColor: ThemeColor?
    ) {
        customModel?.primaryId = primaryColor?.rawValue ?? -1
        customModel?.secondaryId = secondaryColor?.rawValue ?? -1
        customModel?.thirdId = thirdColor?.rawValue ?? -1
        customModel?.textHighLightId = textHighlightColor?.rawValue ?? -1
    }

    private func validateForm() -> Bool {
        switch currentSegment {
        case .preset:
            return presetModel?.isValid ?? false
        case .custom:
            return customModel?.isValid ?? false
        }
    }

    private func saveTheme(activate: Bool) -> CustomThemeModel? {
        var model: CustomThemeModel?
        switch currentSegment {
        case .preset: model = presetModel
        case .custom: model = customModel
        }

        guard var toSave = model else {
            return nil
        }

        toSave.isEnable = activate
        CustomThemeManager.shared.saveTheme(toSave)

        if self.data == nil {
            self.onCreateTheme.onNext(toSave)
        } else {
            self.onEditTheme.onNext(toSave)
        }

        return model
    }
}
// MARK: - Display Model
extension CustomThemeStyleViewModel {

    var segments: [SegmentType] {
        return SegmentType.allCases
    }

    enum SegmentType: String, CaseIterable {
        case preset = "PRESET"
        case custom = "CUSTOM"
    }

    func selectedColor(for set: ThemeColorSet?) -> ThemeColor? {
        guard let set = set, let model = customModel else {
            return nil
        }
        switch set {
        case .primary: return ThemeColor(rawValue: model.primaryId)
        case .secondary: return ThemeColor(rawValue: model.secondaryId)
        case .third: return ThemeColor(rawValue: model.thirdId)
        case .textHighlight: return ThemeColor(rawValue: model.textHighLightId)
        }
    }

    func getColor(for set: ThemeColorSet) -> ThemeColor? {
        switch currentSegment {
        case .preset:
            guard let model = presetModel else { return nil }
            return getColor(from: model, set: set)
        case .custom:
            guard let model = customModel else { return nil }
            return getColor(from: model, set: set)
        }
    }

    func getColor(from model: CustomThemeModel, set: ThemeColorSet) -> ThemeColor {
        switch set {
        case .primary:
            return ThemeColor(rawValue: model.primaryId) ?? .defaultPrimary
        case .secondary:
            return ThemeColor(rawValue: model.secondaryId) ?? .defaultSecondary
        case .third:
            return ThemeColor(rawValue: model.thirdId) ?? .defaultThird
        case .textHighlight:
            return ThemeColor(rawValue: model.textHighLightId) ?? .defaultTextHighLight
        }
    }
}
// swiftlint: enable large_tuple
