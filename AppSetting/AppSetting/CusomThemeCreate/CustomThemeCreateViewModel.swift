//
//  CustomThemeCreateViewModel.swift
//  AppSetting
//
//  Created by <PERSON><PERSON> on 2/12/67.
//
import APILayer
import Core
import RxCocoa
import RxSwift
import RxRelay
import XCoordinator
import SharedData
import Storage
import CUIModule
import DGCharts

public class CustomThemeCreateViewModel: AnyViewModel {
    let router: UnownedRouter<AppSettingsRoute>

    public struct Input {
        let onViewAppear: Observable<Void>
        let onTapBack: Observable<Void>
        let onTapNext: Observable<Void>
    }

    public struct Output {
        let displayData: Driver<CustomThemeModel?>
        // swiftlint: disable large_tuple
        let changeStep: Driver<(
            state: State,
            step: Step,
            direction: UIPageViewController.NavigationDirection
        )>
        // swiftlint: enable large_tuple
    }

    // Output to Coordinator
    let onThemeCreated: PublishSubject<CustomThemeModel>
    let onThemeUpdated: PublishSubject<CustomThemeModel>

    private(set) var data: CustomThemeModel?

    private(set) var currentStep = Step.information

    init(
        router: UnownedRouter<AppSettingsRoute>,
        data: CustomThemeModel?,
        onThemeCreated: PublishSubject<CustomThemeModel>,
        onThemeUpdated: PublishSubject<CustomThemeModel>
    ) {
        self.router = router
        self.data = data
        self.onThemeCreated = onThemeCreated
        self.onThemeUpdated = onThemeUpdated
    }

    public func transform(input: Input) -> Output {

        let displayData = input.onViewAppear
            .map { self.data }

        let forwardStep = input.onTapNext
            .map { self.forwardStep() }
            .map {
                (
                    state: $0,
                    step: self.currentStep,
                    direction: UIPageViewController.NavigationDirection.forward
                )
            }

        let backwardStep = input.onTapBack
            .map { self.backwardStep() }
            .map {
                (
                    state: $0,
                    step: self.currentStep,
                    direction: UIPageViewController.NavigationDirection.reverse
                )
            }

        let changeStep = Observable.merge(forwardStep, backwardStep)

        return Output(
            displayData: displayData.asDriverOnErrorNever(),
            changeStep: changeStep.asDriverOnErrorNever()
        )
    }

    func forwardStep() -> State {
        if currentStep == Step.allCases.last { return .complete }
        currentStep = Step(rawValue: currentStep.rawValue + 1) ?? currentStep
        return .onGoing
    }

    func backwardStep() -> State {
        if currentStep == Step.allCases.first { return .quit }
        currentStep = Step(rawValue: currentStep.rawValue - 1) ?? currentStep
        return .onGoing
    }
    func navigate(to route: AppSettingsRoute) {
        self.router.trigger(route)
    }
}
// MARK: - Display Model
public extension CustomThemeCreateViewModel {

    var steps: [Step] {
        return Step.allCases
    }

    enum State {
        case quit
        case onGoing
        case complete
    }

    enum Step: Int, CaseIterable {
        case information = 0
        case style = 1

        var title: String {
            switch self {
            case .information:
                return "SET UP GENERAL INFORMATION"
            case .style:
                return "ASSIGN STYLE"
            }
        }
    }
}
