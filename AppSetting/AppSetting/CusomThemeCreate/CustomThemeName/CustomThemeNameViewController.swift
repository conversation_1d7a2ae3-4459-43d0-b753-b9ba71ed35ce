//
//  CustomThemeNameViewController.swift
//  AppSetting
//
//  Created by <PERSON><PERSON> on 2/13/67.
//
import Core
import CUIModule
import RxSwift
import RxCocoa
import Utils
import SharedData
import Storage

final class CustomThemeNameViewController: BaseViewController, VMView {

    private lazy var titleLabel = UILabel()

    private lazy var container = UIView(
        backgroundColor: Color.bgDefaultTone,
        cornerRadius: 4
    )

    private lazy var stackView = UIStackView(
        axis: .vertical,
        spacing: 12
    )

    private lazy var textField = CUITextFieldWithTitle(
        title: "YOUR THEME NAME",
        titleTextColor: Color.txtLabel,
        titleErrorColor: Color.txtLabel,
        titleFont: Font.semiBold.of(size: 12),
        textFieldConfig: CUITextField.Config(
            textColor: Color.txtParagraph,
            placeholder: "Name Your Theme",
            backgroundColor: Color.bgDefault,
            clearButtonMode: .whileEditing,
            maxLength: 100,
            clearButtonImage: .image(named: "iconcrosscircle")
        )
    )

    private lazy var nextStepButton = ThemeableButton(
        type: .system,
        titleFont: Font.bold.of(size: 14),
        title: "GO TO NEXT STEP",
        normalTitleColor: Color.txtInverted,
        disabledTitleColor: Color.txtDisabled,
        normalBgColor: Color.bgButtonNeutral,
        disabledBgColor: Color.bgButtonDisabled,
        normalImage: .image(named: "ic_next_step"),
        disabledImage: .image(named: "ic_next_step_disabled"),
        semanticContentAttribute: .forceRightToLeft,
        cornerRadius: 4,
        titleInsets: UIEdgeInsets(top: 0, left: -2, bottom: 0, right: 2),
        imageInsets: UIEdgeInsets(top: 0, left: 2, bottom: 0, right: -2),
        themeType: .secondary
    )

    var viewModel: CustomThemeNameViewModel!

    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        bindActions()
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        navigationBarHidden(true)
    }

    func setupUI() {
        view.backgroundColor = .clear
        
        view.addSubviews([titleLabel, container])

        titleLabel.snp.makeConstraints { make in
            make.top.left.equalTo(16)
            make.right.equalTo(-16)
        }

        container.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(16)
            make.left.equalTo(12)
            make.right.equalTo(-12)
        }

        container.addSubview(stackView)
        stackView.snp.makeConstraints { make in
            make.top.left.equalTo(8)
            make.bottom.right.equalTo(-8)
        }

        stackView.addArrangedSubviews([textField, nextStepButton])
        nextStepButton.snp.makeConstraints { make in
            make.height.equalTo(44)
        }
    }

    func bindActions() {
        nextStepButton.rx.tap.subscribe(onNext: { [weak self] in
            self?.viewModel.onTapNext.onNext(())
        }).disposed(by: disposeBag)

        textField.rx.text
            .bind(to: viewModel.onNamedChanged)
            .disposed(by: disposeBag)
    }

    func bind(viewModel: CustomThemeNameViewModel) {
        self.viewModel = viewModel

        let input = CustomThemeNameViewModel.Input(
            onViewAppear: rx.viewWillAppear.mapToVoid(),
            onTextChanged: textField.rx.text
        )

        let output = viewModel.transform(input: input)

        output.displayData.drive(onNext: { [weak self] in
            self?.displayUI(themeName: $0)
        }).disposed(by: disposeBag)

        output.enableButton.drive(onNext: { [weak self] in
            self?.nextStepButton.isEnabled = $0
        }).disposed(by: disposeBag)

    }
}
// MARK: - Update UI
extension CustomThemeNameViewController {

    private func displayUI(themeName: String?) {

        titleLabel.highlight(
            "STEP 01: SET THEME NAME",
            normalColor: Color.txtLabel,
            normalFont: Font.semiBold.of(size: 12),
            highlightText: ["SET THEME NAME"],
            highlightColor: Color.txtTitle,
            highlightFont: Font.semiBold.of(size: 12)
        )

        textField.updateText(themeName)

        nextStepButton.isEnabled = !(themeName ?? "").isEmpty
    }
}
