//
//  CustomThemeNameViewModel.swift
//  AppSetting
//
//  Created by <PERSON><PERSON> on 2/13/67.
//
import APILayer
import Core
import RxCocoa
import RxSwift
import RxRelay
import XCoordinator
import SharedData
import Storage
import CUIModule
import DGCharts

public class CustomThemeNameViewModel: AnyViewModel {
    let router: UnownedRouter<AppSettingsRoute>

    public struct Input {
        let onViewAppear: Observable<Void>
        let onTextChanged: Observable<String>
    }

    public struct Output {
        let displayData: Driver<String?>
        let enableButton: Driver<Bool>
    }

    // Output to parent
    let onNamedChanged = PublishSubject<String>()
    let onTapNext = PublishSubject<Void>()

    private(set) var themeName: String?

    init(router: UnownedRouter<AppSettingsRoute>, themeName: String?) {
        self.router = router
        self.themeName = themeName
    }

    public func transform(input: Input) -> Output {

        let displayData = input.onViewAppear
            .map { self.themeName }

        let enableButton = input.onTextChanged
            .onNext {
                self.themeName = $0
            }
            .map {
                return !$0.trim().isEmpty
            }

        return Output(
            displayData: displayData.asDriverOnErrorNever(),
            enableButton: enableButton.asDriverOnErrorNever()
        )
    }
}
