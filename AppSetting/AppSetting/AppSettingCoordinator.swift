//
//  AppSettingCoordinator.swift
//  AppSetting
//
//  Created by <PERSON><PERSON><PERSON> on 11/6/66.
//
import Core
import CUIModule
import UIKit
import XCoordinator
import Storage
import RxSwift
import Authentication
import SharedData
import APILayer

public enum AppSettingsRoute: Route {
    case settings
    case logout
    case editProfile
    case didChangeEmail
    case notifications
    case apptheme
    case security
    case termsAndConditions
    case privacyPolicy
    case faqs
    case customThemeTutorial
    case customThemeList
    case customThemeCreate(data: CustomThemeModel?,
                           onCreated: PublishSubject<CustomThemeModel>,
                           onUpdated: PublishSubject<CustomThemeModel>)
    case setupPin(completion: ((_ pin: Bool, _ bio: Bool) -> Void))
    case loginPin(success: (() -> Void), failExceed: (() -> Void))
    case setupBio(completion: ((_ pin: Bool, _ bio: Bool) -> Void))
    case changePin(completion: ((_ pin: Bo<PERSON>, _ bio: Bool) -> Void))
    case changePassword(completion: (() -> Void))
    case didChangePassword
    case suspendAccount(state: SuspendAccountAction)
    case suspendAccountSuccess(state: SuspendAccountAction)
    case otpVerification(profileModel: ProfileInfoModel,
                         otpResponse: OTPRequestEndPoint.Response,
                         onDone: ((OTPAuthType) -> Void)?)
    case tradeAccount(TradeAccountRoute)
    case register(UserAccountType)
    case bankAccountDetail
    case addEditBankAccount(initialBankAccount: QueryUserBankListEndPoint.BankAccount?)
}

public enum AppSettingSubRoute {
    case tradeAccount(TradeAccountRoute, UINavigationController)
    case register(UserAccountType, UINavigationController)
}

public class AppSettingsCoordinator: NavigationCoordinator<AppSettingsRoute> {
    
    private let onLogout: () -> Void
    private var authCoordinator: AuthenticationCoordinator?
    private var navigateTo: ((_ route: AppSettingSubRoute) -> Void)?
    
    deinit {
        removeThemeChangedObserver()
    }
    
    public init(onLogout: @escaping () -> Void,
                subRoute: ((_ route: AppSettingSubRoute) -> Void)?) {
        self.onLogout = onLogout
        super.init(initialRoute: .settings)
        observerThemeChanged()
        self.navigateTo = subRoute
    }
    
    public override func prepareTransition(for route: AppSettingsRoute) -> NavigationTransition {
        switch route {
        case .settings:
            return .push(createSettingViewController())
            
        case .logout:
            // Clear session
            LocalPreference.clearCurrentSession(.sessionExpired)
            self.onLogout()
            
            return .none()
            
        case .apptheme:
            return .push(createAppThemeViewController())
            
        case .termsAndConditions:
            return .push(TermsViewController())
            
        case .privacyPolicy:
            let viewController = HtmlViewController(title: AppSetting.privacyPolicy.title,
                                                    fileName: AppSetting.privacyPolicy.htmlFileName)
            return .push(viewController)
            
        case .notifications:
            let viewModel = NotificationSettingViewModel(router: unownedRouter)
            let viewController = NotificationSettingViewController()
            viewController.bind(viewModel: viewModel)
            return .push(viewController)
            
        case .customThemeTutorial:
            let viewmodel = CustomThemeTutorialViewModel(router: self.unownedRouter)
            let viewController = CustomThemeTutorialViewController()
            viewController.bind(viewModel: viewmodel)
            return .push(viewController)
            
        case .customThemeList:
            var viewControllers = rootViewController.viewControllers.filter {
                $0.className != CustomThemeTutorialViewController.className
            }
            let viewmodel = CustomThemeListViewModel(router: self.unownedRouter)
            let viewController = CustomThemeListViewController()
            viewController.bind(viewModel: viewmodel)
            viewControllers.append(viewController)
            return .set(viewControllers)
            
        case .customThemeCreate(let data, let onCreated, let onUpdated):
            let viewmodel = CustomThemeCreateViewModel(
                router: self.unownedRouter, data: data,
                onThemeCreated: onCreated,
                onThemeUpdated: onUpdated
            )
            let viewController = CustomThemeCreateViewController()
            viewController.bind(viewModel: viewmodel)
            return .push(viewController)
            
        case .security:
            let viewModel = SecurityViewModel(router: unownedRouter)
            let viewController = SecurityViewController()
            viewController.bind(viewModel: viewModel)
            return .push(viewController)
            
        case .editProfile:
            let viewController = EditProfileViewController()
            let viewModel = EditProfileViewModel(router: unownedRouter)
            viewController.bind(viewModel: viewModel)
            
            return .push(viewController)
            
        case .faqs:
            let viewController = FAQViewController()
            let viewModel = FAQViewModel()
            viewController.bind(viewModel: viewModel)
            
            return .push(viewController)
            
        case .setupPin(let completion):
            authCoordinator = AuthenticationCoordinator(root: rootViewController)
            authCoordinator?.trigger(.pinSetup(completion: completion))
            return .none()
            
        case .loginPin(let success, let failExceed):
            authCoordinator = AuthenticationCoordinator(root: rootViewController)
            authCoordinator?.trigger(.pinLogin(
                doableBiometric: true, doableForgetPin: false,
                viewCreated: nil,
                success: success,
                failExceed: failExceed,
                pinUpdated: nil
            ))
            return .none()
            
        case .setupBio(let completion):
            let auth = authCoordinator ?? AuthenticationCoordinator(root: rootViewController)
            auth.trigger(.biomerticSetup(completion: completion))
            return .none()

        case .changePin(let completion):
            authCoordinator = AuthenticationCoordinator(root: rootViewController)
            authCoordinator?.trigger(.changePin(completion: completion))
            
            return .none()
            
        case .changePassword(let completion):
            let viewController = ChangePasswordViewController(completion: completion)
            let viewModel = ChangePasswordViewModel(router: unownedRouter)
            viewController.bind(viewModel: viewModel)
            
            return .push(viewController)
            
        case .didChangePassword:
            return .pop()
            
        case .suspendAccount(let state):
            let viewModel = SuspendAccountViewModel(router: unownedRouter, state: state)
            let viewController = SuspendAccountViewController()
            viewController.bind(viewModel: viewModel)
            viewController.modalTransitionStyle = .crossDissolve
            viewController.modalPresentationStyle = .overFullScreen
            return .present(viewController)
            
        case .suspendAccountSuccess(let state):
            let viewmodel = SuspendAccountSuccessfulViewModel(router: self.unownedRouter, state: state)
            let viewController = SuspendAccountSuccessfulViewController()
            viewController.bind(viewModel: viewmodel)
            return .push(viewController)
            
        case let .otpVerification(profileModel, otpResponse, onDone):
            let viewController = OTPVerificationViewController(sourceType: profileModel.otpType.sourceType,
                                                               onDone: onDone)
            let viewModel = OTPVerificationViewModel(profileModel: profileModel,
                                                     otpResponse: otpResponse)
            viewController.bind(viewModel: viewModel)
            
            return .push(viewController)
            
        case .tradeAccount(let route):
            navigateTo?(.tradeAccount(route, self.rootViewController))
            return .none()
            
        case .register(let accountType):
            navigateTo?(.register(accountType, self.rootViewController))
            return .none()
            
        case .didChangeEmail:
            let viewController = EmailUpdatedViewController(router: unownedRouter)
            
            return .push(viewController)
            
        case .bankAccountDetail:
            let viewController = BankAccountDetailViewController()
            let viewModel = BankAccountDetailViewModel(router: unownedRouter)
            viewController.bind(viewModel: viewModel)
            
            return .push(viewController)
            
        case .addEditBankAccount(let initialBankAccount):
            let viewController = AddEditBankAccountViewController()
            let viewModel = AddEditBankAccountViewModel(initialBankAccount: initialBankAccount)
            viewController.bind(viewModel: viewModel)
            
            return .push(viewController)
        }
    }
    
    private func createSettingViewController() -> AppSettingViewController {
        let viewmodel = AppSettingViewModel(router: self.unownedRouter)
        let viewController = AppSettingViewController()
        viewController.bind(viewModel: viewmodel)
        
        return viewController
    }
    
    private func createAppThemeViewController() -> AppThemeViewController {
        let viewmodel = AppThemeViewModel(router: self.unownedRouter)
        let viewController = AppThemeViewController()
        viewController.bind(viewModel: viewmodel)
        
        return viewController
    }
}
// MARK: - Custom Theme
extension AppSettingsCoordinator {
    
    private enum ResetOnThemeChanged {
        case appSetting(Int)
        case appTheme(Int)
    }
    
    @objc private func themeChanged() {
        
        var viewControllers = rootViewController.viewControllers
        
        viewsNeedReset().forEach {
            switch $0 {
            case .appSetting(let index):
                viewControllers[index] = createSettingViewController()
            case .appTheme(let index):
                viewControllers[index] = createAppThemeViewController()
            }
        }
        rootViewController.setViewControllers(viewControllers, animated: false)
    }
    
    private func viewsNeedReset() -> [ResetOnThemeChanged] {
        var toReset: [ResetOnThemeChanged] = []
        rootViewController.viewControllers.enumerated().forEach { index, value in
            switch value.className {
            case AppSettingViewController.className:
                toReset.append(.appSetting(index))
            case AppThemeViewController.className:
                toReset.append(.appTheme(index))
            default: break
            }
        }
        return toReset
    }
    
    func observerThemeChanged() {
        NotificationCenter.default.addObserver(
            self, selector: #selector(self.themeChanged),
            name: CustomThemeManager.observerName,
            object: nil
        )
    }
    
    func removeThemeChangedObserver() {
        NotificationCenter.default.removeObserver(
            self,
            name: CustomThemeManager.observerName,
            object: nil
        )
    }
}
