//
//  SettingItemCell.swift
//  AppSetting
//
//  Created by <PERSON> on 12/12/24.
//

import UIKit
import Core
import CUIModule

final class SettingItemCell: UITableViewCell, AnyView {
    
    // MARK: UI properties
    private lazy var contentStackView = {
        let stackView = UIStackView(alignment: .center,
                                    spacing: 6)
        stackView.addArrangedSubviews([iconView,
                                       titleLabel,
                                       UIView(),
                                       detailLabel,
                                       arrowIconView])
        
        return stackView
    }()
    
    private lazy var iconView = UIImageView()
    
    private lazy var titleLabel = UILabel(font: Font.medium.of(size: 14),
                                          textColor: Color.txtTitle)
    
    private lazy var detailLabel = UILabel(font: Font.medium.of(size: 10),
                                           textColor: Color.txtParagraph)
    
    private lazy var arrowIconView = UIImageView(image: .image(named: "merit_arrow_14"))
    
    // MARK: Life cycle
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func setupUI() {
        contentView.addSubview(contentStackView)
        
        contentStackView.snp.makeConstraints { make in
            make.top.equalTo(12)
            make.left.equalTo(4)
            make.bottom.equalTo(-12)
            make.right.equalTo(-4)
        }
    }
    
    func updateUI(with displayModel: any UIDisplayModel) {
        guard let appSetting = displayModel as? AppSetting else { return }
        
        iconView.image = appSetting.icon
        titleLabel.text = appSetting.title
        
        detailLabel.text = appSetting.detail
        detailLabel.isHidden = appSetting.detail?.isEmpty != false
        arrowIconView.isHidden = appSetting.detail?.isEmpty == false
    }
}

// MARK: - AppSetting
extension AppSetting: UIDisplayModel {}
