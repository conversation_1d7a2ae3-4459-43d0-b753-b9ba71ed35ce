//
//  AppSettingViewModel.swift
//  AppSetting
//
//  Created by <PERSON><PERSON> on 11/6/66.
//
import APILayer
import Core
import CUIModule
import RxCocoa
import RxSwift
import RxRelay
import XCoordinator

public class AppSettingViewModel: AnyViewModel {
    
    private let router: UnownedRouter<AppSettingsRoute>
    
    public struct Input {
        let onLogout: Observable<Void>
    }
    
    public struct Output {
        let loading: Driver<Bool>
        let error: Driver<Error>
        let didLogout: Observable<Void>
    }
    
    init(router: UnownedRouter<AppSettingsRoute>) {
        self.router = router
    }
    
    public func transform(input: Input) -> Output {
        let activityIndicator = ActivityIndicator()
        let errorTracker = ErrorTracker()
        
        let loggingOut = input.onLogout
            .flatMap {
                UserLogoutEndPoint.service.call()
                    .track(activityIndicator, error: errorTracker)
            }
        
        return Output(loading: activityIndicator.asDriver(),
                      error: errorTracker.asDriver(),
                      didLogout: loggingOut)
    }

    func navigate(to route: AppSettingsRoute) {
        router.trigger(route)
    }
}

// MARK: - Localization & Data
extension AppSettingViewModel {
    
    var settings: [AppSetting] {
        return AppSetting.allCases
    }
    
    var versionTitle: String {
        return "key0270".localized() + " \(Bundle.main.releaseVersionNumber ?? "").\(Bundle.main.buildVersionNumber ?? "")"
    }
}

// MARK: - App Setting Route
extension AppSetting {
    
    public var route: AppSettingsRoute? {
        switch self {
        case .editProfile:
            return .editProfile
        case .bankAccountDetails:
            return .bankAccountDetail
        case .security:
            return .security
        case .termsAndConditions:
            return .termsAndConditions
        case .privacyPolicy:
            return .privacyPolicy
            
        default:
            return nil
        }
    }
}
