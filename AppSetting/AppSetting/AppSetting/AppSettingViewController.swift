//
//  AppSettingViewController.swift
//  AppSetting
//
//  Created by <PERSON><PERSON> on 11/6/66.
//
import Core
import CUIModule
import APILayer
import RxSwift
import Storage
import SharedData

final class AppSettingViewController: BaseViewController, VMView {
    
    // MARK: UI properties
    /// Close Button
    private lazy var closeButton = UIButton(normalImage: .image(named: "merit_ic_cross"))
    
    /// Profile Item View
    private lazy var profileView = ProfileItemView()
    
    private lazy var divider = CUIDivider()
    
    private lazy var tableView = {
        let tableView = UITableView(isScrollEnabled: false,
                                    separatorStyle: .none)
        tableView.dataSource = self
        tableView.delegate = self
        
        tableView.register(SettingItemCell.self)
        
        return tableView
    }()
    /// Scroll view
    private lazy var scrollView = UIScrollView(showsVerticalScrollIndicator: false)
    
    /// Content View
    private lazy var contentView = UIView(backgroundColor: .clear)
    
    /// Content Stack View
    private lazy var contentStackView = UIStackView(axis: .vertical)
    
    /// Logout Label
    private lazy var logoutButton = ThemeableButton(type: .system,
                                                    titleFont: Font.medium.of(size: 14),
                                                    title: "key0262".localized(),
                                                    normalTitleColor: Color.txtTitle,
                                                    normalImage: .image(named: "merit_ic_logout"),
                                                    contentHorizontalAlignment: .leading,
                                                    contentEdgeInsets: UIEdgeInsets(left: 4, right: 4),
                                                    titleInsets: UIEdgeInsets(left: 4, right: -4))
    
    /// Version Label
    private lazy var versionLabel = UILabel(font: Font.light.of(size: 10),
                                            textColor: Color.txtInactive,
                                            textAlignment: .center)
    
    // MARK: Properties
    var viewModel: AppSettingViewModel!
    private let tapLogout = PublishSubject<Void>()
    
    // MARK: Life cycle
    init() {
        super.init(nibName: nil, bundle: nil)
        self.modalPresentationStyle = .fullScreen
    }
    
    required init?(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        setupUI()
        bindActions()
        bindViewModelLogic()
    }
  
    func setupUI() {
        view.backgroundColor = Color.bgDefault
        addSubviews([closeButton,
                     profileView,
                     divider,
                     tableView,
                     versionLabel,
                     logoutButton])
        // Close Button
        closeButton.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide.snp.top)
            make.left.equalTo(12)
            make.width.equalTo(24)
            make.height.equalTo(44)
        }
        // Profile View
        profileView.snp.makeConstraints { make in
            make.top.equalTo(closeButton.snp.bottom).offset(10)
            make.left.equalTo(12)
            make.right.equalTo(-12)
        }
        // Divider
        divider.snp.makeConstraints { make in
            make.top.equalTo(profileView.snp.bottom).offset(16)
            make.left.equalTo(12)
            make.right.equalTo(-12)
        }
        // Scroll View
        tableView.snp.makeConstraints { make in
            make.top.equalTo(divider.snp.bottom).offset(16)
            make.left.equalTo(12)
            make.right.equalTo(-12)
            make.bottom.equalTo(versionLabel.snp.top)
        }

        // Version Label
        versionLabel.snp.makeConstraints { make in
            make.left.equalTo(12)
            make.right.equalTo(-12)
            make.bottom.equalTo(logoutButton.snp.top).offset(-16)
        }
        // Logout Button
        logoutButton.snp.makeConstraints { make in
            make.left.equalTo(12)
            make.right.equalTo(-12)
            make.bottom.equalTo(view.safeAreaLayoutGuide).offset(-12)
            make.height.equalTo(44)
        }
        
        updateDisplay()
    }
    
    func bindActions() {
        disposeBag.insert([
            // Log out
            logoutButton.rx.tap.bind(onNext: { [unowned self] in
                let logoutBottomSheet = CUIBottomSheet(displayModel: .init(icon: .image(named: "merit_ic_logout"),
                                                                           title: "key0102".localized() + " " + "key0271".localized(),
                                                                           highlightTitles: ["key0271".localized()],
                                                                           desc: "key0272".localized(),
                                                                           buttons: [ThemeableButton(titleFont: Font.medium.of(size: 14),
                                                                                                     title: "key0273".localized(),
                                                                                                     normalTitleColor: Color.txtTitle,
                                                                                                     normalBgColor: Color.bgDefault,
                                                                                                     cornerRadius: 22,
                                                                                                     borderColor: Color.txtTitle,
                                                                                                     borderWidth: 0.5,
                                                                                                     tag: 0),
                                                                                     ThemeableButton(titleFont: Font.medium.of(size: 14),
                                                                                                     title: "key0262".localized(),
                                                                                                     normalTitleColor: Color.txtInverted,
                                                                                                     normalBgColor: Color.btn2nd,
                                                                                                     cornerRadius: 22,
                                                                                                     tag: 1)]))
                logoutBottomSheet.rx.tap
                    .subscribe(onNext: { [unowned self] index in
                        guard index == 1 else { return }
                        
                        tapLogout.onNext(())
                    }).disposed(by: disposeBag)
                
                logoutBottomSheet.present()
            }),
            // Close
            closeButton.rx.tap.subscribe(onNext: { [weak self] in
                self?.dismiss(animated: true)
            })
        ])
    }
    
    func bindViewModelLogic() {
        let input = AppSettingViewModel.Input(onLogout: tapLogout.asObservable())
        
        let output = viewModel.transform(input: input)
        
        output.loading.drive(onNext: { [weak self] in
            $0 ? self?.showLoading() : self?.hiddenLoading()
        }).disposed(by: disposeBag)
        
        output.error.drive(onNext: { [weak self] error in
            self?.showAlert(title: "key0895".localized(),
                            message: error.localizedDescription,
                            positiveTitle: "key0832".localized())
        }).disposed(by: disposeBag)
        
        output.didLogout
            .subscribe(onNext: { [unowned self] in
                viewModel.navigate(to: .logout)
            }).disposed(by: disposeBag)
    }
    
    override func setTexts() {
        logoutButton.setTitle("key0262".localized(), for: .normal)
        updateDisplay()
        tableView.reloadData()
    }
    
    private func updateDisplay() {
        versionLabel.text = viewModel.versionTitle
        
        let name: String
        switch LocalizeManager.currentLanguage {
        case .english:
            name = Keychain.userInformation?.basic?.englishName ?? ""
        case .hongKong:
            name = Keychain.userInformation?.basic?.chineseName ?? ""
        }
        profileView.updateUI(with: ProfileItemView.DisplayModel(name: name,
                                                                email: Keychain.userInformation?.basic?.base?.email ?? "",
                                                                image: "merit_ic_user"))
    }
}

// MARK: - UITableViewDataSource, UITableViewDelegate
extension AppSettingViewController: UITableViewDataSource, UITableViewDelegate {
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        AppSetting.allCases.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell: SettingItemCell = tableView.dequeueReusableCell(forIndexPath: indexPath)
        cell.updateUI(with: AppSetting.allCases[indexPath.row])
        
        return cell
    }
    
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
        
        let item = AppSetting.allCases[indexPath.row]
        switch item {
        case .language:
            let bottomSheet = ChangeLanguageBottomSheet()
            
            bottomSheet.present()
            
        default:
            if let route = item.route {
                viewModel.navigate(to: route)
            }
        }
    }
}
