//
//  SecurityItemView.swift
//  AppSetting
//
//  Created by <PERSON><PERSON><PERSON> on 4/3/2567 BE.
//

import UIKit
import Core
import CUIModule
import RxSwift

class SecurityItemView: UIView, AnyView {
    
    private lazy var stackView = {
        let stackView = UIStackView(alignment: .center,
                                    spacing: 6)
        stackView.addArrangedSubviews([iconView,
                                       titleLabel,
                                       UIView()])
        
        if item.switchable {
            stackView.addArrangedSubview(switchControl)
            switchControl.snp.makeConstraints { make in
                make.width.equalTo(42)
                make.height.equalTo(20)
            }
        } else {
            stackView.addArrangedSubview(arrowView)
        }
        
        return stackView
    }()
    
    private lazy var iconView = UIImageView(image: item.icon)
    
    private lazy var titleLabel = UILabel(font: Font.medium.of(size: 14),
                                          text: item.title,
                                          textColor: Color.txtTitle)
    
    private lazy var arrowView = UIImageView(image: .image(named: "icon-arrow"))
    
    fileprivate lazy var overlayButton = UIButton()
    fileprivate lazy var switchOverlayButton = UIButton()
    
    fileprivate lazy var switchControl = {
        let control = YapSwitch()
        control.shape = .square
        control.titleFont = Font.medium.of(size: 10)
        control.onText = "key0830".localized()
        control.offText = "key0831".localized()
        control.onTextColor = Color.txtInverted
        control.offTextColor = Color.txtInverted
        control.onTintColor = UIColor(hexString: "#000000")
        control.offTintColor = UIColor(hexString: "#9F9F9F")
        control.onThumbTintColor = Color.txtInverted
        control.offThumbTintColor = Color.txtInverted
        control.thumbCornerRadius = 2
        control.thumbRadiusPadding = 4
        control.setOn(initialEnabled, animated: false)
        
        return control
    }()
    
    var isOn: Bool {
        switchControl.isOn
    }
    
    // MARK: Properties
    private let item: SecurityItem
    private let initialEnabled: Bool
    fileprivate let isEnable = PublishSubject<Bool>()
    fileprivate let onTapped = PublishSubject<Bool>()
    fileprivate var allowPublish: Bool = true

    init(item: SecurityItem, initialEnabled: Bool = false) {
        self.item = item
        self.initialEnabled = initialEnabled
        
        super.init(frame: .zero)
        
        setupUI()
        bindActions()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func setupUI() {
        addSubview(stackView)
        stackView.snp.makeConstraints { make in
            make.top.equalTo(12)
            make.left.equalTo(4)
            make.bottom.equalTo(-12)
            make.right.equalTo(-4)
        }
        
        if item.switchable {
            addSubview(switchOverlayButton)
            switchOverlayButton.snp.makeConstraints { make in
                make.edges.equalTo(switchControl)
            }
        }
        
        if !item.switchable {
            addSubview(overlayButton)
            overlayButton.snp.makeConstraints { make in
                make.edges.equalToSuperview()
            }
        }
    }
    
    func setData(title: String) {
        titleLabel.text = title
    }
    
    func set(isOn: Bool) {
        allowPublish = false
        switchControl.setOn(isOn, animated: true)
        allowPublish = true
    }
    
    func set(userInteraction: Bool) {
        switchControl.isEnabled = userInteraction
    }
}

// MARK: - Reactive
extension Reactive where Base: SecurityItemView {
    
    var enable: Observable<Bool> {
        base.switchOverlayButton.rx.tap.asObservable().filter { _ in base.allowPublish }
            .map { base.isOn }
    }
    
    var tap: Observable<Void> { base.overlayButton.rx.tap.asObservable() }
}
