//
//  SecurityViewModel.swift
//  AppSetting
//
//  Created by <PERSON><PERSON><PERSON> on 4/3/2567 BE.
//

import APILayer
import Core
import CUIModule
import RxCocoa
import RxSwift
import RxRelay
import XCoordinator
import Storage
import LocalAuthentication
import Utils
import SharedData

public class SecurityViewModel: AnyViewModel {
    
    private let router: UnownedRouter<AppSettingsRoute>
    
    public struct Input {
        let onViewAppear: Observable<Void>
        let onTappedPin: Observable<Bool>
        let onTappedBio: Observable<Bool>
    }
    
    public struct Output {
        let loading: Driver<Bool>
        let error: Driver<Error>
        let pinSetup: Driver<Void>
        let pinLogin: Driver<Void>
        let bioSetUpWithPin: Driver<Void>
    }
    
    init(router: UnownedRouter<AppSettingsRoute>) {
        self.router = router
    }
    
    public func transform(input: Input) -> Output {
        let activityIndicator = ActivityIndicator()
        let errorTracker = ErrorTracker()
        
        let enablePinWithoutConfigured = input.onTappedPin
            .filter { $0 == false } // to turn on
            .mapToVoid()
            .filter { !self.pinHasSetUp } // only go to setup if pin NOT setup
        
        let enableBioWithoutPin = input.onTappedBio
            .filter { $0 == false } // to turn on
            .mapToVoid()
            .filter { !self.pinHasSetUp } // only go to setup if pin NOT setup
        
        let pinSetup = Observable.merge(enablePinWithoutConfigured,
                                        enableBioWithoutPin)
        
        let pinLogin = input.onTappedPin
            .mapToVoid()
            .filter { self.pinHasSetUp } // show pin login if pin HAS setup
        
        // Bio setup when Pin is configured
        let bioSetUpWithPin = input.onTappedBio
            .mapToVoid()
            .filter { self.pinHasSetUp }
            .filter { // check device support for biometric
                LAContext().biometricType == .faceID || LAContext().biometricType == .touchID
            }
        
        return Output(loading: activityIndicator.asDriver(),
                      error: errorTracker.asDriver(),
                      pinSetup: pinSetup.asDriverOnErrorNever(),
                      pinLogin: pinLogin.asDriverOnErrorNever(),
                      bioSetUpWithPin: bioSetUpWithPin.asDriverOnErrorNever())
    }
    
    func navigate(to route: AppSettingsRoute) {
        router.trigger(route)
    }
}

// MARK: - Business Logic
private extension SecurityViewModel {

    var pinHasSetUp: Bool {
        LocalPreference.configuredPin ?? false
    }
}

// MARK: - SecurityItem
enum SecurityItem {
    case enablePIN
    case enableBiometric
    case changePIN
    case changePassword
    case closeAccount
    
    var icon: UIImage? {
        switch self {
        case .enablePIN,
                .changePIN:
            return .image(named: "ic_pin")
        case .enableBiometric:
            return .image(named: "ic_biometric")
        case .changePassword:
            return .image(named: "ic_key")
        case .closeAccount:
            return .image(named: "ic_close")
        }
    }
    
    var title: String {
        switch self {
        case .enablePIN:
            return "key0765".localized()
        case .enableBiometric:
            return "key0766".localized()
        case .changePIN:
            return "key0767".localized()
        case .changePassword:
            return "key0768".localized()
        case .closeAccount:
            return "key0771".localized()
        }
    }
    
    var switchable: Bool {
        switch self {
        case .enablePIN,
                .enableBiometric:
            return true
            
        default:
            return false
        }
    }
}
