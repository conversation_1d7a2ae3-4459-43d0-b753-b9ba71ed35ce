//
//  SecurityViewController.swift
//  AppSetting
//
//  Created by <PERSON><PERSON> on 4/3/2567 BE.
//

import Core
import CUIModule
import APILayer
import RxSwift
import Storage
import SharedData

final class SecurityViewController: BaseViewController, VMView {

    // MARK: UI properties
    private lazy var headerView = {
        let displayModel = CUINavigationBar.DisplayModel(leftIcon: .image(named: "merit_ic_back"),
                                                         titleView: titleStackView)
        let headerView = CUINavigationBar(displayModel: displayModel)
        
        return headerView
    }()
    
    private lazy var titleStackView = {
        let stackView = UIStackView(alignment: .center,
                                    spacing: 4)
        stackView.addArrangedSubviews([iconView,
                                       titleLabel])
        
        return stackView
    }()
    
    private lazy var iconView = UIImageView(image: AppSetting.security.icon)
    
    private lazy var titleLabel = UILabel(font: Font.semiBold.of(size: 14),
                                          text: AppSetting.security.title,
                                          textColor: Color.txtTitle)
    
    private lazy var stackView = {
        let stackView = UIStackView(axis: .vertical,
                                    spacing: 8)
        stackView.addArrangedSubviews([loginMethodTitleLabel,
                                       enablePinView,
                                       enableBiometricView,
                                       CUIDivider(canvasHeight: 8),
                                       changeCredentialTitleLabel,
                                       changePinView,
                                       changePasswordView])
        
        return stackView
    }()
    
    private lazy var loginMethodTitleLabel = UILabel(backgroundColor: .clear,
                                                     font: Font.semiBold.of(size: 14),
                                                     text: "key0764".localized(),
                                                     textColor: Color.txtTitle)
    
    private lazy var enablePinView = SecurityItemView(item: .enablePIN,
                                                      initialEnabled: pinEnable)
    
    private lazy var enableBiometricView = SecurityItemView(item: .enableBiometric,
                                                            initialEnabled: bioEnable)
    
    private lazy var changeCredentialTitleLabel = UILabel(font: Font.semiBold.of(size: 14),
                                                          text: "key0770".localized(),
                                                          textColor: Color.txtTitle)
    
    private lazy var changePinView = SecurityItemView(item: .changePIN)
    
    private lazy var changePasswordView = SecurityItemView(item: .changePassword)

    private lazy var closeAccountView = SecurityItemView(item: .closeAccount)
    
    // MARK: Properties
    var viewModel: SecurityViewModel!

    private var pinEnable: Bool {
        LocalPreference.loginPinEnable ?? false
    }

    private var bioEnable: Bool {
        (LocalPreference.loginPinEnable ?? false) && (LocalPreference.biometricEnable ?? false)
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        
        setupUI()
        bindActions()
        bindViewModelLogic()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        
        enablePinView.set(isOn: pinEnable)
        enableBiometricView.set(isOn: bioEnable)
    }
    
    func setupUI() {
        view.addSubviews([headerView,
                          stackView,
                          closeAccountView])
        // Header View
        headerView.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide)
            make.left.right.equalToSuperview()
        }
        
        // Stack View
        stackView.snp.makeConstraints { make in
            make.left.equalTo(12)
            make.right.equalTo(-12)
            make.top.equalTo(headerView.snp.bottom).offset(16)
        }
        
        // Delete Account Button
        closeAccountView.snp.makeConstraints { make in
            make.bottom.equalTo(view.safeAreaLayoutGuide)
            make.left.equalTo(12)
            make.right.equalTo(-12)
        }
    }
    
    func bindActions() {
        headerView.rx.leftTap.subscribe(onNext: { [weak self] in
            self?.backPress()
        }).disposed(by: disposeBag)
        
        closeAccountView.rx.tap.subscribe(onNext: { [weak self] in
            self?.viewModel.navigate(to: .suspendAccount(state: .closeAccount))
        }).disposed(by: disposeBag)
        
        changePinView.rx.tap.subscribe(onNext: { [weak self] in
            self?.viewModel.navigate(to: .changePin(completion: { _, _ in
                self?.updateSetting(type: .pinUpdated)
            }))
        }).disposed(by: disposeBag)
        
        changePasswordView.rx.tap.subscribe(onNext: { [weak self] in
            self?.viewModel.navigate(to: .changePassword(completion: { [unowned self] in
                self?.updateSetting(type: .passwordChanged)
            }))
        }).disposed(by: disposeBag)
    }
    
    func bindViewModelLogic() {
        let input = SecurityViewModel.Input(onViewAppear: rx.viewWillAppear.take(1).mapToVoid(),
                                            onTappedPin: enablePinView.rx.enable,
                                            onTappedBio: enableBiometricView.rx.enable)
        
        let output = viewModel.transform(input: input)

        output.loading.drive(onNext: { [weak self] loading in
            loading ? self?.showLoading() : self?.hiddenLoading()
        }).disposed(by: disposeBag)

        output.error.drive(onNext: { [weak self] error in
            self?.showAlert(title: "key0895".localized(), message: error.localizedDescription)
        }).disposed(by: disposeBag)

        output.pinSetup.drive(onNext: { [weak self] in
            guard let self = self else { return }
            self.viewModel.navigate(to: .setupPin(completion: { pin, bio in
                self.updateSetting(type: .pin(pinOn: pin, bioOn: bio, showToast: false))
            }))
        }).disposed(by: disposeBag)

        output.pinLogin.drive(onNext: { [weak self] in
            guard let self = self else { return }
            self.viewModel.navigate(to: .loginPin(
                success: {
                    self.updateSetting(type: .pin(pinOn: !self.pinEnable, bioOn: nil, showToast: true))
                },
                failExceed: {
                    self.updateSetting(type: nil)
                }
            ))
        }).disposed(by: disposeBag)
        
        output.bioSetUpWithPin.drive(onNext: { [weak self] in
            guard let self = self else { return }
            self.viewModel.navigate(to: .loginPin(
                success: {
                    // to off
                    if self.bioEnable {
                        self.updateSetting(type: .biometric(false))
                        return
                    }
                    // to On
                    
                    self.viewModel.navigate(to: .setupBio(completion: { _, _ in
                        if !self.pinEnable {
                            self.updateSetting(type: .pin(pinOn: true, bioOn: true, showToast: true))
                        } else {
                            self.updateSetting(type: .biometric(true))
                        }
                    }))
                },
                failExceed: {
                    self.updateSetting(type: nil)
                }
            ))
        }).disposed(by: disposeBag)
    }
}

// MARK: - Update UI
extension SecurityViewController {
    
    private func updateSetting(type: SettingType?) {
        navigationController?.popToViewController(self, animated: true)
        
        guard let setting = type else {
            showToast(title: "key0937".localized(),
                      desc: "key0938".localized(),
                      titleColor: Color.txtNegative)
            return
        }
        
        switch setting {
        case let .pin(pinOn, bioOn, showToast):
            LocalPreference.loginPinEnable = pinOn
            enablePinView.set(isOn: pinOn)
            
            if let bioOn = bioOn {
                LocalPreference.biometricEnable = bioOn
                enableBiometricView.set(isOn: bioOn)
            } else if !pinOn {
                LocalPreference.biometricEnable = false
                enableBiometricView.set(isOn: false)
            }
            if showToast {
                if let bioOn = bioOn {
                    self.showToast(title: bioOn ? "key0934".localized() : "key0935".localized(),
                                   desc: bioOn ? "key0936".localized() : "key0774".localized(),
                                   titleColor: Color.txtLabel)
                } else {
                    self.showToast(title: "\(pinOn ? "key0773".localized() : "key0775".localized())",
                                   desc: "\(pinOn ? "key0774".localized() : "key0933".localized())",
                                   titleColor: Color.txtLabel)
                }
            }
            
        case .biometric(let isOn):
            LocalPreference.biometricEnable = isOn
            enableBiometricView.set(isOn: isOn)
            
            self.showToast(title: isOn ? "key0934".localized() : "key0935".localized(),
                           desc: isOn ? "key0936".localized() : "key0774".localized(),
                           titleColor: Color.txtLabel)
            
        case .pinUpdated:
            self.showToast(title: "key0779".localized(),
                           desc: "key0780".localized(),
                           titleColor: Color.txtLabel)
            
        case .passwordChanged:
            self.showToast(title: "key0781".localized(),
                           desc: "key0782".localized(),
                           titleColor: Color.txtLabel)
        }
    }
    
    private func showToast(title: String, desc: String,titleColor: UIColor) {
        CUIToastView.show(data: .init(title: title,
                                      desc: desc,
                                      titleStyle: .init(textColor: titleColor)),
                          inView: view,
                          underView: headerView)
    }
}

extension SecurityViewController {
    
    enum SettingType {
        case pin(pinOn: Bool, bioOn: Bool?, showToast: Bool)
        case biometric(Bool)
        case pinUpdated
        case passwordChanged
    }
}
