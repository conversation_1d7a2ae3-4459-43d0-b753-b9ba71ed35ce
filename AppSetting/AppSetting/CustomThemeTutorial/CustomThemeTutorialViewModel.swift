//
//  CustomThemeTutorialViewModel.swift
//  AppSetting
//
//  Created by <PERSON><PERSON> on 2/12/67.
//
import APILayer
import Core
import RxCocoa
import RxSwift
import RxRelay
import XCoordinator
import SharedData
import Storage
import CUIModule
import DGCharts

public class CustomThemeTutorialViewModel: AnyViewModel {
    let router: UnownedRouter<AppSettingsRoute>

    public struct Input {
        let onViewAppear: Observable<Void>
    }

    public struct Output {
        let displayData: Driver<Void>
    }

    init(router: UnownedRouter<AppSettingsRoute>) {
        self.router = router
    }

    public func transform(input: Input) -> Output {

        let displayData = input.onViewAppear

        return Output(
            displayData: displayData.asDriverOnErrorNever()
        )
    }

    func navigate(to route: AppSettingsRoute) {
        router.trigger(route)
    }
}
