//
//  CustomThemeTutorialViewController.swift
//  AppSetting
//
//  Created by <PERSON><PERSON> on 2/12/67.
//
import Core
import CUIModule
import RxSwift
import RxCocoa
import Utils
import SharedData

final class CustomThemeTutorialViewController: BaseViewController, VMView {

    private lazy var imageView = UIImageView(
        contentMode: .scaleAspectFit,
        image: .image(named: "imgthemetutorial")
    )

    private lazy var titleLabel = UILabel(
        font: Font.bold.of(size: 20),
        numberOfLines: 0,
        text: "CREATING A CUSTOM THEME",
        textColor: Color.txtTitle
    )

    private lazy var descLabel = UILabel(
        font: Font.medium.of(size: 14),
        textColor: Color.txtParagraph,
        lines: 0
    )

    private lazy var tryItOutButton = ThemeableButton(
        titleFont: Font.bold.of(size: 16),
        title: "LET’S TRY IT OUT!",
        normalTitleColor: Color.pitchBlack,
        normalBgColor: Color.bgButtonDefault,
        cornerRadius: 24,
        themeType: .primary
    )

    var viewModel: CustomThemeTutorialViewModel!

    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        bindActions()
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        navigationBarHidden(true)
    }

    func setupUI() {
        view.backgroundColor = Color.bgDefault

        addSubviews([imageView, titleLabel, descLabel, tryItOutButton])

        let screenHeight = UIScreen.main.bounds.height
        let defaultPadding: CGFloat = 24

        imageView.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide.snp.top).offset(screenHeight*0.088)
            make.left.right.equalToSuperview()
            make.height.equalTo(screenHeight*0.24)
        }

        titleLabel.snp.makeConstraints { make in
            make.top.equalTo(imageView.snp.bottom).offset(defaultPadding*2)
            make.left.equalTo(defaultPadding)
            make.right.equalTo(-defaultPadding)
        }

        descLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(defaultPadding)
            make.left.equalTo(defaultPadding)
            make.right.equalTo(-defaultPadding)
        }

        tryItOutButton.snp.makeConstraints { make in
            make.left.equalTo(defaultPadding)
            make.right.equalTo(-defaultPadding)
            make.bottom.equalTo(view.safeAreaLayoutGuide.snp.bottom).offset(-(defaultPadding*2))
            make.height.equalTo(48)
        }
    }

    func bindActions() {
        tryItOutButton.rx.tap.subscribe(onNext: { [weak self] in
            self?.viewModel.navigate(to: .customThemeList)
        }).disposed(by: disposeBag)
    }

    func bind(viewModel: CustomThemeTutorialViewModel) {
        self.viewModel = viewModel

        let input = CustomThemeTutorialViewModel.Input(
            onViewAppear: rx.viewWillAppear.mapToVoid()
        )

        let output = viewModel.transform(input: input)

        output.displayData.drive(onNext: { [weak self] in
            self?.displayUI()
        }).disposed(by: disposeBag)
    }
}
// MARK: - Update UI
extension CustomThemeTutorialViewController {

    private func displayUI() {

        // swiftlint: disable line_length
        descLabel.highlight(
            "You can choose colors for certain elements within this app to customize the look and feel of the app in your own way!\n\nYour choices of the color will be automatically adjusted when switching between dark and light mode.\n\nIf you select Smart Colors, you only need to pick one color and the rest will be adjusted automatically for you. Smart Colors is available on Custom tab.",
            normalColor: Color.txtParagraph,
            normalFont: Font.medium.of(size: 14),
            highlightText: ["Smart Colors", "Custom"],
            highlightColor: Color.txtLabel,
            highlightFont: Font.medium.of(size: 14)
        )
        // swiftlint: enable line_length
    }
}
