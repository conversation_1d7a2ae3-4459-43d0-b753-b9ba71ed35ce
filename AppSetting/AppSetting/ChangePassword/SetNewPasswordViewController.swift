//
//  SetNewPasswordViewController.swift
//  Login
//
//  Created by <PERSON> on 06/03/2024.
//

import UIKit
import Core
import CUIModule
import Authentication
import RxSwift

final class SetNewPasswordViewController: UIViewController, AnyView {
    
    // MARK: UI properties
    private lazy var passwordInputView = CreatePasswordInputView(title: "key0254".localized())
    
    fileprivate lazy var confirmButton = UIButton(type: .system,
                                                  backgroundColor: Color.btn2nd,
                                                  titleFont: Font.medium.of(size: 14),
                                                  title: "key0260".localized(),
                                                  normalTitleColor: Color.txtInverted,
                                                  disabledTitleColor: Color.txtDisabled,
                                                  cornerRadius: 22)

    // MARK: Properties
    private var verifyButtons: [UIButton] = []
    private let disposeBag = DisposeBag()
    fileprivate let onConfirm = PublishSubject<String>()
    
    // MARK: Life cycle
    override func viewDidLoad() {
        super.viewDidLoad()
        
        setupUI()
        bindActions()
    }
    
    func setupUI() {
        view.backgroundColor = Color.bgDefault
        view.addSubviews([passwordInputView,
                          confirmButton])
        
        passwordInputView.snp.makeConstraints { make in
            make.top.equalTo(16)
            make.left.equalTo(12)
            make.right.equalTo(-12)
        }
                
        confirmButton.snp.makeConstraints { make in
            make.left.equalTo(12)
            make.right.equalTo(-12)
            make.bottom.equalTo(view.safeAreaLayoutGuide).offset(-16)
            make.height.equalTo(44)
        }
    }
    
    func bindActions() {
        passwordInputView.rx.verifiedPassword
            .subscribe(onNext: { [unowned self] password in
                confirmButton.isEnabled = password?.isEmpty == false
                confirmButton.backgroundColor = password?.isEmpty == false ? Color.btn2nd : Color.btnDisabled
            })
            .disposed(by: disposeBag)
        
        confirmButton.rx.tap
            .withLatestFrom(passwordInputView.rx.verifiedPassword.compactMap { $0 })
            .subscribe(onNext: { [unowned self] password in
                onConfirm.onNext(password)
            })
            .disposed(by: disposeBag)
    }
    
    func reset() {
        passwordInputView.reset()
    }
}

// MARK: - JXSegmentedListContainerViewListDelegate
extension SetNewPasswordViewController: JXSegmentedListContainerViewListDelegate {
    func listView() -> UIView { view }
}

// MARK: - extension Reactive
extension Reactive where Base: SetNewPasswordViewController {
    
    var confirmPassword: Observable<String> {
        base.onConfirm.asObservable()
    }
}
