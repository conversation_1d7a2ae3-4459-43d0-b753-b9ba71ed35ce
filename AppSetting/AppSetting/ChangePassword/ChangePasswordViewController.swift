//
//  ChangePasswordViewController.swift
//  AppSetting
//
//  Created by <PERSON> on 06/03/2024.
//

import UIKit
import Core
import CUIModule
import Authentication
import Storage
import RxSwift
import APILayer

final class ChangePasswordViewController: BaseStepIndicatorContainer, VMView {
    
    // MARK: UI properties
    private lazy var headerView = {
        let displayModel = CUINavigationBar.DisplayModel(leftIcon: .image(named: "merit_ic_back"),
                                                         titleView: titleStackView)
        return CUINavigationBar(displayModel: displayModel)
    }()
    
    private lazy var titleStackView = {
        let stackView = UIStackView(alignment: .center,
                                    spacing: 4)
        stackView.addArrangedSubviews([titleImgView,
                                       titleLabel])
        
        return stackView
    }()
    
    private lazy var titleImgView = UIImageView(image: SecurityItem.changePassword.icon)
    
    private lazy var titleLabel = UILabel(font: Font.semiBold.of(size: 14),
                                          text: SecurityItem.changePassword.title,
                                          textColor: Color.txtTitle)
    
    // MARK: Properties
    var viewModel: ChangePasswordViewModel!
    private let onOtpOption = PublishSubject<(type: String,
                                              address: String,
                                              mobileRegion: String?)>()
    private let onInputOtp = PublishSubject<String>()
    
    private lazy var setPasswordViewController = SetNewPasswordViewController()
    
    private lazy var otpOptionViewcontroller = {
        let mobileRegion = Keychain.userInformation?.basic?.base?.mobileRegion ?? ""
        let phoneNumber = Keychain.userInformation?.basic?.base?.phoneNumber ?? ""
        let email = Keychain.userInformation?.basic?.base?.email ?? ""
        
        return OTPOptionSelectViewController(mobileRegion: mobileRegion,
                                             phoneNumber: phoneNumber,
                                             emailAddress: email)
    }()
    
    private lazy var otpViewController = OTPInputViewController(sourceType: .email,
                                                                parentView: view,
                                                                navigationView: headerView)
    private let completion: (() -> Void)

    // MARK: Life cycle
    init(completion: @escaping () -> Void) {
        self.completion = completion
        super.init(nibName: nil, bundle: nil)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        setupSegmentedDataSource()
        setupUI()
        bindActions()
        bindViewModelLogic()
        moveToStep(ChangePasswordStep.otpOption)
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)

        navigationController?.interactivePopGestureRecognizer?.isEnabled = false
    }
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        
        navigationController?.interactivePopGestureRecognizer?.isEnabled = true
    }
    
    override func allSteps() -> [StepIndicatorStepType] {
        ChangePasswordStep.allCases
    }
    
    override func subViewController(at step: StepIndicatorStepType) -> JXSegmentedListContainerViewListDelegate {
        guard let step = step as? ChangePasswordStep else { fatalError() }
        
        switch step {
        case .setPassword:
            return setPasswordViewController
        case .otpOption:
            return otpOptionViewcontroller
        case .otpInput:
            return otpViewController
        }
    }
    
    func setupUI() {
        view.backgroundColor = Color.bgDefault
        
        view.addSubviews([headerView,
                          stepIndicator,
                          segmentedView,
                          segmentedContainerView])
        
        headerView.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide)
            make.left.right.equalToSuperview()
        }
        
        stepIndicator.snp.makeConstraints { make in
            make.top.equalTo(headerView.snp.bottom).offset(12)
            make.left.equalTo(12)
            make.right.equalTo(-12)
        }
        
        segmentedView.snp.makeConstraints { make in
            make.top.equalTo(stepIndicator.snp.bottom)
            make.left.equalTo(12)
            make.right.equalTo(-12)
            make.height.equalTo(0)
        }
        
        segmentedContainerView.snp.makeConstraints { make in
            make.top.equalTo(stepIndicator.snp.bottom)
            make.left.right.bottom.equalToSuperview()
        }
    }
    
    func bindActions() {
        // Back
        headerView.rx.leftTap
            .subscribe(onNext: { [unowned self] in
                guard let currentStep = currentStep as? ChangePasswordStep else {
                    backPress()
                    return
                }
                
                if currentStep == .otpOption {
                    backPress()
                } else {
                    if currentStep == .setPassword {
                        otpViewController.reset()
                        otpViewController.sendOTP()
                    }
                    previousStep()
                    setPasswordViewController.reset()
                }
            })
            .disposed(by: disposeBag)
        
        // OTP option
        otpOptionViewcontroller.rx.sendOTP
            .subscribe(onNext: { [unowned self] option in
                otpOptionViewcontroller.setInteraction(enable: false)
                switch option {
                case .phoneNumber(let mobileRegion, let phoneNumber):
                    otpViewController.updateSourceType(sourceType: .phone)
                    onOtpOption.onNext((type: option.queryTypeValue,
                                        address: phoneNumber,
                                        mobileRegion: mobileRegion))
                    
                case .email(let email):
                    otpViewController.updateSourceType(sourceType: .email)
                    onOtpOption.onNext((type: option.queryTypeValue,
                                        address: email,
                                        mobileRegion: nil))
                }
            })
            .disposed(by: disposeBag)
        
        // OTP
        otpViewController.rx.submitOtp
            .subscribe(onNext: { [unowned self] otp in
                otpViewController.resignFirstResponder()
                onInputOtp.onNext(otp)
            })
            .disposed(by: disposeBag)
    }
    
    func bindViewModelLogic() {
        let input = ChangePasswordViewModel.Input(otpOption: onOtpOption.asObservable(),
                                                  resendOTP: otpViewController.rx.resendOTP,
                                                  inputOtp: onInputOtp.asObservable(),
                                                  changePassword: setPasswordViewController.rx.confirmPassword)
        
        let output = viewModel.transform(input: input)
        
        output.onShowLoading
            .drive(onNext: { [unowned self] show in
                show ? showLoading() : hiddenLoading()
            })
            .disposed(by: disposeBag)
        
        output.onError
            .drive(onNext: { [unowned self] error in
                var errorMessage = error.localizedDescription
                if let apiError = error as? APIError {
                    errorMessage = apiError.message
                }
                CUIToastView.show(data: .init(title: "key0895".localized(),
                                              desc: errorMessage,
                                              titleStyle: .init(textColor: Color.txtNegative)),
                                  inView: view,
                                  underView: headerView)
                
                otpOptionViewcontroller.setInteraction(enable: true)
            })
            .disposed(by: disposeBag)
        
        output.otpRequested
            .drive(onNext: { [unowned self] address, mobileRegion, response, isResend in
                CUIToastView.show(data: .init(title: "key0111".localized(),
                                              desc: otpViewController.currentSourceType().toastDesc,
                                              titleStyle: .init(textColor: Color.txtTitle)),
                                  inView: view,
                                  underView: headerView)
                otpViewController.otpRequested.onNext((address: address,
                                                       mobileRegion: mobileRegion,
                                                       response: response))
                
                if !isResend {
                    nextStep()
                    otpOptionViewcontroller.setInteraction(enable: true)
                }
            }).disposed(by: disposeBag)
        
        output.otpSubmitted
            .drive(onNext: { [unowned self] in
                nextStep()
            }).disposed(by: disposeBag)
        
        output.doneUpdatePassword
            .drive(onNext: { [unowned self] in
                viewModel.navigate(to: .didChangePassword)
                completion()
            })
            .disposed(by: disposeBag)
    }
}

// MARK: - Element
extension ChangePasswordViewController {
    
    enum ChangePasswordStep: Int, CaseIterable, StepIndicatorStepType {
        case otpOption
        case otpInput
        case setPassword
        
        var id: Int { rawValue }
        var title: String { "\(id)" }
    }
}
