//
//  ChangePasswordViewModel.swift
//  AppSetting
//
//  Created by <PERSON> on 06/03/2024.
//

import Core
import APILayer
import RxSwift
import RxCocoa
import Authentication
import Storage
import XCoordinator
import SharedData

final class ChangePasswordViewModel: AnyViewModel {
    
    struct Input {
        let otpOption: Observable<(type: String, address: String, mobileRegion: String?)>
        let resendOTP: Observable<Void>
        let inputOtp: Observable<String>
        let changePassword: Observable<String>
    }
    
    struct Output {
        let onError: Driver<Error>
        let onShowLoading: Driver<Bool>
        let otpRequested: Driver<(address: String,
                                  mobileRegion: String?,
                                  response: OTPRequestEndPoint.Response,
                                  isResend: Bool)>
        let otpSubmitted: Driver<Void>
        let doneUpdatePassword: Driver<Void>
    }
    
    // MARK: Properties
    private let router: UnownedRouter<AppSettingsRoute>
    
    private var otpType: String = ""
    private var otpAddress: String = ""
    private var mobileRegion: String?
    private var refCode = ""
    
    init(router: UnownedRouter<AppSettingsRoute>) {
        self.router = router
    }
    
    func transform(input: Input) -> Output {
        let activityIndicator = ActivityIndicator()
        let errorTracker = ErrorTracker()
        
        let otpRequest = input.otpOption
            .onNext { [unowned self] in
                otpType = $0.type
                otpAddress = $0.address
                mobileRegion = $0.mobileRegion
            }
            .map {
                (type: $0.type,
                 address: $0.address,
                 mobileRegion: $0.mobileRegion,
                 isResend: false)
            }
        
        let otpResend = input.resendOTP
            .map { [unowned self] in
                (type: otpType,
                 address: otpAddress,
                 mobileRegion: mobileRegion,
                 isResend: true)
            }
        
        let sendOTPRequest = Observable.merge(otpRequest, otpResend)
            .flatMap { [unowned self] data in
                requestOTP(for: data.type,
                           address: data.address,
                           mobileRegion: data.mobileRegion)
                    .track(activityIndicator, error: errorTracker)
                    .map {
                        (address: data.address,
                         mobileRegion: data.mobileRegion,
                         response: $0,
                         isResend: data.isResend)
                    }
            }
            .onNext { [unowned self] in
                refCode = $0.response.refCode ?? ""
            }
        
        let submitOtp = input.inputOtp
            .flatMap { [unowned self] otp in
                submitOTP(otp, refCode: refCode, type: otpType)
                    .track(activityIndicator, error: errorTracker)
            }
            .map { $0.token ?? "" }
            .share()
        
        let onChangePassword = input.changePassword
            .withLatestFrom(submitOtp) { ($0, $1) }
            .flatMap { [unowned self] (password, token) in
                resetPassword(password, token: token)
                    .track(activityIndicator, error: errorTracker)
            }
        
        return Output(onError: errorTracker.asDriver(),
                      onShowLoading: activityIndicator.asDriver(),
                      otpRequested: sendOTPRequest.asDriverOnErrorNever(), 
                      otpSubmitted: submitOtp.mapToVoid().asDriverOnErrorNever(),
                      doneUpdatePassword: onChangePassword.asDriverOnErrorNever())
    }
    
    func navigate(to route: AppSettingsRoute) {
        router.trigger(route)
    }
}

// MARK: - API
private extension ChangePasswordViewModel {
    
    func requestOTP(for type: String, address: String, mobileRegion: String?) -> Observable<OTPRequestEndPoint.Response> {
        OTPRequestEndPoint.service.call(parameter: .init(bizType: BizType.forgotPasswordPin.rawValue,
                                                         otpType: type,
                                                         otpAddress: address,
                                                         mobileRegion: mobileRegion))
    }
    
    func submitOTP(_ otpCode: String,
                   refCode: String,
                   type: String) -> Observable<UserAuthResetVerificationEndPoint.Response> {
        UserAuthResetVerificationEndPoint.service.call(parameter: .init(username: Keychain.userName ?? "",
                                                                        type: "FORGET_PASSWORD",
                                                                        otpType: type,
                                                                        otp: otpCode,
                                                                        refCode: refCode))
    }
    
    func resetPassword(_ password: String, token: String) -> Observable<Void> {
        UserAuthResetEndPoint.service.call(parameter: .init(token: token,
                                                            deviceId: LocalPreference.deviceId ?? "",
                                                            password: password))
        .mapToVoid()
    }
}
