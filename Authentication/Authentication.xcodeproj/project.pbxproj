// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 56;
	objects = {

/* Begin PBXBuildFile section */
		2A326C79E61CC9A798C8B3C2 /* Pods_Authentication.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 0D47456FFAD0BBDB2C958821 /* Pods_Authentication.framework */; };
		DF0AD9682BD769820098FDAF /* QRView.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD92A2BD769820098FDAF /* QRView.swift */; };
		DF0AD96A2BD769820098FDAF /* AuthNavigationView.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD92C2BD769820098FDAF /* AuthNavigationView.swift */; };
		DF0AD96B2BD769820098FDAF /* CodeView.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD92D2BD769820098FDAF /* CodeView.swift */; };
		DF0AD96C2BD769820098FDAF /* BiometricSettingViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD92F2BD769820098FDAF /* BiometricSettingViewController.swift */; };
		DF0AD96D2BD769820098FDAF /* BiometricSettingViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD9302BD769820098FDAF /* BiometricSettingViewModel.swift */; };
		DF0AD96E2BD769820098FDAF /* AuthenticationCoordinator.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD9312BD769820098FDAF /* AuthenticationCoordinator.swift */; };
		DF0AD9712BD769820098FDAF /* OTPInputViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD9362BD769820098FDAF /* OTPInputViewModel.swift */; };
		DF0AD9722BD769820098FDAF /* OTPOptionSelectViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD9372BD769820098FDAF /* OTPOptionSelectViewController.swift */; };
		DF0AD9732BD769820098FDAF /* OTPOptionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD9392BD769820098FDAF /* OTPOptionView.swift */; };
		DF0AD9742BD769820098FDAF /* OTPEmailInputViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD93A2BD769820098FDAF /* OTPEmailInputViewController.swift */; };
		DF0AD9752BD769820098FDAF /* OTPInputViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD93B2BD769820098FDAF /* OTPInputViewController.swift */; };
		DF0AD9762BD769820098FDAF /* PinSetupCompleteViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD93D2BD769820098FDAF /* PinSetupCompleteViewModel.swift */; };
		DF0AD9772BD769820098FDAF /* PinSetupCompleteViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD93E2BD769820098FDAF /* PinSetupCompleteViewController.swift */; };
		DF0AD9782BD769820098FDAF /* ForgotPinViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD9402BD769820098FDAF /* ForgotPinViewController.swift */; };
		DF0AD9792BD769820098FDAF /* ForgotPinViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD9412BD769820098FDAF /* ForgotPinViewModel.swift */; };
		DF0AD97A2BD769820098FDAF /* ForgotPinEmailViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD9432BD769820098FDAF /* ForgotPinEmailViewModel.swift */; };
		DF0AD97B2BD769820098FDAF /* ForgotPinEmailViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD9442BD769820098FDAF /* ForgotPinEmailViewController.swift */; };
		DF0AD97C2BD769820098FDAF /* TwoFAEnabledViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD9462BD769820098FDAF /* TwoFAEnabledViewController.swift */; };
		DF0AD97D2BD769820098FDAF /* TwoFAEnabledViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD9472BD769820098FDAF /* TwoFAEnabledViewModel.swift */; };
		DF0AD97E2BD769820098FDAF /* TwoFALandingViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD9492BD769820098FDAF /* TwoFALandingViewModel.swift */; };
		DF0AD97F2BD769820098FDAF /* TwoFALandingViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD94A2BD769820098FDAF /* TwoFALandingViewController.swift */; };
		DF0AD9802BD769820098FDAF /* OTPSMSViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD94C2BD769820098FDAF /* OTPSMSViewModel.swift */; };
		DF0AD9812BD769820098FDAF /* OTPSMSViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD94D2BD769820098FDAF /* OTPSMSViewController.swift */; };
		DF0AD9822BD769820098FDAF /* PinSetUpViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD94F2BD769820098FDAF /* PinSetUpViewModel.swift */; };
		DF0AD9832BD769820098FDAF /* CreatePinViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD9512BD769820098FDAF /* CreatePinViewController.swift */; };
		DF0AD9842BD769820098FDAF /* CreatePinViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD9522BD769820098FDAF /* CreatePinViewModel.swift */; };
		DF0AD9852BD769820098FDAF /* BiometricSetupViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD9542BD769820098FDAF /* BiometricSetupViewModel.swift */; };
		DF0AD9862BD769820098FDAF /* BiometricSetupViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD9552BD769820098FDAF /* BiometricSetupViewController.swift */; };
		DF0AD9872BD769820098FDAF /* PinSetUpViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD9562BD769820098FDAF /* PinSetUpViewController.swift */; };
		DF0AD9882BD769820098FDAF /* ConfirmPinViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD9582BD769820098FDAF /* ConfirmPinViewController.swift */; };
		DF0AD9892BD769820098FDAF /* ConfirmPinViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD9592BD769820098FDAF /* ConfirmPinViewModel.swift */; };
		DF0AD98A2BD769820098FDAF /* ChangePinViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD95B2BD769820098FDAF /* ChangePinViewModel.swift */; };
		DF0AD98B2BD769820098FDAF /* ChangePinViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD95C2BD769820098FDAF /* ChangePinViewController.swift */; };
		DF0AD98E2BD769820098FDAF /* PinLandingViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD9612BD769820098FDAF /* PinLandingViewController.swift */; };
		DF0AD98F2BD769820098FDAF /* PinLandingViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD9622BD769820098FDAF /* PinLandingViewModel.swift */; };
		DF0AD9902BD769820098FDAF /* CreatePasswordInputView.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD9642BD769820098FDAF /* CreatePasswordInputView.swift */; };
		DF0AD9912BD769820098FDAF /* PinLoginViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD9662BD769820098FDAF /* PinLoginViewController.swift */; };
		DF0AD9922BD769820098FDAF /* PinLoginViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD9672BD769820098FDAF /* PinLoginViewModel.swift */; };
		DF0AE0422BD76CD70098FDAF /* APILayer.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = DF0AE0412BD76CD70098FDAF /* APILayer.framework */; };
		DF0AE0462BD76CDD0098FDAF /* CUIModule.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = DF0AE0452BD76CDD0098FDAF /* CUIModule.framework */; };
		DF0AE0492BD76CE30098FDAF /* SharedData.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = DF0AE0482BD76CE30098FDAF /* SharedData.framework */; };
		DF0AE04C2BD76CEC0098FDAF /* Storage.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = DF0AE04B2BD76CEC0098FDAF /* Storage.framework */; };
		DF273D1D2BD767B6003F9464 /* Authentication.h in Headers */ = {isa = PBXBuildFile; fileRef = DF273D1C2BD767B6003F9464 /* Authentication.h */; settings = {ATTRIBUTES = (Public, ); }; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		0D47456FFAD0BBDB2C958821 /* Pods_Authentication.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_Authentication.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		47760D91722F430C4EDDF590 /* Pods-Authentication.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Authentication.release.xcconfig"; path = "Target Support Files/Pods-Authentication/Pods-Authentication.release.xcconfig"; sourceTree = "<group>"; };
		7155B0C96D202AE7E8A39129 /* Pods-Authentication.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Authentication.debug.xcconfig"; path = "Target Support Files/Pods-Authentication/Pods-Authentication.debug.xcconfig"; sourceTree = "<group>"; };
		DF0AD92A2BD769820098FDAF /* QRView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = QRView.swift; sourceTree = "<group>"; };
		DF0AD92C2BD769820098FDAF /* AuthNavigationView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AuthNavigationView.swift; sourceTree = "<group>"; };
		DF0AD92D2BD769820098FDAF /* CodeView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CodeView.swift; sourceTree = "<group>"; };
		DF0AD92F2BD769820098FDAF /* BiometricSettingViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = BiometricSettingViewController.swift; sourceTree = "<group>"; };
		DF0AD9302BD769820098FDAF /* BiometricSettingViewModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = BiometricSettingViewModel.swift; sourceTree = "<group>"; };
		DF0AD9312BD769820098FDAF /* AuthenticationCoordinator.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AuthenticationCoordinator.swift; sourceTree = "<group>"; };
		DF0AD9362BD769820098FDAF /* OTPInputViewModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = OTPInputViewModel.swift; sourceTree = "<group>"; };
		DF0AD9372BD769820098FDAF /* OTPOptionSelectViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = OTPOptionSelectViewController.swift; sourceTree = "<group>"; };
		DF0AD9392BD769820098FDAF /* OTPOptionView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = OTPOptionView.swift; sourceTree = "<group>"; };
		DF0AD93A2BD769820098FDAF /* OTPEmailInputViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = OTPEmailInputViewController.swift; sourceTree = "<group>"; };
		DF0AD93B2BD769820098FDAF /* OTPInputViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = OTPInputViewController.swift; sourceTree = "<group>"; };
		DF0AD93D2BD769820098FDAF /* PinSetupCompleteViewModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = PinSetupCompleteViewModel.swift; sourceTree = "<group>"; };
		DF0AD93E2BD769820098FDAF /* PinSetupCompleteViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = PinSetupCompleteViewController.swift; sourceTree = "<group>"; };
		DF0AD9402BD769820098FDAF /* ForgotPinViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ForgotPinViewController.swift; sourceTree = "<group>"; };
		DF0AD9412BD769820098FDAF /* ForgotPinViewModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ForgotPinViewModel.swift; sourceTree = "<group>"; };
		DF0AD9432BD769820098FDAF /* ForgotPinEmailViewModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ForgotPinEmailViewModel.swift; sourceTree = "<group>"; };
		DF0AD9442BD769820098FDAF /* ForgotPinEmailViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ForgotPinEmailViewController.swift; sourceTree = "<group>"; };
		DF0AD9462BD769820098FDAF /* TwoFAEnabledViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = TwoFAEnabledViewController.swift; sourceTree = "<group>"; };
		DF0AD9472BD769820098FDAF /* TwoFAEnabledViewModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = TwoFAEnabledViewModel.swift; sourceTree = "<group>"; };
		DF0AD9492BD769820098FDAF /* TwoFALandingViewModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = TwoFALandingViewModel.swift; sourceTree = "<group>"; };
		DF0AD94A2BD769820098FDAF /* TwoFALandingViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = TwoFALandingViewController.swift; sourceTree = "<group>"; };
		DF0AD94C2BD769820098FDAF /* OTPSMSViewModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = OTPSMSViewModel.swift; sourceTree = "<group>"; };
		DF0AD94D2BD769820098FDAF /* OTPSMSViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = OTPSMSViewController.swift; sourceTree = "<group>"; };
		DF0AD94F2BD769820098FDAF /* PinSetUpViewModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = PinSetUpViewModel.swift; sourceTree = "<group>"; };
		DF0AD9512BD769820098FDAF /* CreatePinViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CreatePinViewController.swift; sourceTree = "<group>"; };
		DF0AD9522BD769820098FDAF /* CreatePinViewModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CreatePinViewModel.swift; sourceTree = "<group>"; };
		DF0AD9542BD769820098FDAF /* BiometricSetupViewModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = BiometricSetupViewModel.swift; sourceTree = "<group>"; };
		DF0AD9552BD769820098FDAF /* BiometricSetupViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = BiometricSetupViewController.swift; sourceTree = "<group>"; };
		DF0AD9562BD769820098FDAF /* PinSetUpViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = PinSetUpViewController.swift; sourceTree = "<group>"; };
		DF0AD9582BD769820098FDAF /* ConfirmPinViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ConfirmPinViewController.swift; sourceTree = "<group>"; };
		DF0AD9592BD769820098FDAF /* ConfirmPinViewModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ConfirmPinViewModel.swift; sourceTree = "<group>"; };
		DF0AD95B2BD769820098FDAF /* ChangePinViewModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ChangePinViewModel.swift; sourceTree = "<group>"; };
		DF0AD95C2BD769820098FDAF /* ChangePinViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ChangePinViewController.swift; sourceTree = "<group>"; };
		DF0AD9612BD769820098FDAF /* PinLandingViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = PinLandingViewController.swift; sourceTree = "<group>"; };
		DF0AD9622BD769820098FDAF /* PinLandingViewModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = PinLandingViewModel.swift; sourceTree = "<group>"; };
		DF0AD9642BD769820098FDAF /* CreatePasswordInputView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CreatePasswordInputView.swift; sourceTree = "<group>"; };
		DF0AD9662BD769820098FDAF /* PinLoginViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = PinLoginViewController.swift; sourceTree = "<group>"; };
		DF0AD9672BD769820098FDAF /* PinLoginViewModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = PinLoginViewModel.swift; sourceTree = "<group>"; };
		DF0AE0412BD76CD70098FDAF /* APILayer.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = APILayer.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		DF0AE0452BD76CDD0098FDAF /* CUIModule.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = CUIModule.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		DF0AE0482BD76CE30098FDAF /* SharedData.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = SharedData.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		DF0AE04B2BD76CEC0098FDAF /* Storage.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = Storage.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		DF273D192BD767B6003F9464 /* Authentication.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Authentication.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		DF273D1C2BD767B6003F9464 /* Authentication.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = Authentication.h; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		DF273D162BD767B6003F9464 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				2A326C79E61CC9A798C8B3C2 /* Pods_Authentication.framework in Frameworks */,
				DF0AE0492BD76CE30098FDAF /* SharedData.framework in Frameworks */,
				DF0AE04C2BD76CEC0098FDAF /* Storage.framework in Frameworks */,
				DF0AE0422BD76CD70098FDAF /* APILayer.framework in Frameworks */,
				DF0AE0462BD76CDD0098FDAF /* CUIModule.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		26B6DE3FA84D7F2953422E4D /* Pods */ = {
			isa = PBXGroup;
			children = (
				7155B0C96D202AE7E8A39129 /* Pods-Authentication.debug.xcconfig */,
				47760D91722F430C4EDDF590 /* Pods-Authentication.release.xcconfig */,
			);
			name = Pods;
			path = ../Pods;
			sourceTree = "<group>";
		};
		A8779B4C253F3D42B2F18BF3 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				DF0AE04B2BD76CEC0098FDAF /* Storage.framework */,
				DF0AE0482BD76CE30098FDAF /* SharedData.framework */,
				DF0AE0452BD76CDD0098FDAF /* CUIModule.framework */,
				DF0AE0412BD76CD70098FDAF /* APILayer.framework */,
				0D47456FFAD0BBDB2C958821 /* Pods_Authentication.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		DF0AD9292BD769820098FDAF /* Views */ = {
			isa = PBXGroup;
			children = (
				DF0AD92A2BD769820098FDAF /* QRView.swift */,
				DF0AD92C2BD769820098FDAF /* AuthNavigationView.swift */,
				DF0AD92D2BD769820098FDAF /* CodeView.swift */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		DF0AD92E2BD769820098FDAF /* BiometricSetting */ = {
			isa = PBXGroup;
			children = (
				DF0AD92F2BD769820098FDAF /* BiometricSettingViewController.swift */,
				DF0AD9302BD769820098FDAF /* BiometricSettingViewModel.swift */,
			);
			path = BiometricSetting;
			sourceTree = "<group>";
		};
		DF0AD9352BD769820098FDAF /* OTPInput */ = {
			isa = PBXGroup;
			children = (
				DF0AD93B2BD769820098FDAF /* OTPInputViewController.swift */,
				DF0AD9362BD769820098FDAF /* OTPInputViewModel.swift */,
				DF0AD9372BD769820098FDAF /* OTPOptionSelectViewController.swift */,
				DF0AD93A2BD769820098FDAF /* OTPEmailInputViewController.swift */,
				DF0AD9382BD769820098FDAF /* Subviews */,
			);
			path = OTPInput;
			sourceTree = "<group>";
		};
		DF0AD9382BD769820098FDAF /* Subviews */ = {
			isa = PBXGroup;
			children = (
				DF0AD9392BD769820098FDAF /* OTPOptionView.swift */,
			);
			path = Subviews;
			sourceTree = "<group>";
		};
		DF0AD93C2BD769820098FDAF /* PinSetupComplete */ = {
			isa = PBXGroup;
			children = (
				DF0AD93E2BD769820098FDAF /* PinSetupCompleteViewController.swift */,
				DF0AD93D2BD769820098FDAF /* PinSetupCompleteViewModel.swift */,
			);
			path = PinSetupComplete;
			sourceTree = "<group>";
		};
		DF0AD93F2BD769820098FDAF /* ForgotPin */ = {
			isa = PBXGroup;
			children = (
				DF0AD9402BD769820098FDAF /* ForgotPinViewController.swift */,
				DF0AD9412BD769820098FDAF /* ForgotPinViewModel.swift */,
				DF0AD9422BD769820098FDAF /* ForgotPinEmail */,
			);
			path = ForgotPin;
			sourceTree = "<group>";
		};
		DF0AD9422BD769820098FDAF /* ForgotPinEmail */ = {
			isa = PBXGroup;
			children = (
				DF0AD9442BD769820098FDAF /* ForgotPinEmailViewController.swift */,
				DF0AD9432BD769820098FDAF /* ForgotPinEmailViewModel.swift */,
			);
			path = ForgotPinEmail;
			sourceTree = "<group>";
		};
		DF0AD9482BD769820098FDAF /* 2FA */ = {
			isa = PBXGroup;
			children = (
				DF0AD94A2BD769820098FDAF /* TwoFALandingViewController.swift */,
				DF0AD9492BD769820098FDAF /* TwoFALandingViewModel.swift */,
				DF0AD9462BD769820098FDAF /* TwoFAEnabledViewController.swift */,
				DF0AD9472BD769820098FDAF /* TwoFAEnabledViewModel.swift */,
			);
			path = 2FA;
			sourceTree = "<group>";
		};
		DF0AD94B2BD769820098FDAF /* OTPSMS */ = {
			isa = PBXGroup;
			children = (
				DF0AD94D2BD769820098FDAF /* OTPSMSViewController.swift */,
				DF0AD94C2BD769820098FDAF /* OTPSMSViewModel.swift */,
			);
			path = OTPSMS;
			sourceTree = "<group>";
		};
		DF0AD94E2BD769820098FDAF /* PinSetup */ = {
			isa = PBXGroup;
			children = (
				DF0AD9562BD769820098FDAF /* PinSetUpViewController.swift */,
				DF0AD94F2BD769820098FDAF /* PinSetUpViewModel.swift */,
				DF0AD9502BD769820098FDAF /* CreatePin */,
				DF0AD9532BD769820098FDAF /* BiometricSetup */,
				DF0AD9572BD769820098FDAF /* ConfirmPin */,
			);
			path = PinSetup;
			sourceTree = "<group>";
		};
		DF0AD9502BD769820098FDAF /* CreatePin */ = {
			isa = PBXGroup;
			children = (
				DF0AD9512BD769820098FDAF /* CreatePinViewController.swift */,
				DF0AD9522BD769820098FDAF /* CreatePinViewModel.swift */,
			);
			path = CreatePin;
			sourceTree = "<group>";
		};
		DF0AD9532BD769820098FDAF /* BiometricSetup */ = {
			isa = PBXGroup;
			children = (
				DF0AD9552BD769820098FDAF /* BiometricSetupViewController.swift */,
				DF0AD9542BD769820098FDAF /* BiometricSetupViewModel.swift */,
			);
			path = BiometricSetup;
			sourceTree = "<group>";
		};
		DF0AD9572BD769820098FDAF /* ConfirmPin */ = {
			isa = PBXGroup;
			children = (
				DF0AD9582BD769820098FDAF /* ConfirmPinViewController.swift */,
				DF0AD9592BD769820098FDAF /* ConfirmPinViewModel.swift */,
			);
			path = ConfirmPin;
			sourceTree = "<group>";
		};
		DF0AD95A2BD769820098FDAF /* ChangePin */ = {
			isa = PBXGroup;
			children = (
				DF0AD95C2BD769820098FDAF /* ChangePinViewController.swift */,
				DF0AD95B2BD769820098FDAF /* ChangePinViewModel.swift */,
			);
			path = ChangePin;
			sourceTree = "<group>";
		};
		DF0AD9602BD769820098FDAF /* PinLanding */ = {
			isa = PBXGroup;
			children = (
				DF0AD9612BD769820098FDAF /* PinLandingViewController.swift */,
				DF0AD9622BD769820098FDAF /* PinLandingViewModel.swift */,
			);
			path = PinLanding;
			sourceTree = "<group>";
		};
		DF0AD9632BD769820098FDAF /* Password */ = {
			isa = PBXGroup;
			children = (
				DF0AD9642BD769820098FDAF /* CreatePasswordInputView.swift */,
			);
			path = Password;
			sourceTree = "<group>";
		};
		DF0AD9652BD769820098FDAF /* PinLogin */ = {
			isa = PBXGroup;
			children = (
				DF0AD9662BD769820098FDAF /* PinLoginViewController.swift */,
				DF0AD9672BD769820098FDAF /* PinLoginViewModel.swift */,
			);
			path = PinLogin;
			sourceTree = "<group>";
		};
		DF273D0F2BD767B6003F9464 = {
			isa = PBXGroup;
			children = (
				DF273D1B2BD767B6003F9464 /* Authentication */,
				DF273D1A2BD767B6003F9464 /* Products */,
				26B6DE3FA84D7F2953422E4D /* Pods */,
				A8779B4C253F3D42B2F18BF3 /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		DF273D1A2BD767B6003F9464 /* Products */ = {
			isa = PBXGroup;
			children = (
				DF273D192BD767B6003F9464 /* Authentication.framework */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		DF273D1B2BD767B6003F9464 /* Authentication */ = {
			isa = PBXGroup;
			children = (
				DF0AD9312BD769820098FDAF /* AuthenticationCoordinator.swift */,
				DF0AD92E2BD769820098FDAF /* BiometricSetting */,
				DF0AD95A2BD769820098FDAF /* ChangePin */,
				DF0AD93F2BD769820098FDAF /* ForgotPin */,
				DF0AD9352BD769820098FDAF /* OTPInput */,
				DF0AD94B2BD769820098FDAF /* OTPSMS */,
				DF0AD9632BD769820098FDAF /* Password */,
				DF0AD9602BD769820098FDAF /* PinLanding */,
				DF0AD9652BD769820098FDAF /* PinLogin */,
				DF0AD94E2BD769820098FDAF /* PinSetup */,
				DF0AD93C2BD769820098FDAF /* PinSetupComplete */,
				DF0AD9482BD769820098FDAF /* 2FA */,
				DF0AD9292BD769820098FDAF /* Views */,
				DF273D1C2BD767B6003F9464 /* Authentication.h */,
			);
			path = Authentication;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		DF273D142BD767B6003F9464 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				DF273D1D2BD767B6003F9464 /* Authentication.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		DF273D182BD767B6003F9464 /* Authentication */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = DF273D202BD767B6003F9464 /* Build configuration list for PBXNativeTarget "Authentication" */;
			buildPhases = (
				8E1E00D83B065249A701CFD5 /* [CP] Check Pods Manifest.lock */,
				DF273D142BD767B6003F9464 /* Headers */,
				DF273D152BD767B6003F9464 /* Sources */,
				DF273D162BD767B6003F9464 /* Frameworks */,
				DF273D172BD767B6003F9464 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = Authentication;
			productName = Authentication;
			productReference = DF273D192BD767B6003F9464 /* Authentication.framework */;
			productType = "com.apple.product-type.framework";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		DF273D102BD767B6003F9464 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastUpgradeCheck = 1500;
				TargetAttributes = {
					DF273D182BD767B6003F9464 = {
						CreatedOnToolsVersion = 15.0;
						LastSwiftMigration = 1500;
					};
				};
			};
			buildConfigurationList = DF273D132BD767B6003F9464 /* Build configuration list for PBXProject "Authentication" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = DF273D0F2BD767B6003F9464;
			productRefGroup = DF273D1A2BD767B6003F9464 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				DF273D182BD767B6003F9464 /* Authentication */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		DF273D172BD767B6003F9464 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		8E1E00D83B065249A701CFD5 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-Authentication-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		DF273D152BD767B6003F9464 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				DF0AD96D2BD769820098FDAF /* BiometricSettingViewModel.swift in Sources */,
				DF0AD9892BD769820098FDAF /* ConfirmPinViewModel.swift in Sources */,
				DF0AD9902BD769820098FDAF /* CreatePasswordInputView.swift in Sources */,
				DF0AD9792BD769820098FDAF /* ForgotPinViewModel.swift in Sources */,
				DF0AD98E2BD769820098FDAF /* PinLandingViewController.swift in Sources */,
				DF0AD96E2BD769820098FDAF /* AuthenticationCoordinator.swift in Sources */,
				DF0AD97E2BD769820098FDAF /* TwoFALandingViewModel.swift in Sources */,
				DF0AD9882BD769820098FDAF /* ConfirmPinViewController.swift in Sources */,
				DF0AD9712BD769820098FDAF /* OTPInputViewModel.swift in Sources */,
				DF0AD9832BD769820098FDAF /* CreatePinViewController.swift in Sources */,
				DF0AD9812BD769820098FDAF /* OTPSMSViewController.swift in Sources */,
				DF0AD97A2BD769820098FDAF /* ForgotPinEmailViewModel.swift in Sources */,
				DF0AD9682BD769820098FDAF /* QRView.swift in Sources */,
				DF0AD97F2BD769820098FDAF /* TwoFALandingViewController.swift in Sources */,
				DF0AD9722BD769820098FDAF /* OTPOptionSelectViewController.swift in Sources */,
				DF0AD9822BD769820098FDAF /* PinSetUpViewModel.swift in Sources */,
				DF0AD98A2BD769820098FDAF /* ChangePinViewModel.swift in Sources */,
				DF0AD97B2BD769820098FDAF /* ForgotPinEmailViewController.swift in Sources */,
				DF0AD9872BD769820098FDAF /* PinSetUpViewController.swift in Sources */,
				DF0AD9742BD769820098FDAF /* OTPEmailInputViewController.swift in Sources */,
				DF0AD98B2BD769820098FDAF /* ChangePinViewController.swift in Sources */,
				DF0AD96A2BD769820098FDAF /* AuthNavigationView.swift in Sources */,
				DF0AD9922BD769820098FDAF /* PinLoginViewModel.swift in Sources */,
				DF0AD9912BD769820098FDAF /* PinLoginViewController.swift in Sources */,
				DF0AD9862BD769820098FDAF /* BiometricSetupViewController.swift in Sources */,
				DF0AD9802BD769820098FDAF /* OTPSMSViewModel.swift in Sources */,
				DF0AD9762BD769820098FDAF /* PinSetupCompleteViewModel.swift in Sources */,
				DF0AD9732BD769820098FDAF /* OTPOptionView.swift in Sources */,
				DF0AD9852BD769820098FDAF /* BiometricSetupViewModel.swift in Sources */,
				DF0AD9782BD769820098FDAF /* ForgotPinViewController.swift in Sources */,
				DF0AD96B2BD769820098FDAF /* CodeView.swift in Sources */,
				DF0AD96C2BD769820098FDAF /* BiometricSettingViewController.swift in Sources */,
				DF0AD97D2BD769820098FDAF /* TwoFAEnabledViewModel.swift in Sources */,
				DF0AD9752BD769820098FDAF /* OTPInputViewController.swift in Sources */,
				DF0AD98F2BD769820098FDAF /* PinLandingViewModel.swift in Sources */,
				DF0AD9772BD769820098FDAF /* PinSetupCompleteViewController.swift in Sources */,
				DF0AD9842BD769820098FDAF /* CreatePinViewModel.swift in Sources */,
				DF0AD97C2BD769820098FDAF /* TwoFAEnabledViewController.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		DF273D1E2BD767B6003F9464 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		DF273D1F2BD767B6003F9464 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		DF273D212BD767B6003F9464 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7155B0C96D202AE7E8A39129 /* Pods-Authentication.debug.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				MODULE_VERIFIER_SUPPORTED_LANGUAGES = "objective-c objective-c++";
				MODULE_VERIFIER_SUPPORTED_LANGUAGE_STANDARDS = "gnu17 gnu++20";
				PRODUCT_BUNDLE_IDENTIFIER = com.sirius.Authentication;
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		DF273D222BD767B6003F9464 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 47760D91722F430C4EDDF590 /* Pods-Authentication.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				MODULE_VERIFIER_SUPPORTED_LANGUAGES = "objective-c objective-c++";
				MODULE_VERIFIER_SUPPORTED_LANGUAGE_STANDARDS = "gnu17 gnu++20";
				PRODUCT_BUNDLE_IDENTIFIER = com.sirius.Authentication;
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		DF273D132BD767B6003F9464 /* Build configuration list for PBXProject "Authentication" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				DF273D1E2BD767B6003F9464 /* Debug */,
				DF273D1F2BD767B6003F9464 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		DF273D202BD767B6003F9464 /* Build configuration list for PBXNativeTarget "Authentication" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				DF273D212BD767B6003F9464 /* Debug */,
				DF273D222BD767B6003F9464 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = DF273D102BD767B6003F9464 /* Project object */;
}
