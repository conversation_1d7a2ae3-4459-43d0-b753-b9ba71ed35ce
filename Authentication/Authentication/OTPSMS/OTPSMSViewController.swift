//
//  OTPSMSViewController.swift
//  Authentication
//
//  Created by <PERSON><PERSON><PERSON> on 1/8/67.
//
import Core
import CUIModule
import RxSwift
import RxCocoa
import Utils
import SharedData
import APILayer

final class OTPSMSViewController: BaseViewController, VMView {
    
    // MARK: UI properties
    private lazy var navigationView = AuthNavigationView(title: "key0192".localized())
    
    private lazy var container = UIView()
    
    private lazy var inputViewContoller = OTPInputViewController(sourceType: viewModel.authSourceType,
                                                                 parentView: view,
                                                                 navigationView: navigationView)
    
    // MARK: Properties
    var viewModel: OTPSMSViewModel!
    
    // MARK: Life cycle
    override func viewDidLoad() {
        super.viewDidLoad()
        
        setupUI()
        bindActions()
        bindViewModelLogic()
    }
    
    func setupUI() {
        view.addSubviews([navigationView,
                          container])
        navigationView.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide)
            make.left.right.equalToSuperview()
        }
        
        container.snp.makeConstraints { make in
            make.top.equalTo(navigationView.snp.bottom).offset(24)
            make.left.right.bottom.equalToSuperview()
        }
        
        addChild(controller: inputViewContoller, 
                 container: container)
    }

    func bindActions() {
        navigationView.rx.back.subscribe(onNext: { [weak self] in
            self?.backPress()
        }).disposed(by: disposeBag)
    }
    
    func bindViewModelLogic() {
        let input = OTPSMSViewModel.Input(onViewAppear: rx.viewWillAppear.take(1).mapToVoid(),
                                          onResendOtp: inputViewContoller.rx.resendOTP,
                                          onSubmit: inputViewContoller.rx.submitOtp)
        
        let output = viewModel.transform(input: input)
        
        output.loading.drive(onNext: { [unowned self] in
            $0 ? showLoading() : hiddenLoading()
        }).disposed(by: disposeBag)
        
        output.error.drive(onNext: { [unowned self] in
            inputViewContoller.setError(true)
            
            let message = ($0 as? APIError)?.message ?? $0.localizedDescription
            CUIToastView.show(data: .init(title: "key0895".localized(),
                                          desc:message,
                                          titleStyle: .init(textColor: Color.txtNegative)),
                              inView: view,
                              underView: navigationView)
        }).disposed(by: disposeBag)
        
        output.otpRequested.drive(onNext: { [unowned self] in
            inputViewContoller.otpRequested.onNext((address: nil,
                                                    mobileRegion: nil,
                                                    response: $0))
            
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) { [unowned self] in
                CUIToastView.show(data: .init(title: "key0111".localized(),
                                              desc: "key0824".localized(),
                                              titleStyle: .init(textColor: Color.txtTitle)),
                                  inView: view,
                                  underView: navigationView)
                
            }
        }).disposed(by: disposeBag)
        
        output.submitOTP.drive(onNext: { [unowned self] shouldPop in
            if shouldPop {
                backPress()
            }
        }).disposed(by: disposeBag)
    }
}
