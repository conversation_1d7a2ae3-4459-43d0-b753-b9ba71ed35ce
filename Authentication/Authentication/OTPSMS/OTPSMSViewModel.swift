//
//  OTPSMSViewModel.swift
//  Authentication
//
//  Created by <PERSON><PERSON><PERSON> on 1/8/67.
//
import APILayer
import Core
import CUIModule
import RxCocoa
import RxSwift
import RxRelay
import XCoordinator
import SharedData
import Storage

public class OTPSMSViewModel: AnyViewModel {
    
    private let router: UnownedRouter<AuthenticationRoute>
    
    public struct Input {
        let onViewAppear: Observable<Void>
        let onResendOtp: Observable<Void>
        let onSubmit: Observable<String>
    }
    
    public struct Output {
        let loading: Driver<Bool>
        let error: Driver<Error>
        let otpRequested: Driver<OTPRequestEndPoint.Response>
        let submitOTP: Driver<Bool>
    }
    
    // Output to Coordinator
    public let onCompleted = PublishSubject<OTPScreenType>()
    private let initialOtpResponse: OTPRequestEndPoint.Response?
    private var refCode: String = ""
    
    var authSourceType: OTPInputViewController.SourceType {
        switch screenType {
        case .twoFactorSetUp(let type):
            fallthrough
        case .twoFactorLogin(let type):
            switch type {
            case .email:
                return .email
            case .mobile:
                return .phone
            }
            
        default:
            return .email
        }
    }
    
    private var screenType: OTPScreenType
    
    init(router: UnownedRouter<AuthenticationRoute>,
         otpType: OTPScreenType,
         initialOtpResponse: OTPRequestEndPoint.Response?) {
        self.router = router
        self.screenType = otpType
        self.initialOtpResponse = initialOtpResponse
    }
    
    public func transform(input: Input) -> Output {
        let activityIndicator = ActivityIndicator()
        let errorTracker = ErrorTracker()
        
        let otpRequest = input.onViewAppear
            .flatMap { [unowned self] in
                if let response = initialOtpResponse {
                    return Observable.just(response)
                }
                
                return OTPRequestEndPoint.service.requestOTP(for: screenType)
                    .track(activityIndicator, error: errorTracker)
            }
        
        let otpResend = input.onResendOtp
            .flatMap { [unowned self] in
                OTPRequestEndPoint.service.requestOTP(for: screenType)
                    .track(activityIndicator, error: errorTracker)
            }
        
        let otpResponse = Observable.merge(otpRequest,
                                           otpResend)
            .onNext { [unowned self] in
                refCode = $0.refCode ?? ""
            }
        
        let submit = input.onSubmit
            .flatMap { [unowned self] in
                submitOTP(otpCode: $0)
                    .track(activityIndicator, error: errorTracker)
            }
            .onNext { [unowned self] in
                onCompleted.onNext(screenType)
            }
            .map {
                switch self.screenType {
                case .walletCashWithdraw,
                        .walletDeposit:
                    return true
                    
                default:
                    return false
                }
            }
        
        return Output(loading: activityIndicator.asDriver(),
                      error: errorTracker.asDriver(),
                      otpRequested: otpResponse.asDriverOnErrorNever(),
                      submitOTP: submit.asDriverOnErrorNever())
    }
    
    func navigate(to route: AuthenticationRoute) {
        router.trigger(route)
    }
}

// MARK: - Api call
private extension OTPSMSViewModel {
    
    func submitOTP(otpCode: String) -> Observable<Void> {
        switch screenType {
        case let .walletCashWithdraw(amount, currency, bankAccountId):
            return createCashWithdraw(request: .init(refCode: refCode,
                                                     otpCode: otpCode,
                                                     otpType: OTPAuthType.email.rawValue,
                                                     otpAddress: Keychain.userName ?? "",
                                                     currency: currency,
                                                     amount: amount,
                                                     bankAccountId: bankAccountId))
            
        case let .walletDeposit(amount, currency, bankAccountId, docKey):
            return createDeposit(request: .init(refCode: refCode,
                                                otpCode: otpCode,
                                                otpType: OTPAuthType.email.rawValue,
                                                otpAddress: Keychain.userName ?? "",
                                                currency: currency,
                                                amount: amount,
                                                bankAccountId: bankAccountId,
                                                proofFile: docKey))
            
        case .twoFactorSetUp(let type):
            return setUpTwoFactor(request: .init(action: "ENABLE",
                                                 otpType: type.rawValue,
                                                 otp: otpCode,
                                                 refCode: refCode))
            
        case .twoFactorLogin(let type):
            return loginTwoFactor(reqeust: .init(username: Keychain.userName ?? "",
                                                 deviceId: LocalPreference.deviceId ?? "",
                                                 password: nil,
                                                 pin: nil,
                                                 otp: otpCode,
                                                 token: Keychain.token ?? "",
                                                 otpType: type.rawValue, 
                                                 refCode: refCode))
            .onNext {
                LocalPreference.sessionId = $0.sessionId
                LocalPreference.configuredPin = $0.configuredPin
            }
            .mapToVoid()
            .flatMap(fetchUserInformation)
            .onNext { Keychain.userInformation = $0 }
            .mapToVoid()
            .flatMap {
                QueryUserProfileEndPoint.service.call()
            }
            .onNext { Keychain.userProfile = $0 }
            .mapToVoid()
        }
    }

    func setUpTwoFactor(request: UserTwoFAEndPoint.Request) -> Observable<Void> {
        UserTwoFAEndPoint.service.call(parameter: request).mapToVoid()
    }

    func loginTwoFactor(reqeust: UserLoginEndPoint.Request) -> Observable<UserLoginEndPoint.Response> {
        UserLoginEndPoint.service.request(parameters: reqeust)
    }

    private func fetchUserInformation() -> Observable<UserInformationEndPoint.Response> {
        UserInformationEndPoint.service.call()
    }

    func createDeposit(request: DepositEndPoint.Request) -> Observable<Void> {
        DepositEndPoint.service.call(parameter: request).mapToVoid()
    }

    func createCashWithdraw(request: CashWithdrawEndPoint.Request) -> Observable<Void> {
        CashWithdrawEndPoint.service.call(parameter: request).mapToVoid()
    }
}
