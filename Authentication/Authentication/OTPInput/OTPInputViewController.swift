//
//  OTPInputViewController.swift
//  Authentication
//
//  Created by <PERSON><PERSON><PERSON> on 3/5/67.
//
import Core
import CUIModule
import RxSwift
import RxCocoa
import Utils
import APILayer
import IQKeyboardManagerSwift

public final class OTPInputViewController: BaseViewController {
    
    // MARK: UI properties
    private lazy var contentStackView = {
        let stackView = UIStackView(axis: .vertical,
                                    alignment: .center,
                                    spacing: 16)
        
        stackView.addArrangedSubviews([imageView,
                                       titleLabel,
                                       captionLabel,
                                       otpView,
                                       countDownView,
                                       errorView,
                                       refLabel])
        stackView.setCustomSpacing(8, after: titleLabel)
        imageView.snp.makeConstraints { make in
            make.width.equalTo(138)
            make.height.equalTo(120)
        }
        countDownView.snp.makeConstraints { make in
            make.height.equalTo(24)
        }
        errorView.isHidden = true
        
        return stackView
    }()

    private lazy var imageView = UIImageView(contentMode: .scaleAspectFit)

    private lazy var titleLabel = UILabel(font: Font.semiBold.of(size: 18),
                                          textColor: Color.txtTitle)

    private lazy var captionLabel = UILabel()
    
    private lazy var countDownView = CountDownView()
    
    private lazy var errorView = UIButton(type: .system,
                                          backgroundColor: Color.bgNegative,
                                          titleFont: Font.regular.of(size: 14),
                                          title: "key0945".localized(),
                                          normalTitleColor: Color.txtNegative,
                                          cornerRadius: 2,
                                          contentEdgeInsets: UIEdgeInsets(with: 2, left: 20, bottom: 2, right: 20),
                                          isUserInteractionEnabled: false)
    
    fileprivate lazy var otpView = OTPInputView()
    
    private lazy var refLabel = UILabel()
    
    private lazy var buttonStackView = {
        let stackView = UIStackView(distribution: .fillEqually,
                                    spacing: 8)
        
        stackView.addArrangedSubviews([resendOtpButton,
                                       submitButton])
        resendOtpButton.snp.makeConstraints { $0.height.equalTo(44) }
        submitButton.snp.makeConstraints { $0.height.equalTo(44) }
        
        return stackView
    }()
    
    fileprivate lazy var resendOtpButton = ThemeableButton(titleFont: Font.medium.of(size: 14),
                                                           title: "key0202".localized(),
                                                           normalTitleColor: Color.txtParagraph,
                                                           disabledTitleColor: Color.txtDisabled,
                                                           normalBgColor: Color.btn4th,
                                                           disabledBgColor: Color.btnDisabled,
                                                           cornerRadius: 22)
    
    fileprivate lazy var submitButton = ThemeableButton(titleFont: Font.bold.of(size: 16),
                                                        title: "key0045".localized(),
                                                        normalTitleColor: Color.txtInverted,
                                                        disabledTitleColor: Color.txtDisabled,
                                                        normalBgColor: Color.btn2nd,
                                                        disabledBgColor: Color.bgButtonDisabled,
                                                        cornerRadius: 22,
                                                        isEnable: false)
    
    // MARK: Properties
    var sourceType: SourceType
    let parentView: UIView
    let navigationView: UIView?
    let viewModel = OTPInputViewModel()
    
    public let otpRequested = PublishSubject<(address: String?,
                                              mobileRegion: String?,
                                              response: OTPRequestEndPoint.Response)>()
    let onSendOTP = PublishSubject<Void>()
    
    fileprivate let onExpired = BehaviorSubject<Bool>(value: false)
    fileprivate let onAutoSubmit = PublishSubject<String>()
    
    private var dropShadowOpacity: Float {
        return currentAppTheme == .light ? 0.07 : 0.5
    }

    // MARK: Life cycle
    public init(sourceType: SourceType,
                parentView: UIView,
                navigationView: UIView?) {
        self.sourceType = sourceType
        self.parentView = parentView
        self.navigationView = navigationView
        
        super.init(nibName: nil, bundle: nil)
        
        bind()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    public override func viewDidLoad() {
        super.viewDidLoad()
        
        setupUI()
        bindActions()
    }
    
    public override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        
        IQKeyboardManager.shared.enable = false
        IQKeyboardManager.shared.enableAutoToolbar = false
    }
    
    public override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        
        IQKeyboardManager.shared.enable = true
        IQKeyboardManager.shared.enableAutoToolbar = true
    }
    
    func setupUI() {
        view.addSubviews([contentStackView,
                          buttonStackView])
        contentStackView.snp.makeConstraints { make in
            make.top.equalTo(24)
            make.left.equalTo(12)
            make.right.equalTo(-12)
            make.bottom.lessThanOrEqualToSuperview()
        }
        
        buttonStackView.snp.makeConstraints { make in
            make.left.equalTo(12)
            make.right.equalTo(-12)
            make.bottom.equalTo(view.safeAreaLayoutGuide.snp.bottom).offset(-16)
        }
    }
    
    func bindActions() {
        otpView.rx.codeFilled
            .withLatestFrom(onExpired) { ($0, $1) }
            .subscribe(onNext: { [unowned self] filled, expired in
                submitButton.isEnabled = filled && !expired && viewModel.isValidRefCode()
                
                if !expired && filled && !expired && viewModel.isValidRefCode() {
                    onAutoSubmit.onNext(otpView.code)
                }
            })
            .disposed(by: disposeBag)
        
        onExpired
            .subscribe(onNext: { [unowned self] expired in
                if expired {
                    submitButton.isEnabled = false
                    setError(true)
                    
                    CUIToastView.show(data: .init(title: "key0944".localized(),
                                                  desc: "key0945".localized(),
                                                  titleStyle: .init(textColor: Color.txtNegative)),
                                      inView: parentView,
                                      underView: navigationView)
                }
            })
            .disposed(by: disposeBag)
    }
    
    func bind() {
        let input = OTPInputViewModel.Input(otpRequested: otpRequested.map {
            (type: self.sourceType,
             address: $0.address,
             mobileRegion: $0.mobileRegion,
             response: $0.response)
        })
        
        let output = viewModel.transform(input: input)
        
        output.loading.drive(onNext: { [weak self] in
            if $0 {
                self?.showLoading()
            } else {
                self?.hiddenLoading()
            }
        }).disposed(by: disposeBag)
        
        output.displayData.drive(onNext: { [weak self] in
            self?.displayUI($0)
            self?.resendOtpButton.isEnabled = false
        }).disposed(by: disposeBag)
        
        output.countDown.drive(onNext: { [weak self] in
            self?.countDownView.setTimeLeft(seconds: $0)
            self?.onExpired.onNext($0 <= 0)
        }).disposed(by: disposeBag)
    }
    
    public func setError(_ isError: Bool) {
        otpView.setErrorState(isError: isError)
        submitButton.isEnabled = false
        errorView.isHidden = !isError
        countDownView.isHidden = isError
        
        if isError && !resendOtpButton.isEnabled {
            resendOtpButton.isEnabled = true
        }
    }
    
    public func reset() {
        otpView.resetInput()
        submitButton.isEnabled = false
    }
    
    public func sendOTP() {
        onSendOTP.onNext(())
    }
}

// MARK: - Update UI
extension OTPInputViewController {
    
    private func displayUI(_ data: OTPInputViewModel.DisplayModel) {
        imageView.image = data.icon
        titleLabel.text = data.title
        
        captionLabel.highlight(data.caption + data.platform,
                               normalColor: Color.txtParagraph,
                               normalFont: Font.light.of(size: 14),
                               highlightText: [data.platform],
                               highlightColor: Color.txtLabel,
                               highlightFont: Font.medium.of(size: 14))
        
        refLabel.highlight(data.refTitle + data.refCode,
                           normalColor: Color.txtParagraph,
                           normalFont: Font.regular.of(size: 14),
                           highlightText: [data.refCode],
                           highlightColor: Color.txtLabel,
                           highlightFont: Font.regular.of(size: 14))
        countDownView.setTimeLeft(seconds: data.expireSeconds)
        
        otpView.resetInput()
        submitButton.isEnabled = false
        setError(false)
    }
    
    public func updateSourceType(sourceType: SourceType) {
        self.sourceType = sourceType
    }
    
    public func currentSourceType() -> SourceType {
        sourceType
    }
    
    public func updatePlatform(_ platform: String, resetTime: Bool) {
//        viewModel.updatePlatform.onNext(platform)
//        if resetTime {
//            refreshTimer.onNext(())
//        }
    }
}

// MARK: - Type
public extension OTPInputViewController {
    
    enum SourceType {
        case phone
        case email
        
        public var platform: String {
            switch self {
            case .phone: return "SMS"
            case .email: return "email"
            }
        }
        
        public var icon: UIImage? {
            switch self {
            case .phone: return .image(named: "iconphone")
            case .email: return .image(named: "iconemail")
            }
        }
        
        public var toastDesc: String {
            switch self {
            case .phone:
                return "key0112".localized()
            case .email:
                return "key0824".localized()
            }
        }
    }
}

// MARK: - JXSegmentedListContainerViewListDelegate
extension OTPInputViewController: JXSegmentedListContainerViewListDelegate {
    public func listView() -> UIView { view }
}

// MARK: - Reactive
public extension Reactive where Base: OTPInputViewController {
    
    var resendOTP: Observable<Void> {
        base.resendOtpButton.rx.tap.mapToVoid()
    }

    var submitOtp: Observable<String> {
        Observable.merge(base.submitButton.rx.tap.map { base.otpView.code },
                         base.onAutoSubmit.debounce(.milliseconds(100),
                                                    scheduler: MainScheduler.instance))
    }
}
