//
//  OTPInputViewModel.swift
//  Authentication
//
//  Created by <PERSON><PERSON><PERSON> on 3/5/67.
//
import APILayer
import Core
import CUIModule
import RxCocoa
import RxSwift
import RxRelay
import XCoordinator
import SharedData
import Storage
import UIKit

class OTPInputViewModel: AnyViewModel {
    
    struct Input {
        let otpRequested: Observable<(type: OTPInputViewController.SourceType,
                                      address: String?,
                                      mobileRegion: String?,
                                      response: OTPRequestEndPoint.Response)>
    }
    
    struct Output {
        let loading: Driver<Bool>
        let displayData: Driver<DisplayModel>
        let countDown: Driver<Int>
    }
    
    var expireSeconds: Int = 0
    private(set) var displayData: DisplayModel?
    
    func transform(input: Input) -> Output {
        let activityIndicator = ActivityIndicator()
        
        let otpRequested = input.otpRequested
            .map(mapToDisplayModel)
            .onNext { [unowned self] in
                displayData = $0
            }
            .share()
        
        let timer = Observable<Int>.interval(.seconds(1), scheduler: MainScheduler.instance)
            .map { self.expireSeconds - $0 }
            .filter { $0 >= 0 }
        
        let countDown = otpRequested.mapToVoid()
            .flatMapLatest { timer }
        
        return Output(loading: activityIndicator.asDriver(),
                      displayData: otpRequested.asDriverOnErrorNever(),
                      countDown: countDown.asDriverOnErrorNever())
    }
}

// MARK: - Data Mapping
extension OTPInputViewModel {
    
    func mapToDisplayModel(sourceType: OTPInputViewController.SourceType,
                           address: String?,
                           mobileRegion: String?,
                           response: OTPRequestEndPoint.Response) -> DisplayModel {
        expireSeconds = response.expireSeconds ?? 0
        
        switch sourceType {
        case .phone:
            return mapToPhone(address ?? Keychain.userInformation?.basic?.base?.phoneNumber ?? "",
                              mobileRegion: mobileRegion ?? Keychain.userInformation?.basic?.base?.mobileRegion ?? "",
                              refCode: response.refCode ?? "--")
            
        case .email:
            return mapToEmail(address ?? Keychain.userInformation?.basic?.base?.email ?? Keychain.userName ?? "",
                              refCode: response.refCode ?? "--")
        }
    }
    
    func mapToPhone(_ phone: String, mobileRegion: String, refCode: String) -> DisplayModel {
        var platform = phone.maskPhoneNumber()
        if !mobileRegion.isEmpty {
            platform = "(\(mobileRegion)) " + platform
        }
        
        return DisplayModel(icon: .image(named: "merit_otpphone"),
                            title: "key0031".localized(),
                            caption: "key0199".localized() + " ",
                            platform: platform,
                            expireSeconds: expireSeconds,
                            refTitle: "key0201".localized() + " ",
                            refCode: refCode)
    }
    
    func mapToEmail(_ mail: String, refCode: String) -> DisplayModel {
        DisplayModel(icon: .image(named: "merit_otpemail"),
                     title: "key0816".localized(),
                     caption: "key0033".localized() + " ",
                     platform: mail.maskEmail(with: "*"),
                     expireSeconds: expireSeconds,
                     refTitle: "key0201".localized() + " ",
                     refCode: refCode)
    }
    
    func isValidRefCode() -> Bool {
        guard
            let refCode = displayData?.refCode,
            !refCode.isEmpty,
            refCode != "--"
        else { return false }
        
        return true
    }
}

// MARK: - Display Model
extension OTPInputViewModel {

    struct DisplayModel {
        let icon: UIImage?
        let title: String
        let caption: String
        var platform: String
        let expireSeconds: Int
        let refTitle: String
        let refCode: String
    }
}

private extension String {
    
    func maskEmail(with mask: String) -> String {
        let email = self
        let components = email.components(separatedBy: "@")
        var maskEmail = ""
        if let first = components.first {
            maskEmail = first.enumerated().map { index, char in
                return [0, first.count - 1].contains(index) ? "\(char)" : mask
            }.joined()

        }
        if let last = components.last {
            // Append the `@` character and the last part of string
            maskEmail = maskEmail + "@" + last
        }

        return maskEmail
    }
}
