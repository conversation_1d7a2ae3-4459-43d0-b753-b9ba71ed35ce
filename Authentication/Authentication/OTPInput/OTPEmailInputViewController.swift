//
//  OTPEmailInputViewController.swift
//  Authentication
//
//  Created by <PERSON> on 04/03/2024.
//

import UIKit
import Core
import CUIModule
import RxSwift

public final class OTPEmailInputViewController: UIViewController, AnyView {
    
    // MARK: UI properties
    private lazy var contentStackView = {
        let stackView = UIStackView(axis: .vertical,
                                    spacing: 24)
        stackView.addArrangedSubviews([titleLabel,
                                       descLabel1,
                                       descLabel2,
                                       CUIDivider(),
                                       emailInputTF,
                                       contactButton])

        stackView.setCustomSpacing(16, after: titleLabel)
        stackView.setCustomSpacing(16, after: descLabel1)

        return stackView
    }()
    
    private lazy var titleLabel = UILabel(font: Font.medium.of(size: 14),
                                          text: "key0250".localized(),
                                          textColor: Color.txtTitle)

    private lazy var descLabel1 = UILabel(font: Font.light.of(size: 14),
                                         numberOfLines: 0,
                                         text: "key0251".localized(),
                                         textColor: Color.txtParagraph)

    private lazy var descLabel2 = UILabel(font: Font.light.of(size: 14),
                                         numberOfLines: 0,
                                         text: "key0252".localized(),
                                         textColor: Color.txtParagraph)

    fileprivate lazy var emailInputTF = {
        let displayModel = AuthInputTextField.DisplayModel
            .initialSetup(title: "key0244".localized(),
                          placeholder: "key0245".localized(),
                          keyboardType: .emailAddress)
        
        return AuthInputTextField(initialDisplay: displayModel)
    }()
    
    private lazy var contactButton = UIButton(type: .system)
    
    fileprivate lazy var sendOTPButton = ThemeableButton(
        type: .system,
        titleFont: Font.medium.of(size: 14),
        title: "key0247".localized(),
        normalTitleColor: Color.txtInverted,
        disabledTitleColor: Color.txtDisabled,
        normalBgColor: Color.btn2nd,
        disabledBgColor: Color.bgButtonDisabled,
        cornerRadius: 22
    )

    // MARK: Properties
    private let disposeBag = DisposeBag()
    
    // MARK: Life cycle
    public override func viewDidLoad() {
        super.viewDidLoad()
        
        setupUI()
        bindActions()
    }
    
    public func setupUI() {
        view.backgroundColor = Color.bgDefault
        view.addSubviews([contentStackView,
                          sendOTPButton])

        contentStackView.snp.makeConstraints { make in
            make.top.equalTo(24)
            make.left.equalTo(12)
            make.right.equalTo(-12)
        }
        
        descLabel1.setLineSpacing(lineHeightMultiple: 1.2)
        descLabel2.setLineSpacing(lineHeightMultiple: 1.2)

        // Config contact button
        let attributes: [NSAttributedString.Key: Any] = [
            .font: Font.medium.of(size: 12),
            .foregroundColor: Color.txtParagraph,
            .underlineStyle: NSUnderlineStyle.single.rawValue
        ]
        
        let attributeString = NSMutableAttributedString(string: "key0246".localized(),
                                                        attributes: attributes)
        contactButton.setAttributedTitle(attributeString, for: .normal)
        
        sendOTPButton.snp.makeConstraints { make in
            make.bottom.equalTo(view.safeAreaLayoutGuide.snp.bottom).offset(-16)
            make.left.equalTo(12)
            make.right.equalTo(-12)
            make.height.equalTo(44)
        }
    }
    
    public func bindActions() {
        emailInputTF.rx.text
            .subscribe(onNext: { [unowned self] text in
                sendOTPButton.isEnabled = text.isValidEmail(softCheck: true)
            })
            .disposed(by: disposeBag)
    }
}

// MARK: - JXSegmentedListContainerViewListDelegate
extension OTPEmailInputViewController: JXSegmentedListContainerViewListDelegate {
    public func listView() -> UIView { view }
}

// MARK: - extension Reactive
public extension Reactive where Base: OTPEmailInputViewController {
    
    var sendOTP: Observable<String> {
        base.sendOTPButton.rx.tap
            .withLatestFrom(base.emailInputTF.rx.text)
            .asObservable()
    }
}
