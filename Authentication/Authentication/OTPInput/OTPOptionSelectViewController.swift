//
//  OTPOptionSelectViewController.swift
//  Authentication
//
//  Created by <PERSON> on 04/03/2024.
//

import UIKit
import Core
import CUIModule
import RxSwift
import SharedData

public final class OTPOptionSelectViewController: BaseViewController, AnyView {
    
    // MARK: UI properties
    private lazy var contentStackView = {
        let stackView = UIStackView(axis: .vertical,
                                    spacing: 16)
        stackView.addArrangedSubviews([titleLabel,
                                       descLabel,
                                       CUIDivider(canvasHeight: 16),
                                       phoneOptionView,
                                       emailOptionView,
                                       contactButtonContainer])
        stackView.setCustomSpacing(0, after: phoneOptionView)
        
        return stackView
    }()
    
    private lazy var titleLabel = UILabel(font: Font.semiBold.of(size: 14),
                                          text: "key0777".localized(),
                                          textColor: Color.txtTitle)
    
    private lazy var descLabel = UILabel(font: Font.light.of(size: 12),
                                         numberOfLines: 0,
                                         text: "key0778".localized(),
                                         textColor: Color.txtParagraph)

    fileprivate lazy var phoneOptionView = OTPOptionView(optionType: .phoneNumber(mobileRegion: mobileRegion,
                                                                                  phoneNumber: phoneNumber))
    fileprivate lazy var emailOptionView = OTPOptionView(optionType: .email(email: emailAddress))
    
    private lazy var contactButtonContainer = {
        let view = UIView()
        view.addSubview(contactButton)
        contactButton.snp.makeConstraints { make in
            make.top.equalTo(8)
            make.bottom.equalTo(-8)
            make.centerX.equalToSuperview()
        }
        
        return view
    }()
    
    private lazy var contactButton = UIButton(type: .system)
    
    // MARK: Properties
    private let mobileRegion: String
    private let phoneNumber: String
    private let emailAddress: String
    
    // MARK: Life cycle
    public init(mobileRegion: String, phoneNumber: String, emailAddress: String) {
        self.mobileRegion = mobileRegion
        self.phoneNumber = phoneNumber
        self.emailAddress = emailAddress
        
        super.init(nibName: nil, bundle: nil)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    public override func viewDidLoad() {
        super.viewDidLoad()
        
        setupUI()
    }
    
    public func setupUI() {
        view.backgroundColor = Color.bgDefault
        view.addSubview(contentStackView)
        
        contentStackView.snp.makeConstraints { make in
            make.top.equalTo(24)
            make.left.equalTo(12)
            make.right.equalTo(-12)
        }
                
        // Config contact button
        let attributes: [NSAttributedString.Key: Any] = [
            .font: Font.light.of(size: 14),
            .foregroundColor: Color.txtInactive,
            .underlineStyle: NSUnderlineStyle.single.rawValue
        ]
        
        let attributeString = NSMutableAttributedString(string: "key0246".localized(),
                                                        attributes: attributes)
        contactButton.setAttributedTitle(attributeString, for: .normal)
    }
    
    public func setInteraction(enable: Bool) {
        phoneOptionView.isUserInteractionEnabled = enable
        emailOptionView.isUserInteractionEnabled = enable
    }
}

extension OTPOptionSelectViewController: JXSegmentedListContainerViewListDelegate {
    
    public func listView() -> UIView { view }
}

// MARK: - extension Reactive
public extension Reactive where Base: OTPOptionSelectViewController {
    
    var sendOTP: Observable<OTPOptionType> {
        Observable.merge(base.phoneOptionView.rx.selectedOption,
                         base.emailOptionView.rx.selectedOption)
    }
}

// MARK: - OTPOptionType
public enum OTPOptionType {
    case phoneNumber(mobileRegion: String, phoneNumber: String)
    case email(email: String)
    
    public var title: String {
        switch self {
        case .phoneNumber:
            return "key0018".localized()
            
        case .email:
            return "key0020".localized()
        }
    }
    
    public var value: String {
        switch self {
        case .phoneNumber(let mobileRegion, let phoneNumber):
            if !mobileRegion.isEmpty {
                return "(\(mobileRegion)) " + phoneNumber.maskPhoneNumber()
            }
            
            return phoneNumber.maskPhoneNumber()
            
        case .email(let email):
            return email.maskEmail()
        }
    }
    
    public var isDefault: Bool {
        switch self {
        case .phoneNumber:
            return true
            
        default:
            return false
        }
    }
    
    public var queryTypeValue: String {
        switch self {
        case .phoneNumber:
            return OTPAuthType.mobile.rawValue
            
        case .email:
            return OTPAuthType.email.rawValue
        }
    }
}
