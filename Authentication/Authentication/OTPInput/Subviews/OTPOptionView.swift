//
//  OTPOptionView.swift
//  Authentication
//
//  Created by <PERSON> on 04/03/2024.
//

import UIKit
import Core
import CUIModule
import RxSwift

final class OTPOptionView: UIView, AnyView {
    
    // MARK: UI properties
    private lazy var contentStackView = {
        let stackView = UIStackView(alignment: .center,
                                    spacing: 4)
        stackView.addArrangedSubviews([titleLabel,
                                       contentLabel,
                                       UIView(),
                                       arrowView])
        
        return stackView
    }()
    
    private lazy var titleLabel = UILabel(font: Font.medium.of(size: 14),
                                          text: optionType.title,
                                          textColor: Color.txtTitle)
    
    private lazy var contentLabel = UILabel(font: Font.regular.of(size: 14),
                                            text: optionType.value,
                                            textColor: Color.txtParagraph)
    
    private lazy var arrowView = UIImageView(image: .image(named: "icon-arrow"))
    
    fileprivate lazy var overlayButton = UIButton()
    
    // MARK: Properties
    fileprivate let optionType: OTPOptionType
        
    // MARK: Life cycle
    init(optionType: OTPOptionType) {
        self.optionType = optionType
        super.init(frame: .zero)
        
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func setupUI() {
        addSubviews([contentStackView,
                    overlayButton])
        
        contentStackView.snp.makeConstraints {
            $0.top.equalTo(12)
            $0.left.equalTo(4)
            $0.bottom.equalTo(-12)
            $0.right.equalTo(-4)
        }
        
        overlayButton.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }
}

// MARK: - extension Reactive
extension Reactive where Base: OTPOptionView {
    
    var selectedOption: Observable<OTPOptionType> {
        base.overlayButton.rx.tap.map { base.optionType }
    }
}
