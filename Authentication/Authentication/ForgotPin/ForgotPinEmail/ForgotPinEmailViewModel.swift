//
//  ForgotPinEmailViewModel.swift
//  Authentication
//
//  Created by <PERSON><PERSON> on 3/7/67.
//
import APILayer
import Core
import CUIModule
import RxCocoa
import RxSwift
import RxRelay
import XCoordinator
import Storage

public class ForgotPinEmailViewModel: AnyViewModel {

    private let router: UnownedRouter<AuthenticationRoute>

    public struct Input {
        let onViewAppear: Observable<Void>
        let onEnterEmail: Observable<String>
    }

    public struct Output {
        let displayData: Driver<Void>
        let enableButton: Driver<Bool>
    }

    // Output to parent
    let sendOtp = PublishSubject<String>()

    init(router: UnownedRouter<AuthenticationRoute>) {
        self.router = router
    }

    public func transform(input: Input) -> Output {

        let displayData = input.onViewAppear

        let enterEmail = input.onEnterEmail
            .map { $0.isValidEmail(softCheck: true) }

        return Output(
            displayData: displayData.asDriverOnErrorNever(),
            enableButton: enterEmail.asDriverOnErrorNever()
        )
    }

    func navigate(to route: AuthenticationRoute) {
        self.router.trigger(route)
    }
}
