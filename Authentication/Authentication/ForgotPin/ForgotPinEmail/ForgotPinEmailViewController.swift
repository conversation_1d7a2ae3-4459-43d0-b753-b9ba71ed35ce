//
//  ForgotPinEmailViewController.swift
//  Authentication
//
//  Created by <PERSON><PERSON><PERSON> on 3/7/67.
//
import CUIModule
import UIKit
import SnapKit
import RxSwift
import Core
import Storage

final class ForgotPinEmailViewController: BaseViewController, VMView {

    private lazy var stackView = UIStackView(
        axis: .vertical,
        spacing: 24
    )

    private lazy var titleLabel = UILabel(
        font: Font.medium.of(size: 14),
        text: "key0242".localized(),
        textColor: Color.txtTitle
    )

    private lazy var descLabel = UILabel(
        font: Font.light.of(size: 14),
        numberOfLines: 0,
        text: "key0243".localized(),
        textColor: Color.txtParagraph
    )

    private lazy var emailTextField = CUITextFieldWithTitle(
        title: "key0244".localized(),
        titleTextColor: Color.txtTitle,
        titleErrorColor: Color.txtNegative,
        titleFont: Font.medium.of(size: 14),
        textFieldConfig: CUITextField.Config(
            placeholder: "key0245".localized(),
            inputType: .text(.emailAddress)
        )
    )

    private var contactButton: UIButton = {
        let button = UIButton(type: .system, backgroundColor: .clear)
        let attributedString = NSMutableAttributedString(
            string: "key0246".localized(),
            attributes: [
                .font: Font.regular.of(size: 14),
                .foregroundColor: Color.txtParagraph,
                .underlineStyle: NSUnderlineStyle.single.rawValue
            ]
        )

        button.setAttributedTitle(attributedString, for: .normal)
        return button
    }()

    fileprivate lazy var sendOtpButton = ThemeableButton(
        titleFont: Font.medium.of(size: 14),
        title: "key0247".localized(),
        normalTitleColor: Color.txtInverted,
        disabledTitleColor: Color.txtDisabled,
        normalBgColor: Color.btn2nd,
        disabledBgColor: Color.bgButtonDisabled,
        cornerRadius: 22,
        isEnable: false
    )

    var viewModel: ForgotPinEmailViewModel!
    let parentView: UIView
    let navigationView: UIView?

    init(
        parentView: UIView,
                navigationView: UIView?
    ) {
        self.parentView = parentView
        self.navigationView = navigationView
        super.init(nibName: nil, bundle: nil)
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        bindActions()
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        navigationBarHidden(true)
    }

    func setupUI() {
        view.addSubviews([stackView, sendOtpButton])
        stackView.snp.makeConstraints { make in
            make.top.equalTo(24)
            make.left.equalTo(12)
            make.right.equalTo(-12)
        }

        stackView.addArrangedSubviews([
            titleLabel, descLabel, CUIDivider(),
            emailTextField, contactButton
        ])
        stackView.setCustomSpacing(16, after: titleLabel)

        contactButton.snp.makeConstraints { make in
            make.height.equalTo(16)
        }

        sendOtpButton.snp.makeConstraints { make in
            make.left.equalTo(12)
            make.right.equalTo(-12)
            make.bottom.equalTo(view.safeAreaLayoutGuide.snp.bottom).offset(-16)
            make.height.equalTo(44)
        }
    }

    func bind(viewModel: ForgotPinEmailViewModel) {
        self.viewModel = viewModel

        let input = ForgotPinEmailViewModel.Input(
            onViewAppear: rx.viewWillAppear.mapToVoid(), 
            onEnterEmail: emailTextField.rx.text
        )

        let output = viewModel.transform(input: input)

        output.displayData.drive(onNext: { [weak self] in
            self?.displayUI()
        }).disposed(by: disposeBag)

        output.enableButton.drive(onNext: { [weak self] in
            self?.sendOtpButton.isEnabled = $0
        }).disposed(by: disposeBag)
    }

    func bindActions() {
        sendOtpButton.rx.tap
            .compactMap { self.emailTextField.text }
            .subscribe(onNext: {
                self.viewModel.sendOtp.onNext($0)
            })
            .disposed(by: disposeBag)
    }

    private func displayUI() {

    }
}
