//
//  ForgotPinViewModel.swift
//  Authentication
//
//  Created by <PERSON><PERSON><PERSON> on 3/7/67.
//
import APILayer
import Core
import RxCocoa
import RxSwift
import RxRelay
import XCoordinator
import SharedData
import Storage
import CUIModule
import <PERSON><PERSON>harts
import LocalAuthentication

public class ForgotPinViewModel: AnyViewModel {
    let router: UnownedRouter<AuthenticationRoute>

    public struct Input {
        let onViewAppear: Observable<Void>
        let forwardStep: Observable<Void>
        let backwardStep: Observable<Void>
        let requestOTP: Observable<String>
        let resendOTP: Observable<Void>
        let submitOtp: Observable<String>
    }

    public struct Output {
        let loading: Driver<Bool>
        let otpError: Driver<Error>
        let displayData: Driver<ForgotPinStep>
        let changeStep: Driver<(
            state: StepState,
            step: ForgotPinStep,
            direction: UIPageViewController.NavigationDirection
        )>
        let otpRequested: Driver<(
            email: String,
            request: OTPRequestEndPoint.Response,
            isResend: Bool
        )>
        let otpSubmitted: Driver<String>
    }

    internal let steps = ForgotPinStep.allCases
    private(set) var currentStep = ForgotPinStep.emailInput
    let pinCount = 6

    private var userEmail: String = ""
    private var refCode: String = ""
    private var token: String = ""

    init(router: UnownedRouter<AuthenticationRoute>) {
        self.router = router
    }

    public func transform(input: Input) -> Output {

        let activityIndicator = ActivityIndicator()
        let errorTracker = ErrorTracker()
        
        let displayData = input.onViewAppear
            .map { self.currentStep }

        let forwardStep = input.forwardStep
            .map { self.forwardStep() }
            .map {
                (
                    state: $0,
                    step: self.currentStep,
                    direction: UIPageViewController.NavigationDirection.forward
                )
            }

        let backwardStep = input.backwardStep
            .map { self.backwardStep() }
            .map {
                (
                    state: $0,
                    step: self.currentStep,
                    direction: UIPageViewController.NavigationDirection.reverse
                )
            }

        let changeStep = Observable.merge(forwardStep, backwardStep)
        
        let otpRequest = input.requestOTP
            .onNext { self.userEmail = $0 }
            .map { (email: $0, isResend: false) }
            .share()

        let otpResend = input.resendOTP
            .map { (email: self.userEmail, isResend: true) }
            .share()


        let sendOTPRequest = Observable.merge(otpRequest, otpResend)
            .flatMap { data in
                self.requestOTP(data.email)
                    .track(activityIndicator, error: errorTracker)
                    .map { (email: self.userEmail, request: $0, isResend: data.isResend) }
            }
            .onNext { self.refCode = $0.request.refCode ?? "" }

        let submitOtp = input.submitOtp
            .flatMap {
                self.submitOTP($0)
                    .track(activityIndicator, error: errorTracker)
            }
            .compactMap {  $0.token }
            .onNext { self.token = $0 }

        return Output(
            loading: activityIndicator.asDriver(),
            otpError: errorTracker.asDriver(),
            displayData: displayData.asDriverOnErrorNever(),
            changeStep: changeStep.asDriverOnErrorNever(),
            otpRequested: sendOTPRequest.asDriverOnErrorNever(),
            otpSubmitted: submitOtp.asDriverOnErrorNever()
        )
    }

    func forwardStep() -> StepState {
        if currentStep == steps.last { return .complete }
        currentStep = ForgotPinStep(rawValue: currentStep.rawValue + 1) ?? currentStep
        return .onGoing
    }

    func backwardStep()-> StepState {
        if currentStep == steps.first { return .quit }
        currentStep = ForgotPinStep(rawValue: currentStep.rawValue - 1) ?? currentStep
        return .onGoing
    }

    func navigate(to route: AuthenticationRoute) {
        self.router.trigger(route)
    }
}
// MARK: - Api call
extension ForgotPinViewModel {

    func requestOTP(_ address: String) -> Observable<OTPRequestEndPoint.Response> {
        OTPRequestEndPoint.service.call(
            parameter: OTPRequestEndPoint.Request(
                bizType: BizType.forgotPasswordPin.rawValue,
                otpType: OTPAuthType.email.rawValue,
                otpAddress: address
            )
        )
    }

    func submitOTP(_ otpCode: String) -> Observable<UserAuthResetVerificationEndPoint.Response> {
        UserAuthResetVerificationEndPoint.service.call(
            parameter: UserAuthResetVerificationEndPoint.Request(
                username: userEmail,
                type: "FORGET_PIN",
                otpType: OTPAuthType.email.rawValue,
                otp: otpCode,
                refCode: refCode
            )
        )
    }
}
// MARK: - Display Model
public extension ForgotPinViewModel {

    enum StepState {
        case quit
        case onGoing
        case complete
    }

    enum ForgotPinStep: Int, CaseIterable {
        case emailInput
        case otpVerify
        case createPin
        case confirmPin

        var title: String { return "key0241".localized() }
    }
}
