//
//  ForgotPinViewController.swift
//  Authentication
//
//  Created by <PERSON><PERSON><PERSON> on 3/7/67.
//
import Core
import CUIModule
import RxSwift
import RxCocoa
import Utils
import SharedData
import Storage
import APILayer

final class ForgotPinViewController: BaseViewController, VMView {

    private lazy var navView = AuthNavigationView(title: viewModel.currentStep.title)

    private lazy var stepView = CUIStepIndicatorView(totalSteps: self.viewModel.steps.count)

    private lazy var pageContainerView = UIView(backgroundColor: .clear)

    private lazy var pageController: UIPageViewController = {
        let pageController = UIPageViewController(
            transitionStyle: .scroll,
            navigationOrientation: .horizontal,
            options: nil
        )
        pageController.view.backgroundColor = .clear
        return pageController
    }()

    var viewModel: ForgotPinViewModel!

    private var pages: [UIViewController] = []

    private lazy var emailViewModel = ForgotPinEmailViewModel(
        router: viewModel.router
    )
    private lazy var otpViewController = OTPInputViewController(
        sourceType: .email,
        parentView: self.view,
        navigationView: navView
    )
    private lazy var createPinViewModel = CreatePinViewModel(
        router: viewModel.router,
        pinCount: viewModel.pinCount
    )
    private lazy var confirmPinViewModel = ConfirmPinViewModel(
        router: viewModel.router,
        pinCount: viewModel.pinCount,
        type: .forgot
    )

    private let requestOTP = PublishSubject<String>()
    private let resendOTP = PublishSubject<Void>()
    private let submitOTP = PublishSubject<String>()

    private let nextStep = PublishSubject<Void>()
    private let previousStep = PublishSubject<Void>()

    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        bindActions()
        setupPageController()
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        navigationBarHidden(true)
    }

    func setupUI() {
        view.backgroundColor = Color.bgDefault

        view.addSubviews([navView, stepView, pageContainerView])
        navView.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
        }

        stepView.snp.makeConstraints { make in
            make.top.equalTo(navView.snp.bottom).offset(24)
            make.left.right.equalToSuperview()
            make.height.equalTo(27)
        }

        pageContainerView.snp.makeConstraints { make in
            make.top.equalTo(stepView.snp.bottom)
            make.bottom.left.right.equalToSuperview()
        }
    }
    
    func bindActions() {
        
    }
    
    func bind(viewModel: ForgotPinViewModel) {
        self.viewModel = viewModel
        
        let input = ForgotPinViewModel.Input(onViewAppear: rx.viewWillAppear.mapToVoid(),
                                             forwardStep: nextStep,
                                             backwardStep: Observable.merge(previousStep, navView.rx.back),
                                             requestOTP: requestOTP,
                                             resendOTP: resendOTP,
                                             submitOtp: submitOTP)
        
        let output = viewModel.transform(input: input)
        
        output.loading.drive(onNext: { [weak self] in
            if $0 {
                self?.showLoading()
            } else {
                self?.hiddenLoading()
            }
        }).disposed(by: disposeBag)
        
        output.otpError.drive(onNext: { [unowned self] error in
            otpViewController.setError(true)
            CUIToastView.show(
                data: CUIToastView.DisplayModel(
                    title: "key0825".localized(),
                    desc: (error as? APIError)?.message ?? "",
                    titleStyle: CUIToastView.TextStyle(textColor: Color.txtNegative)
                ),
                inView: view,
                underView: navView
            )
        }).disposed(by: disposeBag)
        
        output.displayData.drive(onNext: { [weak self] in
            self?.displayUI(step: $0)
        }).disposed(by: disposeBag)

        output.otpSubmitted.drive(onNext: { [weak self] in
            self?.confirmPinViewModel.updateToken.onNext($0)
            self?.nextStep.onNext(())
        }).disposed(by: disposeBag)

        output.changeStep.drive(onNext: { [weak self] in
            switch $0.state {
            case .quit:
                self?.navigationController?.popViewController(animated: true)
            case .onGoing:
                self?.displayUI(step: $0.step)
                self?.setPage(index: $0.step.rawValue, direction: $0.direction, animated: true)
            case .complete:
                self?.viewModel.navigate(to: .pinSetupCompleted(
                    type: .pinOnly,
                    actionTitle: "key0248".localized(),
                    showNew: true
                ))
            }
        }).disposed(by: disposeBag)
        
        output.otpRequested.drive(onNext: { [weak self] in
            guard let self = self else { return }
            CUIToastView.show(data: .init(title: "key0111".localized(),
                                          desc: "key0112".localized(),
                                          titleStyle: .init(textColor: Color.txtTitle)),
                              inView: self.view,
                              underView: self.navView)
            
            self.otpViewController.otpRequested.onNext((address: $0.email,
                                                        mobileRegion: nil,
                                                        response: $0.request))
            
            if !$0.isResend { self.nextStep.onNext(()) }
        }).disposed(by: disposeBag)
    }

    func bindChildViewModels() {

        emailViewModel.sendOtp.subscribe(onNext: { [weak self] in
            self?.requestOTP.onNext($0)
        }).disposed(by: disposeBag)

        otpViewController.rx.resendOTP
            .bind(to: resendOTP)
            .disposed(by: disposeBag)

        otpViewController.rx.submitOtp
            .bind(to: submitOTP)
            .disposed(by: disposeBag)

        createPinViewModel.pinFilled.subscribe(onNext: { [weak self] in
            self?.confirmPinViewModel.updatePinCode.onNext($0)
            self?.nextStep.onNext(())
        }).disposed(by: disposeBag)

        confirmPinViewModel.showToast.subscribe(onNext: { [weak self] in
            guard let self = self else { return }
            CUIToastView.show(data: $0, inView: self.view, underView: self.navView)
        }).disposed(by: disposeBag)

        confirmPinViewModel.pinSuccess.subscribe(onNext: { [weak self] in
            self?.nextStep.onNext(())
        }).disposed(by: disposeBag)

        confirmPinViewModel.pinFailExceed.subscribe(onNext: { [weak self] in
            guard let self = self else { return }
            self.previousStep.onNext(())
            CUIToastView.show(
                data: CUIToastView.DisplayModel(
                    title: "key0942".localized(),
                    desc: "key0943".localized(),
                    titleStyle: CUIToastView.TextStyle(
                        textColor: Color.txtNegative,
                        font: Font.semiBold.of(size: 12)
                    )
                ),
                inView: self.view,
                underView: self.navView
            )
        }).disposed(by: disposeBag)
    }
}
// MARK: - Update UI
extension ForgotPinViewController {

    private func displayUI(
        step: ForgotPinViewModel.ForgotPinStep
    ) {
        navView.setTitle(step.title)
        stepView.updateCurrentStep(step.rawValue + 1)
    }
}
// MARK: - Page View Controller
extension ForgotPinViewController {
    func setupPageController() {
        self.addChild(pageController)
        pageContainerView.addSubview(pageController.view)
        pageController.view.frame = pageContainerView.bounds
        pageController.view.translatesAutoresizingMaskIntoConstraints = false
        pageController.didMove(toParent: self)
        setUpChildViewControllers()
    }

    func setUpChildViewControllers() {
        // If pages are already setup, return
        guard pages.isEmpty else { return }

        self.viewModel.steps.forEach {
            switch $0 {
            case .emailInput:
                let viewController = ForgotPinEmailViewController(
                    parentView: self.view,
                    navigationView: navView
                )
                viewController.bind(viewModel: emailViewModel)
                pages.append(viewController)

            case .otpVerify:
                pages.append(otpViewController)

            case .createPin:
                let createPinViewController = CreatePinViewController()
                createPinViewController.bind(viewModel: createPinViewModel)
                pages.append(createPinViewController)

            case .confirmPin:
                let confirmPinViewController = ConfirmPinViewController()
                confirmPinViewController.bind(viewModel: confirmPinViewModel)
                pages.append(confirmPinViewController)
            }
        }
        setPage(index: viewModel.currentStep.rawValue, direction: .forward, animated: false)
        bindChildViewModels()
    }

    func setPage(
        index: Int,
        direction: UIPageViewController.NavigationDirection,
        animated: Bool
    ) {
        guard index < pages.count, index >= 0 else { return }
        pageController.setViewControllers(
            [pages[index]],
            direction: direction,
            animated: animated
        )
    }
}
