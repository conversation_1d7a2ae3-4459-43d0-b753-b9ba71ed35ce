//
//  AuthenticationCoordinator.swift
//  Authentication
//
//  Created by <PERSON><PERSON><PERSON> on 1/8/67.
//
import Core
import CUIModule
import UIKit
import XCoordinator
import Storage
import IQKeyboardManagerSwift
import RxSwift
import SharedData
import APILayer

public enum AuthenticationRoute: Route {
    case otp(type: OTPScreenType,
             otpResponse: OTPRequestEndPoint.Response?,
             completion: ((_ type: OTPScreenType) -> Void)?)
    
    case twoFALanding(isSetUp: Bool,
                      completion: ((_ type: OTPAuthType?) -> Void)?,
                      successActionTitle: String)
    
    case twoFAEnabled(type: OTPAuthType,
                      actionTitle: String)
    
    case twoFASetupCompleted(type: OTPAuthType?)
    
    case pinLanding(completion: ((_ pin: Bool, _ bio: Bool) -> Void)?)
    case pinSetup(completion: ((_ pin: Bool, _ bio: Bool) -> Void)?)
    case pinSetupCompleted(type: PinSet<PERSON>CompleteViewModel.SetUpType,
                           actionTitle: String,
                           showNew: Bool)
    case biomerticSetup(completion: ((_ pin: Bool, _ bio: Bool) -> Void)?)
    
    case pinCompleted(_ pin: Bool, _ bio: Bool)
    case pinFailExceed
    
    case pinLogin(doableBiometric: Bool,
                  doableForgetPin: Bool,
                  viewCreated: ((_ viewController: PinLoginViewController) -> Void)?,
                  success: (() -> Void),
                  failExceed: (() -> Void),
                  pinUpdated: ((_ pin: Bool, _ bio: Bool) -> Void)?)
    
    case pinLoginSuccess
    
    case forgotPin
    
    case changePin(completion: ((_ pin: Bool, _ bio: Bool) -> Void)?)
}

public class AuthenticationCoordinator: NavigationCoordinator<AuthenticationRoute> {
    
    private var twoFACompleted: ((_ type: OTPAuthType?) -> Void)?
    private var pinCompleted: ((_ pin: Bool, _ bio: Bool) -> Void)?
    private var pinFailExcced: (() -> Void)?
    private var pinLoginSuccess: (() -> Void)?
    
    public init(root: UINavigationController?, initRoute: AuthenticationRoute? = nil) {
        if let root = root {
            super.init(rootViewController: root, initialRoute: initRoute)
        } else {
            super.init(initialRoute: initRoute)
        }
        
        IQKeyboardManager.shared.disabledDistanceHandlingClasses.append(OTPSMSViewController.self)
        IQKeyboardManager.shared.previousNextDisplayMode = .alwaysHide
    }
    
    public override func prepareTransition(for route: AuthenticationRoute) -> NavigationTransition {
        switch route {
        case let .otp(type, otpResponse, completion):
            let viewController = OTPSMSViewController()
            let viewmodel = OTPSMSViewModel(router: unownedRouter,
                                            otpType: type, 
                                            initialOtpResponse: otpResponse)
            viewmodel.onCompleted.subscribe(onNext: {
                completion?($0)
            }).disposed(by: viewController.disposeBag)
            
            viewController.bind(viewModel: viewmodel)
            viewController.hidesBottomBarWhenPushed = true
            
            return .push(viewController)
            
        case .twoFALanding(let isSetUp, let completion, let succesActionTitle):
            self.twoFACompleted = completion
            let viewController = TwoFALandingViewController(successActionTitle: succesActionTitle)
            let viewModel = TwoFALandingViewModel(router: unownedRouter,
                                                  isSetUp: isSetUp)
            viewController.bind(viewModel: viewModel)
            
            return .push(viewController)
            
        case .twoFAEnabled(let type, let actionTitle):
            let viewController = TwoFAEnabledViewController(actionTitle: actionTitle)
            let viewModel = TwoFAEnabledViewModel(router: self.unownedRouter, type: type)
            viewController.bind(viewModel: viewModel)
            return .push(viewController)

        case .twoFASetupCompleted(let type):
            twoFACompleted?(type)
            return .none()

        case .pinLanding(let completion):
            self.pinCompleted = completion
            let viewController = PinLandingViewController()
            let viewModel = PinLandingViewModel(router: self.unownedRouter)
            viewController.bind(viewModel: viewModel)
            return .push(viewController)

        case .pinSetup(let completion):
            if let completion = completion {
                self.pinCompleted = completion
            }
            let viewController = PinSetUpViewController()
            let viewModel = PinSetUpViewModel(router: self.unownedRouter)
            viewController.bind(viewModel: viewModel)
            return .push(viewController)

        case .pinSetupCompleted(let type, let actionTitle, let showNew):
            let viewController = PinSetupCompleteViewController()
            let viewModel = PinSetupCompleteViewModel(
                router: self.unownedRouter, type: type,
                actionTitle: actionTitle, showNew: showNew
            )
            viewController.bind(viewModel: viewModel)
            return .push(viewController)

        case .biomerticSetup(let completion):
            self.pinCompleted = completion
            let viewController = BiometricSettingViewController()
            let viewModel = BiometricSettingViewModel(router: self.unownedRouter)
            viewController.bind(viewModel: viewModel)
            
            return .push(viewController)

        case .pinCompleted(let pin, let bio):
            pinCompleted?(pin, bio)
            return .none()

        case .pinFailExceed:
            pinFailExcced?()
            return .none()
            
        case .pinLoginSuccess:
            pinLoginSuccess?()
            return .none()
            
        case .pinLogin(let doableBiometric, let doableForgetPin,
                       let viewCreated, let success,
                       let failExceed, let updated):
            let viewController = PinLoginViewController(doableBiometric: doableBiometric,
                                                        doableForgetPin: doableForgetPin)
            let viewModel = PinLoginViewModel(router: self.unownedRouter)
            viewController.bind(viewModel: viewModel)
            
            self.pinCompleted = updated
            self.pinFailExcced = failExceed
            self.pinLoginSuccess = success
            
            if let viewCreated = viewCreated {
                viewCreated(viewController)
                return .none()
            }
            return .push(viewController)
            
        case .forgotPin:
            let viewController = ForgotPinViewController()
            let viewModel = ForgotPinViewModel(router: self.unownedRouter)
            viewController.bind(viewModel: viewModel)
            return .push(viewController)

        case .changePin(let completion):
            self.pinCompleted = completion
            let viewController = ChangePinViewController()
            let viewModel = ChangePinViewModel(router: unownedRouter)
            viewController.bind(viewModel: viewModel)
            
            return .push(viewController)
        }
    }
}
