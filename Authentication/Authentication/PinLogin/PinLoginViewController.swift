//
//  PinLoginViewController.swift
//  Login
//
//  Created by <PERSON><PERSON><PERSON> on 3/6/67.
//
import CUIModule
import UIKit
import SnapKit
import RxSwift
import Core
import Storage
import Utils
import APILayer

public class PinLoginViewController: BaseViewController, VMView {
    
    private lazy var navView = CUINavigationBar(displayModel: .init(leftIcon: .image(named: "merit_ic_back"),
                                                                    titleView: titleLabel))
    
    private lazy var titleLabel = UILabel(font: Font.semiBold.of(size: 14),
                                          text: "key0235".localized(),
                                          textColor: Color.txtTitle)
    
    private lazy var pinView = PinView(
        title: "key0236".localized(),
        caption: "key0237".localized(arguments: viewModel.pinCount),
        pinCount: viewModel.pinCount,
        showBiometric: doableBiometric && LocalPreference.biometricEnable ?? false
    )
    
    private var forgotButton = {
        let button = UIButton(type: .system, backgroundColor: .clear)
        let attributedString = NSMutableAttributedString(
            string: "key0238".localized(),
            attributes: [.font: Font.medium.of(size: 12),
                         .foregroundColor: Color.txtInactive,
                         .underlineStyle: NSUnderlineStyle.single.rawValue]
        )
        
        button.setAttributedTitle(attributedString, for: .normal)
        
        return button
    }()
    
    public var viewModel: PinLoginViewModel!
    
    var biometricSuccess = PublishSubject<Void>()
    
    private let doableBiometric: Bool
    private let doableForgetPin: Bool
    
    public init(doableBiometric: Bool,
                doableForgetPin: Bool) {
        self.doableBiometric = doableBiometric
        self.doableForgetPin = doableForgetPin
        
        super.init(nibName: nil, bundle: nil)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    public override func viewDidLoad() {
        super.viewDidLoad()
        
        setupUI()
        bindActions()
    }

    public override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)

        if doableBiometric && LocalPreference.biometricEnable ?? false {
            loginWithBio()
        }
    }

    public func setupUI() {
        view.addSubviews([navView,
                          pinView,
                          forgotButton])
        forgotButton.isHidden = !doableForgetPin

        navView.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide.snp.top)
            make.left.right.equalToSuperview()
        }

        pinView.snp.makeConstraints { make in
            make.top.equalTo(navView.snp.bottom).offset(UIScreen.main.bounds.height*0.08)
            make.bottom.equalTo(forgotButton.snp.top).offset(-16)
            make.left.equalTo(12)
            make.right.equalTo(-12)
        }

        forgotButton.snp.makeConstraints { make in
            make.height.equalTo(48)
            make.bottom.equalTo(view.safeAreaLayoutGuide.snp.bottom).offset(-16)
            make.left.equalTo(12)
            make.right.equalTo(-12)
        }
    }
    
    public func bind(viewModel: PinLoginViewModel) {
        self.viewModel = viewModel
        
        let input = PinLoginViewModel.Input(onViewAppear: rx.viewWillAppear.mapToVoid(),
                                            onPinFilled: pinView.rx.pinFilled,
                                            biometric: biometricSuccess)
        
        let output = viewModel.transform(input: input)
        
        output.loading.drive(onNext: { [weak self] loading in
            loading ? self?.showLoading() : self?.hiddenLoading()
        }).disposed(by: disposeBag)

        output.displayData.drive(onNext: { [weak self] in
            self?.displayUI()
        }).disposed(by: disposeBag)

        output.error.drive(onNext: { [weak self]  in
            guard let self = self else { return }

            guard ($0 as? APIError)?.code != "TXCU000201" else {
                self.viewModel.navigate(to: .pinFailExceed)
                return
            }

            self.pinView.reset()
            CUIToastView.show(
                data: CUIToastView.DisplayModel(
                    title: "key0239".localized(),
                    desc: ($0 as? APIError)?.message ?? $0.localizedDescription,
                    titleStyle: CUIToastView.TextStyle(
                        textColor: Color.txtNegative
                    )
                ),
                inView: self.view,
                underView: self.navView
            )
        }).disposed(by: disposeBag)

        output.pinSuccess.drive(onNext: { [weak self] in
            self?.viewModel.navigate(to: .pinLoginSuccess)
        }).disposed(by: disposeBag)
    }

    public func bindActions() {
        navView.rx.leftTap.subscribe(onNext: { [weak self] in
            self?.navigationController?.popViewController(animated: true)
        }).disposed(by: disposeBag)

        pinView.rx.biometricTap.subscribe(onNext: { [weak self] in
            self?.loginWithBio()
        }).disposed(by: disposeBag)

        forgotButton.rx.tap.subscribe(onNext: { [weak self] in
            self?.viewModel.navigate(to: .forgotPin)
        }).disposed(by: disposeBag)
    }

    private func loginWithBio() {
        LAPolicyUtil.evaluatePolicy(
            reason: "key0946".localized(),
            result: { [weak self] in
                switch $0 {
                case .faceID, .touchID:
                    self?.biometricSuccess.onNext(())
                default:
                    break
                }
            }
        )
    }

    private func displayUI() {
        pinView.reset()
    }
}
