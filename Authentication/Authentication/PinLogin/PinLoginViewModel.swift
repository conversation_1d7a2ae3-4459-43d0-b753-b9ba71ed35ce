//
//  PinLoginViewModel.swift
//  Login
//
//  Created by <PERSON><PERSON><PERSON> on 3/6/67.
//
import APILayer
import Core
import CUIModule
import RxCocoa
import RxSwift
import RxRelay
import XCoordinator
import Storage

public class PinLoginViewModel: AnyViewModel {

    private let router: UnownedRouter<AuthenticationRoute>

    public struct Input {
        let onViewAppear: Observable<Void>
        let onPinFilled: Observable<String>
        let biometric: Observable<Void>
    }

    public struct Output {
        let loading: Driver<Bool>
        let error: Driver<Error>
        let displayData: Driver<Void>
        let pinSuccess: Driver<Void>
    }

    let pinCount: Int = 6

    init(router: UnownedRouter<AuthenticationRoute>) {
        self.router = router
    }

    public func transform(input: Input) -> Output {
        let activityIndicator = ActivityIndicator()
        let errorTracker = ErrorTracker()
        
        let displayData = input.onViewAppear

        let biometric = input.biometric
            .map { Keychain.userPin ?? "" }
        
        var tempPin = ""
        
        let login = Observable.merge(input.onPinFilled, biometric)
            .do(onNext: { tempPin = $0 })
            .flatMap {
                self.loginWithPin($0)
                    .track(activityIndicator, error: errorTracker)
            }
            .onNext {
                LocalPreference.sessionId = $0.sessionId
                LocalPreference.configuredPin = $0.configuredPin
                Keychain.userPin = tempPin
            }
            .mapToVoid()
            .flatMap {
                self.fetchUserInformation()
                    .track(activityIndicator, error: errorTracker)
            }
            .onNext { Keychain.userInformation = $0 }
            .mapToVoid()
            .flatMap {
                QueryUserProfileEndPoint.service.call()
                    .track(activityIndicator, error: errorTracker)
            }
            .onNext { Keychain.userProfile = $0 }
            .mapToVoid()

        return Output(
            loading: activityIndicator.asDriver(),
            error: errorTracker.asDriver(),
            displayData: displayData.asDriverOnErrorNever(),
            pinSuccess: login.asDriverOnErrorNever()
        )
    }

    func navigate(to route: AuthenticationRoute) {
        self.router.trigger(route)
    }
}
// MARK: - Api calls
private extension PinLoginViewModel {

    func loginWithPin(_ pin: String) -> Observable<UserLoginEndPoint.Response> {
        UserLoginEndPoint.service.request(parameters: UserLoginEndPoint.Request(
            username: Keychain.userName ?? "",
            deviceId: LocalPreference.deviceId ?? "",
            password: nil, pin: pin,
            otp: nil, token: nil, otpType: nil, refCode: nil
        ))
    }

    func fetchUserInformation() -> Observable<UserInformationEndPoint.Response> {
        UserInformationEndPoint.service.call()
    }
}
