//
//  ChangePinViewController.swift
//  Authentication
//
//  Created by <PERSON><PERSON><PERSON> on 3/11/67.
////
import Core
import CUIModule
import RxSwift
import RxCocoa
import Utils
import SharedData
import Storage

final class ChangePinViewController: BaseViewController, VMView {

    // MARK: UI properties
    private lazy var headerView = {
        let displayModel = CUINavigationBar.DisplayModel(leftIcon: .image(named: "merit_ic_back"),
                                                         titleView: titleStackView)
        return CUINavigationBar(displayModel: displayModel)
    }()
    
    private lazy var titleStackView = {
        let stackView = UIStackView(alignment: .center,
                                    spacing: 4)
        stackView.addArrangedSubviews([titleImgView,
                                       titleLabel])
        
        return stackView
    }()
    
    private lazy var titleImgView = UIImageView(image: .image(named: "ic_pin"))
    
    private lazy var titleLabel = UILabel(font: Font.semiBold.of(size: 14),
                                          text: "key0767".localized(),
                                          textColor: Color.txtTitle)

    private lazy var stepView = CUIStepIndicatorView(totalSteps: viewModel.steps.count)
    
    private lazy var pageContainerView = UIView(backgroundColor: .clear)
    
    private lazy var pageController = {
        let pageController = UIPageViewController(transitionStyle: .scroll,
                                                  navigationOrientation: .horizontal,
                                                  options: nil)
        pageController.view.backgroundColor = .clear
        
        return pageController
    }()
    
    // MARK: Properties
    var viewModel: ChangePinViewModel!
    private var pages: [UIViewController] = []
    
    private lazy var otpOptionViewController = {
        let mobileRegion = Keychain.userInformation?.basic?.base?.mobileRegion ?? ""
        let phoneNumber = Keychain.userInformation?.basic?.base?.phoneNumber ?? ""
        let email = Keychain.userInformation?.basic?.base?.email ?? ""
        
        return OTPOptionSelectViewController(mobileRegion: mobileRegion,
                                             phoneNumber: phoneNumber,
                                             emailAddress: email)
    }()
    
    private lazy var otpInputViewController = OTPInputViewController(sourceType: .phone,
                                                                     parentView: view,
                                                                     navigationView: headerView)
    
    private lazy var createPinViewModel = CreatePinViewModel(router: viewModel.router,
                                                             pinCount: viewModel.pinCount)
    
    private lazy var confirmPinViewModel = ConfirmPinViewModel(router: viewModel.router,
                                                               pinCount: viewModel.pinCount,
                                                               type: .change)

    private let nextStep = PublishSubject<Void>()
    private let previousStep = PublishSubject<Void>()
    private let onOtpOption = PublishSubject<(type: String,
                                              address: String,
                                              mobileRegion: String?)>()

    override func viewDidLoad() {
        super.viewDidLoad()
        
        setupUI()
        setupPageController()
        bindViewModelLogic()
    }

    func setupUI() {
        view.backgroundColor = Color.bgDefault

        view.addSubviews([headerView, stepView, pageContainerView])
        headerView.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide)
            make.left.right.equalToSuperview()
        }

        stepView.snp.makeConstraints { make in
            make.top.equalTo(headerView.snp.bottom).offset(12)
            make.left.right.equalToSuperview()
            make.height.equalTo(30)
        }

        pageContainerView.snp.makeConstraints { make in
            make.top.equalTo(stepView.snp.bottom)
            make.bottom.left.right.equalToSuperview()
        }
    }
    
    func bindViewModelLogic() {
        let input = ChangePinViewModel.Input(onViewAppear: rx.viewWillAppear.mapToVoid(),
                                             forwardStep: nextStep,
                                             backwardStep: Observable.merge(previousStep, headerView.rx.leftTap),
                                             otpOption: onOtpOption.asObservable(),
                                             resendOTP: otpInputViewController.rx.resendOTP,
                                             submitOtp: otpInputViewController.rx.submitOtp)

        let output = viewModel.transform(input: input)

        output.loading.drive(onNext: { [weak self] in
            if $0 {
                self?.showLoading()
            } else {
                self?.hiddenLoading()
            }
        }).disposed(by: disposeBag)
        
        output.otpError.drive(onNext: { [weak self] _ in
            guard let self = self else { return }
            self.otpInputViewController.setError(true)
            
            CUIToastView.show(data: .init(title: "key0895".localized(),
                                          desc: "key0826".localized(),
                                          titleStyle: CUIToastView.TextStyle(textColor: Color.txtNegative)),
                              inView: self.view,
                              underView: self.headerView)
        }).disposed(by: disposeBag)

        output.displayData.drive(onNext: { [weak self] in
            self?.displayUI(step: $0)
        }).disposed(by: disposeBag)

        output.changeStep.drive(onNext: { [weak self] in
            switch $0.state {
            case .quit:
                self?.navigationController?.popViewController(animated: true)
                
            case .onGoing:
                self?.displayUI(step: $0.step)
                self?.setPage(index: $0.step.rawValue, direction: $0.direction, animated: true)
                
            case .complete:
                self?.viewModel.navigate(to: .pinCompleted(true, false))
            }
        }).disposed(by: disposeBag)
        
        output.otpRequested
            .drive(onNext: { [unowned self] address, mobileRegion, response, isResend in
                CUIToastView.show(data: .init(title: "key0111".localized(),
                                              desc: otpInputViewController.currentSourceType().toastDesc,
                                              titleStyle: .init(textColor: Color.txtTitle)),
                                  inView: view,
                                  underView: headerView)
                otpInputViewController.otpRequested.onNext((address: address,
                                                            mobileRegion: mobileRegion,
                                                            response: response))
                
                if !isResend {
                    nextStep.onNext(())
                    otpOptionViewController.setInteraction(enable: true)
                }
            }).disposed(by: disposeBag)
        
        output.otpSubmitted.drive(onNext: { [unowned self] in
            confirmPinViewModel.updateToken.onNext($0)
            nextStep.onNext(())
        }).disposed(by: disposeBag)
    }

    private func bindChildViewModels() {
        otpOptionViewController.rx.sendOTP.subscribe(onNext: { [unowned self] in
            otpOptionViewController.setInteraction(enable: false)
            
            switch $0 {
            case .phoneNumber(let mobileRegion, let phoneNumber):
                otpInputViewController.updateSourceType(sourceType: .phone)
                onOtpOption.onNext((type: $0.queryTypeValue,
                                    address: phoneNumber,
                                    mobileRegion: mobileRegion))
                
            case .email(let email):
                otpInputViewController.updateSourceType(sourceType: .email)
                onOtpOption.onNext((type: $0.queryTypeValue, 
                                    address: email,
                                    mobileRegion: nil))
            }

        }).disposed(by: disposeBag)

        createPinViewModel.pinFilled.subscribe(onNext: { [weak self] in
            self?.confirmPinViewModel.updatePinCode.onNext($0)
            self?.nextStep.onNext(())
        }).disposed(by: disposeBag)

        confirmPinViewModel.showToast.subscribe(onNext: { [weak self] in
            guard let self = self else { return }
            
            CUIToastView.show(data: $0,
                              inView: self.view,
                              underView: self.headerView)
        }).disposed(by: disposeBag)

        confirmPinViewModel.pinSuccess.subscribe(onNext: { [weak self] in
            self?.nextStep.onNext(())
        }).disposed(by: disposeBag)
        
        confirmPinViewModel.pinFailExceed.subscribe(onNext: { [weak self] in
            guard let self = self else { return }
            
            self.previousStep.onNext(())
            CUIToastView.show(data: .init(title: "key0942".localized(),
                                          desc: "key0943".localized(),
                                          titleStyle: .init(textColor: Color.txtNegative,
                                                            font: Font.semiBold.of(size: 12))),
                              inView: self.view,
                              underView: self.headerView
            )
        }).disposed(by: disposeBag)
    }
}

// MARK: - Update UI
extension ChangePinViewController {

    private func displayUI(step: ChangePinViewModel.ChangePinStep) {
        stepView.updateCurrentStep(step.rawValue + 1)
    }
}

// MARK: - Page View Controller
extension ChangePinViewController {
    
    func setupPageController() {
        self.addChild(pageController)
        pageContainerView.addSubview(pageController.view)
        pageController.view.frame = pageContainerView.bounds
        pageController.view.translatesAutoresizingMaskIntoConstraints = false
        pageController.didMove(toParent: self)
        setUpChildViewControllers()
    }

    func setUpChildViewControllers() {
        // If pages are already setup, return
        guard pages.isEmpty else { return }

        self.viewModel.steps.forEach {
            switch $0 {

            case .otpOption:
                pages.append(otpOptionViewController)

            case .otpVerify:
                pages.append(otpInputViewController)

            case .createPin:
                let createPinViewController = CreatePinViewController()
                createPinViewController.bind(viewModel: createPinViewModel)
                pages.append(createPinViewController)

            case .confirmPin:
                let confirmPinViewController = ConfirmPinViewController()
                confirmPinViewController.bind(viewModel: confirmPinViewModel)
                pages.append(confirmPinViewController)
            }
        }
        
        setPage(index: viewModel.currentStep.rawValue,
                direction: .forward,
                animated: false)
        bindChildViewModels()
    }
    
    func setPage(index: Int,
                 direction: UIPageViewController.NavigationDirection,
                 animated: Bool) {
        guard index < pages.count, index >= 0 else { return }
        
        pageController.setViewControllers([pages[index]],
                                          direction: direction,
                                          animated: animated)
    }
}
