//
//  ChangePinViewModel.swift
//  Authentication
//
//  Created by <PERSON><PERSON><PERSON> on 3/11/67.
//
import APILayer
import Core
import RxCocoa
import RxSwift
import RxRelay
import XCoordinator
import SharedData
import Storage
import CUIModule
import LocalAuthentication

public class ChangePinViewModel: AnyViewModel {
    
    let router: UnownedRouter<AuthenticationRoute>

    public struct Input {
        let onViewAppear: Observable<Void>
        let forwardStep: Observable<Void>
        let backwardStep: Observable<Void>
        let otpOption: Observable<(type: String, 
                                   address: String,
                                   mobileRegion: String?)>
        let resendOTP: Observable<Void>
        let submitOtp: Observable<String>
    }
    
    public struct Output {
        let loading: Driver<Bool>
        let otpError: Driver<Error>
        let displayData: Driver<ChangePinStep>
        let changeStep: Driver<(state: StepState,
                                step: ChangePinStep,
                                direction: UIPageViewController.NavigationDirection)>
        let otpRequested: Driver<(address: String,
                                  mobileRegion: String?,
                                  response: OTPRequestEndPoint.Response,
                                  isResend: Bool)>
        let otpSubmitted: Driver<String>
    }

    let steps = ChangePinStep.allCases
    private(set) var currentStep = ChangePinStep.otpOption

    let pinCount = 6
    private var otpType: String = ""
    private var otpAddress: String = ""
    private var mobileRegion: String?
    private var refCode = ""
    
    init(router: UnownedRouter<AuthenticationRoute>) {
        self.router = router
    }
    
    public func transform(input: Input) -> Output {
        let activityIndicator = ActivityIndicator()
        let errorTracker = ErrorTracker()
        
        let displayData = input.onViewAppear
            .map { self.currentStep }
        
        let forwardStep = input.forwardStep
            .map { self.forwardStep() }
            .map {
                (state: $0,
                 step: self.currentStep,
                 direction: UIPageViewController.NavigationDirection.forward)
            }
        
        let backwardStep = input.backwardStep
            .map { self.backwardStep() }
            .map {
                (state: $0,
                 step: self.currentStep,
                 direction: UIPageViewController.NavigationDirection.reverse)
            }

        let changeStep = Observable.merge(forwardStep,
                                          backwardStep)
        
        let otpRequest = input.otpOption
            .onNext { [unowned self] in
                otpType = $0.type
                otpAddress = $0.address
                mobileRegion = $0.mobileRegion
            }
            .map {
                (type: $0.type,
                 address: $0.address,
                 mobileRegion: $0.mobileRegion,
                 isResend: false)
            }
        
        let otpResend = input.resendOTP
            .map { [unowned self] in
                (type: otpType,
                 address: otpAddress,
                 mobileRegion: mobileRegion,
                 isResend: true)
            }
        
        let sendOTPRequest = Observable.merge(otpRequest, otpResend)
            .flatMap { [unowned self] data in
                requestOTP(for: data.type,
                           address: data.address,
                           mobileRegion: data.mobileRegion)
                .track(activityIndicator, error: errorTracker)
                .map {
                    (address: data.address,
                     mobileRegion: data.mobileRegion,
                     response: $0,
                     isResend: data.isResend)
                }
            }
            .onNext { [unowned self] in
                refCode = $0.response.refCode ?? ""
            }
        
        let submitOtp = input.submitOtp
            .flatMap { [unowned self] otp in
                submitOTP(otp, refCode: refCode, type: otpType)
                    .track(activityIndicator, error: errorTracker)
            }
            .map { $0.token ?? "" }
        
        return Output(loading: activityIndicator.asDriver(),
                      otpError: errorTracker.asDriver(),
                      displayData: displayData.asDriverOnErrorNever(),
                      changeStep: changeStep.asDriverOnErrorNever(),
                      otpRequested: sendOTPRequest.asDriverOnErrorNever(),
                      otpSubmitted: submitOtp.asDriverOnErrorNever())
    }

    func forwardStep() -> StepState {
        if currentStep == steps.last { return .complete }
        currentStep = ChangePinStep(rawValue: currentStep.rawValue + 1) ?? currentStep
        
        return .onGoing
    }

    func backwardStep()-> StepState {
        if currentStep == steps.first { return .quit }

        currentStep = ChangePinStep(rawValue: currentStep.rawValue - 1) ?? currentStep
        
        return .onGoing
    }
    
    func navigate(to route: AuthenticationRoute) {
        router.trigger(route)
    }
}

// MARK: - Api call
extension ChangePinViewModel {
    
    func requestOTP(for type: String, address: String, mobileRegion: String?) -> Observable<OTPRequestEndPoint.Response> {
        OTPRequestEndPoint.service.call(parameter: .init(bizType: BizType.forgotPasswordPin.rawValue,
                                                         otpType: type,
                                                         otpAddress: address,
                                                         mobileRegion: mobileRegion))
    }
    
    func submitOTP(_ otpCode: String,
                   refCode: String,
                   type: String) -> Observable<UserAuthResetVerificationEndPoint.Response> {
        UserAuthResetVerificationEndPoint.service.call(parameter: .init(username: Keychain.userName ?? "",
                                                                        type: "FORGET_PIN",
                                                                        otpType: type,
                                                                        otp: otpCode,
                                                                        refCode: refCode))
    }
    
    func resetPin(_ pin: String, token: String) -> Observable<Void> {
        UserAuthResetEndPoint.service.call(parameter: .init(token: token,
                                                            deviceId: LocalPreference.deviceId ?? "",
                                                            pin: pin))
        .mapToVoid()
    }
}

// MARK: - Display Model
public extension ChangePinViewModel {

    enum StepState {
        case quit
        case onGoing
        case complete
    }

    enum ChangePinStep: Int, CaseIterable {
        case otpOption
        case otpVerify
        case createPin
        case confirmPin

        var title: String { return "key0767".localized() }

        var icon: UIImage? { return .image(named: "ic_lock") }
    }
}
