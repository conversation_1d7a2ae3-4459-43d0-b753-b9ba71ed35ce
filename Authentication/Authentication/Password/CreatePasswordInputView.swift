//
//  CreatePasswordInputView.swift
//  Authentication
//
//  Created by <PERSON> on 06/03/2024.
//
import UIKit
import Core
import CUIModule
import RxSwift
import SharedData

public final class CreatePasswordInputView: UIView, AnyView {
    
    // MARK: UI properties
    private lazy var textfieldStackView = {
        let stackView = UIStackView(axis: .vertical,
                                    spacing: 16)
        
        stackView.addArrangedSubviews([titleLabel,
                                       newPasswordTF,
                                       newVerifyButtonsStackView,
                                       repeatPasswordTF,
                                       repeatVerifyButtonsStackView])
        stackView.setCustomSpacing(8, after: newPasswordTF)
        stackView.setCustomSpacing(8, after: repeatPasswordTF)
        
        return stackView
    }()
    
    private lazy var titleLabel = UILabel(font: Font.semiBold.of(size: 14),
                                          text: title,
                                          textColor: Color.txtTitle)
    
    fileprivate lazy var newPasswordTF = {
        let displayModel = AuthInputTextField.DisplayModel
            .initialSetup(title: "",
                          placeholder: "key0257".localized(),
                          needSecure: true)
        
        return AuthInputTextField(initialDisplay: displayModel)
    }()
    
    private lazy var newVerifyButtonsStackView = {
        let stackView = UIStackView(axis: .vertical,
                                    alignment: .leading,
                                    spacing: 2)
        
        return stackView
    }()
    
    fileprivate lazy var repeatPasswordTF = {
        let displayModel = AuthInputTextField.DisplayModel
            .initialSetup(title: "key0258".localized(),
                          placeholder: "key0259".localized(),
                          needSecure: true)
        
        return AuthInputTextField(initialDisplay: displayModel)
    }()

    private lazy var repeatVerifyButtonsStackView = {
        let stackView = UIStackView(axis: .vertical,
                                    alignment: .leading,
                                    spacing: 2)

        return stackView
    }()

    // MARK: Properties
    private let disposeBag = DisposeBag()
    private var newVerifyButtons: [UIButton] = []
    private var repeatVerifyButtons: [UIButton] = []
    
    private let title: String
    
    fileprivate let verifiedPassword = PublishSubject<String?>()
    
    // MARK: Life cycle
    public init(title: String = "key0256".localized()) {
        self.title = title
        super.init(frame: .zero)
        
        setupUI()
        bindActions()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    public func setupUI() {
        backgroundColor = .clear
        
        addSubview(textfieldStackView)
        
        textfieldStackView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        for field in PasswordVerifyField.newVerifyFields {
            let button = verifyButton(for: field)
            newVerifyButtonsStackView.addArrangedSubview(button)

            newVerifyButtons.append(button)
        }

        for field in PasswordVerifyField.repeatVerifyFields {
            let button = verifyButton(for: field)
            repeatVerifyButtonsStackView.addArrangedSubview(button)

            repeatVerifyButtons.append(button)
        }
    }
    
    public func bindActions() {        
        // Reset error state when begin editing
        Observable.merge(newPasswordTF.rx.didBeginEditing,
                         repeatPasswordTF.rx.didBeginEditing)
        .subscribe(onNext: { [unowned self] in
            newPasswordTF.updateUI(with: AuthInputTextField.DisplayModel.updateErrorState(isError: false))
            repeatPasswordTF.updateUI(with: AuthInputTextField.DisplayModel.updateErrorState(isError: false))
        })
        .disposed(by: disposeBag)
        
        // Enable Confirm button (verify both passwords)
        Observable.combineLatest(newPasswordTF.rx.text,
                                 repeatPasswordTF.rx.text)
        .subscribe(onNext: { [unowned self] password, repeatPassword in

            let newVerifiedFields = self.verifyNewPassword(password)
            for button in self.newVerifyButtons {
                button.isSelected = newVerifiedFields.contains(where: { $0.rawValue == button.tag })
            }

            let repeatVerifiedFields = self.verfifyRepeatPassword(repeatPassword, password)
            for button in self.repeatVerifyButtons {
                button.isSelected = repeatVerifiedFields.contains(where: { $0.rawValue == button.tag })
            }

            let validNewPassword = newVerifiedFields.count == PasswordVerifyField.newVerifyFields.count
            let validRepeatPassword = repeatVerifiedFields.count == PasswordVerifyField.repeatVerifyFields.count

            guard validNewPassword, validRepeatPassword else {
                verifiedPassword.onNext(nil)
                return
            }
            
            verifiedPassword.onNext(password)
        })
        .disposed(by: disposeBag)
    }

    public func reset() {
        [newPasswordTF,
         repeatPasswordTF]
            .forEach { tf in
                tf.updateUI(with: AuthInputTextField.DisplayModel.exitEditting)
                tf.updateUI(with: AuthInputTextField.DisplayModel.updateErrorState(isError: false))
                tf.updateUI(with: AuthInputTextField.DisplayModel.setValue(value: ""))
            }
    }
}

// MARK: - Private
private extension CreatePasswordInputView {
    
    func verifyButton(for field: PasswordVerifyField) -> UIButton {
        UIButton(titleFont: Font.light.of(size: 12),
                 title: field.title,
                 normalTitleColor: Color.txtInactive,
                 selectedTitleColor: Color.txtParagraph,
                 normalImage: .image(named: "merit_tick_grey"),
                 selectedImage: .image(named: "merit_tick_green"),
                 contentHorizontalAlignment: .leading,
                 titleInsets: UIEdgeInsets(left: 4, right: -4),
                 isUserInteractionEnabled: false,
                 tag: field.rawValue)
    }
    
    func verifyNewPassword(_ password: String) -> [PasswordVerifyField] {
        var verifiedFields = PasswordVerifyField.newVerifyFields
        if password.count < 8 {
            verifiedFields.removeAll(where: { $0 == .characterCount })
        }
        
        if password.allSatisfy({ !$0.isUppercase }) {
            verifiedFields.removeAll(where: { $0 == .upperCharacter })
        }

        if password.allSatisfy({ !$0.isNumber }) {
            verifiedFields.removeAll(where: { $0 == .numberCharacter })
        }
        
        let charSet = CharacterSet(charactersIn: "#$%@&_")
        if password.rangeOfCharacter(from: charSet) == nil {
            verifiedFields.removeAll(where: { $0 == .specialCharacter })
        }
        
        return verifiedFields
    }

    func verfifyRepeatPassword(_ password1: String, _ password2: String) -> [PasswordVerifyField] {
        var verifiedFields = PasswordVerifyField.repeatVerifyFields

        if (password1.isEmpty && password2.isEmpty) || password1 != password2 {
            verifiedFields.removeAll(where: { $0 == .matched })
        }

        return verifiedFields
    }
}

// MARK: - extension Reactive
public extension Reactive where Base: CreatePasswordInputView {
    
    var verifiedPassword: Observable<String?> {
        base.verifiedPassword.asObservable()
    }
}
