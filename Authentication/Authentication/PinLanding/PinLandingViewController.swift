//
//  PinLandingViewController.swift
//  Authentication
//
//  Created by <PERSON><PERSON><PERSON> on 3/4/67.
//
import CUIModule
import UIKit
import SnapKit
import RxSwift
import Core
import Storage

final class PinLandingViewController: BaseViewController, VMView {
    
    private lazy var stackView = UIStackView(axis: .vertical,
                                             spacing: 16)
    
    private lazy var bannerImageView = UIImageView(contentMode: .scaleAspectFit,
                                                   image: .image(named: "merit_pinsetup"))
    
    private lazy var titleLabel = UILabel(font: Font.semiBold.of(size: 18),
                                          text: "key0212".localized(),
                                          textColor: Color.txtTitle,
                                          textAlignment: .center)
    
    private lazy var descLabel = {
        let label = UILabel()
        label.highlight("key0213".localized(),
                        normalColor: Color.txtParagraph,
                        normalFont: Font.light.of(size: 14),
                        highlightText: ["key0214".localized()],
                        highlightColor: Color.btn2nd,
                        highlightFont: Font.regular.of(size: 14))
        
        return label
    }()
    
    private lazy var buttonStackView = UIStackView(axis: .vertical,
                                                   spacing: 8)
    
    private lazy var setUpButton = ThemeableButton(
        titleFont: Font.medium.of(size: 16),
        title: "key0215".localized(),
        normalTitleColor: Color.txtInverted,
        normalBgColor: Color.btn2nd,
        cornerRadius: 22
    )
    
    private lazy var askLaterButton = ThemeableButton(
        titleFont: Font.medium.of(size: 14),
        title: "key0197".localized(),
        normalTitleColor: Color.txtTitle,
        normalBgColor: Color.bgDefault,
        cornerRadius: 22,
        borderColor: Color.txtTitle,
        borderWidth: 1
    )
    
    var viewModel: PinLandingViewModel!
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        bindActions()
    }
    
    func setupUI() {
        let screenHeight = UIScreen.main.bounds.height
        
        view.addSubviews([stackView])
        
        stackView.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide.snp.top).offset(screenHeight*0.078)
            make.left.equalTo(12)
            make.right.equalTo(-12)
        }
        
        stackView.addArrangedSubviews([
            bannerImageView, titleLabel,
            descLabel, buttonStackView
        ])
        
        bannerImageView.snp.makeConstraints { make in
            make.height.equalTo(screenHeight*0.18)
        }
        
        stackView.setCustomSpacing(screenHeight*0.04, after: bannerImageView)
        stackView.setCustomSpacing(16, after: titleLabel)
        stackView.setCustomSpacing(screenHeight*0.04, after: descLabel)
        
        [setUpButton, askLaterButton].forEach {
            buttonStackView.addArrangedSubview($0)
            $0.snp.makeConstraints { make in
                make.height.equalTo(44)
            }
        }
    }
    
    func bind(viewModel: PinLandingViewModel) {
        self.viewModel = viewModel
        
        let input = PinLandingViewModel.Input(
            onViewAppear: rx.viewWillAppear.take(1).mapToVoid()
        )
        
        let output = viewModel.transform(input: input)
        
        output.displayData.drive(onNext: { [weak self] in
            self?.displayUI()
        }).disposed(by: disposeBag)
    }
    
    func bindActions() {
        
        setUpButton.rx.tap.subscribe(onNext: { [weak self] in
            Keychain.userPin = nil
            LocalPreference.loginPinEnable = nil
            LocalPreference.biometricEnable = nil
            self?.viewModel.navigate(to: .pinSetup(completion: nil))
        }).disposed(by: disposeBag)
        
        askLaterButton.rx.tap.subscribe(onNext: { [weak self] in
            Keychain.userPin = nil
            LocalPreference.loginPinEnable = nil
            LocalPreference.biometricEnable = nil
            self?.viewModel.navigate(to: .pinCompleted(false, false))
        }).disposed(by: disposeBag)
    }
    
    private func displayUI() {
        
    }
}
