//
//  PinSetupCompleteViewController.swift
//  Authentication
//
//  Created by <PERSON><PERSON> on 3/6/67.
//
import CUIModule
import UIKit
import SnapKit
import RxSwift
import Core
import Storage

final class PinSetupCompleteViewController: BaseViewController, VMView {

    private lazy var stackView = UIStackView(
        axis: .vertical,
        spacing: 0
    )

    private lazy var bannerImageView = UIImageView(
        contentMode: .scaleAspectFit,
        image: .image(named: "merit_alldone")
    )

    private lazy var titleLabel = UILabel(
        font: Font.semiBold.of(size: 18),
        text: "key0224".localized(),
        textColor: Color.txtTitle,
        textAlignment: .center
    )

    private lazy var descLabel = UILabel(
        font: Font.light.of(size: 14),
        textColor: Color.txtParagraph,
        lines: 0
    )

    private lazy var startButton = ThemeableButton(
        titleFont: Font.medium.of(size: 14),
        title: "key0211".localized(),
        normalTitleColor: Color.txtInverted,
        normalBgColor: Color.btn2nd,
        cornerRadius: 22
    )

    var viewModel: PinSetupCompleteViewModel!

    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        bindActions()
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        navigationBarHidden(true)
    }

    func setupUI() {
        let screenHeight = UIScreen.main.bounds.height

        view.addSubviews([stackView, startButton])
        stackView.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide.snp.top).offset(screenHeight*0.13)
            make.left.equalTo(12)
            make.right.equalTo(-12)
        }

        startButton.snp.makeConstraints { make in
            make.left.equalTo(12)
            make.right.equalTo(-12)
            make.bottom.equalTo(view.safeAreaLayoutGuide.snp.bottom).offset(-16)
            make.height.equalTo(44)
        }
        
        stackView.addArrangedSubviews([
            bannerImageView, titleLabel,
            descLabel
        ])

        bannerImageView.snp.makeConstraints { make in
            make.height.equalTo(screenHeight*0.21)
        }

        stackView.setCustomSpacing(screenHeight*0.04, after: bannerImageView)
        stackView.setCustomSpacing(16, after: titleLabel)
    }

    func bind(viewModel: PinSetupCompleteViewModel) {
        self.viewModel = viewModel

        let input = PinSetupCompleteViewModel.Input(
            onViewAppear: rx.viewWillAppear.take(1).mapToVoid()
        )

        let output = viewModel.transform(input: input)

        output.displayData.drive(onNext: { [weak self] in
            self?.displayUI(type: $0.0, actionTitle: $0.1, showNew: $0.2)
        }).disposed(by: disposeBag)
    }

    func bindActions() {
        startButton.rx.tap.subscribe(onNext: { [weak self] in
            self?.viewModel.navigate(to: .pinCompleted(
                LocalPreference.loginPinEnable ?? false, LocalPreference.biometricEnable ?? false
            ))
        }).disposed(by: disposeBag)
    }

    private func displayUI(
        type: PinSetupCompleteViewModel.SetUpType,
        actionTitle: String,
        showNew: Bool
    ) {

        let newTag = showNew ? "new " : ""

        switch type {
        case .pinOnly: 
            descLabel.text = "key0225".localized(arguments: newTag)
        case .bioOnly:
            descLabel.text = "key0226".localized(arguments: newTag)
        case .pinAndBio:
            descLabel.text = "key0226".localized(arguments: newTag)
        }

        descLabel.setLineSpacing(lineHeightMultiple: 1.2)

        startButton.setTitle(actionTitle, for: .normal)
    }
}
