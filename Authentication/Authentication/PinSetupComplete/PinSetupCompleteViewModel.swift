//
//  PinSetupCompleteViewModel.swift
//  Authentication
//
//  Created by <PERSON><PERSON> on 3/6/67.
//
import APILayer
import Core
import CUIModule
import RxCocoa
import RxSwift
import RxRelay
import XCoordinator

public class PinSetupCompleteViewModel: AnyViewModel {

    private let router: UnownedRouter<AuthenticationRoute>

    public struct Input {
        let onViewAppear: Observable<Void>
    }

    public struct Output {
        let displayData: Driver<(SetUpType, String, Bool)>
    }

    private let type: SetUpType
    private let actionTitle: String
    private let showNew: Bool

    init(
        router: UnownedRouter<AuthenticationRoute>,
        type: SetUpType,
        actionTitle: String,
        showNew: Bool
    ) {
        self.router = router
        self.type = type
        self.actionTitle = actionTitle
        self.showNew = showNew
    }

    public func transform(input: Input) -> Output {

        let displayData = input.onViewAppear
            .map { (self.type, self.actionTitle, self.showNew) }

        return Output(
            displayData: displayData.asDriverOnErrorNever()
        )
    }

    func navigate(to route: AuthenticationRoute) {
        self.router.trigger(route)
    }
}

extension PinSetupCompleteViewModel {

    public enum SetUpType {
        case pinOnly
        case bioOnly
        case pinAndBio
    }
}
