//
//  CreatePinViewController.swift
//  Authentication
//
//  Created by <PERSON><PERSON><PERSON> on 3/4/67.
//
import CUIModule
import UIKit
import SnapKit
import RxSwift
import Core
import Storage

final class CreatePinViewController: BaseViewController, VMView {

    private lazy var pinView = PinView(
        title: "key0217".localized(),
        caption: "key0218".localized(arguments: viewModel.pinCount),
        pinCount: viewModel.pinCount,
        showBiometric: false
    )

    var viewModel: CreatePinViewModel!

    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        bindActions()
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        navigationBarHidden(true)
    }

    func setupUI() {

        view.addSubview(pinView)
        pinView.snp.makeConstraints { make in
            make.top.equalTo(24)
            make.bottom.equalTo(view.safeAreaLayoutGuide.snp.bottom).offset(-24)
            make.left.equalTo(12)
            make.right.equalTo(-12)
        }
    }

    func bind(viewModel: CreatePinViewModel) {
        self.viewModel = viewModel

        let input = CreatePinViewModel.Input(
            onViewAppear: rx.viewWillAppear.mapToVoid()
        )

        let output = viewModel.transform(input: input)

        output.displayData.drive(onNext: { [weak self] in
            self?.displayUI()
        }).disposed(by: disposeBag)
    }

    func bindActions() {
        pinView.rx.pinFilled.bind(to: viewModel.pinFilled).disposed(by: disposeBag)
    }

    private func displayUI() {
        pinView.reset()
    }
}
