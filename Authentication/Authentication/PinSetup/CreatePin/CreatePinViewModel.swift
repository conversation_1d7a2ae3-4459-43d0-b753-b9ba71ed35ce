//
//  CreatePinViewModel.swift
//  Authentication
//
//  Created by <PERSON><PERSON><PERSON> on 3/4/67.
//
import APILayer
import Core
import CUIModule
import RxCocoa
import RxSwift
import RxRelay
import XCoordinator

public class CreatePinViewModel: AnyViewModel {

    private let router: UnownedRouter<AuthenticationRoute>

    public struct Input {
        let onViewAppear: Observable<Void>
    }

    public struct Output {
        let displayData: Driver<Void>
    }

    // Output to parent
    let pinFilled = PublishSubject<String>()

    let pinCount: Int

    init(
        router: UnownedRouter<AuthenticationRoute>,
        pinCount: Int
    ) {
        self.router = router
        self.pinCount = pinCount
    }

    public func transform(input: Input) -> Output {
        
        let displayData = input.onViewAppear

        return Output(
            displayData: displayData.asDriverOnErrorNever()
        )
    }

    func navigate(to route: AuthenticationRoute) {
        self.router.trigger(route)
    }
}
