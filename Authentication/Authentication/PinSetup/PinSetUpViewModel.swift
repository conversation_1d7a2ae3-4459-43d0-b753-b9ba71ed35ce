//
//  PinSetUpViewModel.swift
//  Authentication
//
//  Created by <PERSON><PERSON><PERSON> on 3/4/67.
//
import APILayer
import Core
import RxCocoa
import RxSwift
import RxRelay
import XCoordinator
import SharedData
import Storage
import CUIModule
import D<PERSON>harts
import LocalAuthentication

public class PinSetUpViewModel: AnyViewModel {
    let router: UnownedRouter<AuthenticationRoute>

    public struct Input {
        let onViewAppear: Observable<Void>
        let forwardStep: Observable<Void>
        let backwardStep: Observable<Void>
    }

    public struct Output {
        let displayData: Driver<PinSetUpStep>
        let changeStep: Driver<(
            state: StepState,
            step: PinSetUpStep,
            direction: UIPageViewController.NavigationDirection
        )>
    }

    internal let steps = PinSetUpStep.allCases
    private(set) var currentStep = PinSetUpStep.createPin

    let pinCount = 6

    init(router: UnownedRouter<AuthenticationRoute>) {
        self.router = router
    }

    public func transform(input: Input) -> Output {

        let displayData = input.onViewAppear
            .map { self.currentStep }

        let forwardStep = input.forwardStep
            .map { self.forwardStep() }
            .map {
                (
                    state: $0,
                    step: self.currentStep,
                    direction: UIPageViewController.NavigationDirection.forward
                )
            }

        let backwardStep = input.backwardStep
            .map { self.backwardStep() }
            .map {
                (
                    state: $0,
                    step: self.currentStep,
                    direction: UIPageViewController.NavigationDirection.reverse
                )
            }

        let changeStep = Observable.merge(forwardStep, backwardStep)

        return Output(
            displayData: displayData.asDriverOnErrorNever(),
            changeStep: changeStep.asDriverOnErrorNever()
        )
    }

    func forwardStep() -> StepState {
        if currentStep == steps.last { return .complete }
        currentStep = PinSetUpStep(rawValue: currentStep.rawValue + 1) ?? currentStep
        return .onGoing
    }

    func backwardStep()-> StepState {
        if currentStep == steps.first { return .quit }
        
        if currentStep == .biometric {
            currentStep = .createPin
        } else {
            currentStep = PinSetUpStep(rawValue: currentStep.rawValue - 1) ?? currentStep
        }
        return .onGoing
    }

    func navigate(to route: AuthenticationRoute) {
        self.router.trigger(route)
    }

}
// MARK: - Display Model
public extension PinSetUpViewModel {

    enum StepState {
        case quit
        case onGoing
        case complete
    }

    enum PinSetUpStep: Int, CaseIterable {
        case createPin
        case confirmPin
        case biometric

        public static var allCases: [PinSetUpViewModel.PinSetUpStep] {
            switch LAContext().biometricType {
            case .none:
                return [.createPin, .confirmPin]
            case .touchID, .faceID:
                return [.createPin, .confirmPin, .biometric]
            }
        }

        var titleWithStep: String {
            switch self {
            case .createPin:
                return "key0003".localized() + ": " + "key0217".localized()
            case .confirmPin:
                return "key0004".localized() + ": " + "key0219".localized()
            case .biometric:
                return "key0005".localized() + ": " + "key0221".localized()
            }
        }

        var title: String {
            switch self {
            case .createPin:
                return "key0217".localized()
            case .confirmPin:
                return "key0219".localized()
            case .biometric:
                return "key0221".localized()
            }
        }
    }
}
