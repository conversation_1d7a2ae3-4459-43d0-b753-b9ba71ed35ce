//
//  PinSetUpViewController.swift
//  Authentication
//
//  Created by <PERSON><PERSON> on 3/4/67.
//
import Core
import CUIModule
import RxSwift
import RxCocoa
import Utils
import SharedData
import Storage

final class PinSetUpViewController: BaseViewController, VMView {

    private lazy var navView = AuthNavigationView()

    private lazy var stepView = CUIStepIndicatorView(totalSteps: self.viewModel.steps.count)

    private lazy var pageContainerView = UIView(backgroundColor: .clear)

    private lazy var pageController: UIPageViewController = {
        let pageController = UIPageViewController(
            transitionStyle: .scroll,
            navigationOrientation: .horizontal,
            options: nil
        )
        pageController.view.backgroundColor = .clear
        return pageController
    }()

    var viewModel: PinSetUpViewModel!

    private var pages: [UIViewController] = []

    private lazy var createPinViewModel = CreatePinViewModel(
        router: viewModel.router,
        pinCount: viewModel.pinCount
    )
    private lazy var confirmPinViewModel = ConfirmPinViewModel(
        router: viewModel.router,
        pinCount: viewModel.pinCount,
        type: .setUp
    )
    private lazy var biometricSetupViewModel = BiometricSetupViewModel(
        router: viewModel.router
    )

    private let nextStep = PublishSubject<Void>()
    private let previousStep = PublishSubject<Void>()

    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        bindActions()
        setupPageController()
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        navigationBarHidden(true)
    }

    func setupUI() {
        view.backgroundColor = Color.bgDefault

        view.addSubviews([navView, stepView, pageContainerView])
        navView.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
        }

        stepView.snp.makeConstraints { make in
            make.top.equalTo(navView.snp.bottom).offset(32)
            make.left.right.equalToSuperview()
            make.height.equalTo(30)
        }

        pageContainerView.snp.makeConstraints { make in
            make.top.equalTo(stepView.snp.bottom)
            make.bottom.left.right.equalToSuperview()
        }
    }

    func bindActions() {

    }

    func bind(viewModel: PinSetUpViewModel) {
        self.viewModel = viewModel

        let input = PinSetUpViewModel.Input(
            onViewAppear: rx.viewWillAppear.mapToVoid(),
            forwardStep: nextStep,
            backwardStep: Observable.merge(previousStep, navView.rx.back)
        )

        let output = viewModel.transform(input: input)

        output.displayData.drive(onNext: { [weak self] in
            self?.displayUI(step: $0)
        }).disposed(by: disposeBag)

        output.changeStep.drive(onNext: { [weak self] in
            switch $0.state {
            case .quit:
                self?.navigationController?.popViewController(animated: true)
            case .onGoing:
                self?.displayUI(step: $0.step)
                self?.setPage(index: $0.step.rawValue, direction: $0.direction, animated: true)
            case .complete:
                self?.viewModel.navigate(to: .pinSetupCompleted(
                    type: LocalPreference.biometricEnable ?? false ? .pinAndBio : .pinOnly,
                    actionTitle: "key0211".localized(),
                    showNew: false
                ))
            }

        }).disposed(by: disposeBag)
    }

    func bindChildViewModels() {
        createPinViewModel.pinFilled.subscribe(onNext: { [weak self] in
            self?.confirmPinViewModel.updatePinCode.onNext($0)
            self?.nextStep.onNext(())
        }).disposed(by: disposeBag)

        confirmPinViewModel.showToast.subscribe(onNext: { [weak self] in
            guard let self = self else { return }
            CUIToastView.show(data: $0, inView: self.view, underView: self.navView)
        }).disposed(by: disposeBag)

        confirmPinViewModel.pinSuccess.subscribe(onNext: { [weak self] in
            self?.nextStep.onNext(())
        }).disposed(by: disposeBag)

        confirmPinViewModel.pinFailExceed.subscribe(onNext: { [weak self] in
            guard let self = self else { return }
            self.previousStep.onNext(())
            CUIToastView.show(
                data: CUIToastView.DisplayModel(
                    title: "key0942".localized(),
                    desc: "key0943".localized(),
                    titleStyle: CUIToastView.TextStyle(
                        textColor: Color.txtNegative
                    )
                ), 
                inView: self.view,
                underView: self.navView
            )
        }).disposed(by: disposeBag)

        biometricSetupViewModel.onCompleted.subscribe(onNext: { [weak self] in
            guard let self = self else { return }
            self.viewModel.navigate(to: .pinSetupCompleted(
                type: $0 ? .pinAndBio : .pinOnly,
                actionTitle: "key0211".localized(),
                showNew: false
            ))
        }).disposed(by: disposeBag)
    }
}
// MARK: - Update UI
extension PinSetUpViewController {

    private func displayUI(
        step: PinSetUpViewModel.PinSetUpStep
    ) {
        navView.setTitle(
            fullText: step.titleWithStep,
            highlightText: [step.title]
        )

        stepView.updateCurrentStep(step.rawValue + 1)
    }
}
// MARK: - Page View Controller
extension PinSetUpViewController {
    func setupPageController() {
        self.addChild(pageController)
        pageContainerView.addSubview(pageController.view)
        pageController.view.frame = pageContainerView.bounds
        pageController.view.translatesAutoresizingMaskIntoConstraints = false
        pageController.didMove(toParent: self)
        setUpChildViewControllers()
    }

    func setUpChildViewControllers() {
        // If pages are already setup, return
        guard pages.isEmpty else { return }

        self.viewModel.steps.forEach {
            switch $0 {
            case .createPin:
                let createPinViewController = CreatePinViewController()
                createPinViewController.bind(viewModel: createPinViewModel)
                pages.append(createPinViewController)

            case .confirmPin:
                let confirmPinViewController = ConfirmPinViewController()
                confirmPinViewController.bind(viewModel: confirmPinViewModel)
                pages.append(confirmPinViewController)

            case .biometric:
                let biometricViewController = BiometricSetupViewController(cancelActionTitle: "key0084".localized())
                biometricViewController.bind(viewModel: biometricSetupViewModel)
                pages.append(biometricViewController)
            }
        }
        setPage(index: viewModel.currentStep.rawValue, direction: .forward, animated: false)
        bindChildViewModels()
    }

    func setPage(
        index: Int,
        direction: UIPageViewController.NavigationDirection,
        animated: Bool
    ) {
        guard index < pages.count, index >= 0 else { return }
        pageController.setViewControllers(
            [pages[index]],
            direction: direction,
            animated: animated
        )
    }
}
