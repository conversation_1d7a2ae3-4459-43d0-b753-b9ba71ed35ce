//
//  ConfirmPinViewModel.swift
//  Authentication
//
//  Created by <PERSON><PERSON><PERSON> on 3/6/67.
//
import APILayer
import Core
import CUIModule
import RxCocoa
import RxSwift
import RxRelay
import XCoordinator
import Storage

public class ConfirmPinViewModel: AnyViewModel {
    
    private let router: UnownedRouter<AuthenticationRoute>
    
    public struct Input {
        let onViewAppear: Observable<Void>
        let onPinFilled: Observable<String>
    }
    
    public struct Output {
        let loading: Driver<Bool>
        let error: Driver<Error>
        let displayData: Driver<Void>
        let pinSuccess: Driver<Void>
        let pinFail: Driver<Int>
        let pinFailExcced: Driver<Void>
    }
    
    // Input from parent
    let updatePinCode = PublishSubject<String>()
    let updateToken = BehaviorSubject(value: "")
    
    // Output to parent
    let showToast = PublishSubject<CUIToastView.DisplayModel>()
    let pinSuccess = PublishSubject<Void>()
    let pinFailExceed = PublishSubject<Void>()
    
    private var pinCode: String = ""
    let pinCount: Int
    
    private let maxFailAttempt = 5
    private var failCount = 0
    
    private let type: ConfirmPinViewModel.AuthType
    
    init(router: UnownedRouter<AuthenticationRoute>,
         pinCount: Int,
         type: ConfirmPinViewModel.AuthType) {
        self.router = router
        self.pinCount = pinCount
        self.type = type
    }
    
    public func transform(input: Input) -> Output {
        let activityIndicator = ActivityIndicator()
        let errorTracker = ErrorTracker()

        let displayData = input.onViewAppear
            .withLatestFrom(updatePinCode)
            .onNext {
                self.pinCode = $0
                self.failCount = 0
            }
            .mapToVoid()

        let pinSuccess = input.onPinFilled
            .filter {
                self.pinCode == $0
            }
            .withLatestFrom(updateToken) { ($0, $1) }
            .flatMap { pin, token in
                self.updateUserPIN(pin: pin, token: token)
                    .track(activityIndicator, error: errorTracker)
            }
            .onNext {
                Keychain.userPin = self.pinCode
                LocalPreference.loginPinEnable = true
                LocalPreference.configuredPin = true
            }

        let pinFail = input.onPinFilled
            .filter {
                self.pinCode != $0
            }
            .onNext { _ in
                self.failCount += 1
            }
            .filter { _ in
                self.failCount < self.maxFailAttempt
            }
            .map { _ in
                self.maxFailAttempt - self.failCount
            }

        let pinFailExcced = input.onPinFilled
            .filter {
                self.pinCode != $0
            }
            .filter { _ in
                self.failCount >= self.maxFailAttempt
            }
            .mapToVoid()
        
        return Output(loading: activityIndicator.asDriver(),
                      error: errorTracker.asDriver(),
                      displayData: displayData.asDriverOnErrorNever(),
                      pinSuccess: pinSuccess.asDriverOnErrorNever(),
                      pinFail: pinFail.asDriverOnErrorNever(),
                      pinFailExcced: pinFailExcced.asDriverOnErrorNever())
    }
    
    func navigate(to route: AuthenticationRoute) {
        self.router.trigger(route)
    }
}

// MARK: - Api call
private extension ConfirmPinViewModel {
    
    func updateUserPIN(pin: String, token: String) -> Observable<Void> {
        switch type {
        case .setUp:
            return UserAuthEndPoint.service.request(password: Keychain.userPassword ?? "",
                                                    deviceId: LocalPreference.deviceId ?? "",
                                                    newPin: pin)
            .mapToVoid()
            
        case .change,
                .forgot:
            return UserAuthResetEndPoint.service.call(parameter: .init(token: token,
                                                                       deviceId: LocalPreference.deviceId ?? "",
                                                                       pin: pin))
            .mapToVoid()
        }
    }
}

// MARK: - Model
extension ConfirmPinViewModel {
    
    enum AuthType {
        case setUp
        case change
        case forgot
    }
}
