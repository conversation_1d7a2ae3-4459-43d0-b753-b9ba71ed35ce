//
//  ConfirmPinViewController.swift
//  Authentication
//
//  Created by <PERSON><PERSON><PERSON> on 3/6/67.
//
import CUIModule
import UIKit
import SnapKit
import RxSwift
import Core

final class ConfirmPinViewController: BaseViewController, VMView {

    private lazy var pinView = PinView(
        title: "key0219".localized(),
        caption: "key0220".localized(arguments: viewModel.pinCount),
        pinCount: viewModel.pinCount,
        showBiometric: false
    )

    var viewModel: ConfirmPinViewModel!

    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        bindActions()
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        navigationBarHidden(true)
    }

    func setupUI() {

        view.addSubview(pinView)
        pinView.snp.makeConstraints { make in
            make.top.equalTo(24)
            make.bottom.equalTo(view.safeAreaLayoutGuide.snp.bottom).offset(-24)
            make.left.equalTo(12)
            make.right.equalTo(-12)
        }
    }

    func bind(viewModel: ConfirmPinViewModel) {
        self.viewModel = viewModel

        let input = ConfirmPinViewModel.Input(
            onViewAppear: rx.viewWillAppear.mapToVoid(),
            onPinFilled: pinView.rx.pinFilled
        )

        let output = viewModel.transform(input: input)

        output.loading.drive(onNext: { [weak self] loading in
            loading ? self?.showLoading() : self?.hiddenLoading()
        }).disposed(by: disposeBag)

        output.error.drive(onNext: { [weak self] error in
            self?.pinView.reset()
            self?.viewModel.showToast.onNext(CUIToastView.DisplayModel(
                title: "key0895".localized(),
                desc: error.localizedDescription,
                titleStyle: CUIToastView.TextStyle(
                    textColor: Color.txtNegative
                )
            ))
        }).disposed(by: disposeBag)

        output.displayData.drive(onNext: { [weak self] in
            self?.displayUI()
        }).disposed(by: disposeBag)

        output.pinSuccess.drive(onNext: { [weak self] in
            self?.viewModel.pinSuccess.onNext(())
        }).disposed(by: disposeBag)

        output.pinFail.drive(onNext: { [weak self] in
            self?.pinView.reset()
            self?.viewModel.showToast.onNext(CUIToastView.DisplayModel(
                title: "key0942".localized(),
                desc: "key0943".localized(arguments: $0),
                titleStyle: CUIToastView.TextStyle(
                    textColor: Color.txtNegative
                )
            ))
        }).disposed(by: disposeBag)

        output.pinFailExcced.drive(onNext: { [weak self] in
            self?.viewModel.pinFailExceed.onNext(())
        }).disposed(by: disposeBag)
    }

    func bindActions() {

    }

    private func displayUI() {
        pinView.reset()
    }
}
