//
//  BiometricSetupViewModel.swift
//  Authentication
//
//  Created by <PERSON><PERSON><PERSON> on 3/6/67.
//
import APILayer
import Core
import CUIModule
import RxCocoa
import RxSwift
import RxRelay
import XCoordinator

public class BiometricSetupViewModel: AnyViewModel {

    private let router: UnownedRouter<AuthenticationRoute>

    public struct Input {
        let onViewAppear: Observable<Void>
    }

    public struct Output {
        let displayData: Driver<Void>
    }

    // Output to parent
    public let onCompleted = PublishSubject<Bool>()

    init(router: UnownedRouter<AuthenticationRoute>) {
        self.router = router
    }

    public func transform(input: Input) -> Output {
        return Output(
            displayData: input.onViewAppear.asDriverOnErrorNever()
        )
    }

    func navigate(to route: AuthenticationRoute) {
        self.router.trigger(route)
    }
}
