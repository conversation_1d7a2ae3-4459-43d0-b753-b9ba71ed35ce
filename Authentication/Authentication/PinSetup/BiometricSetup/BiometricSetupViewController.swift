//
//  BiometricSetupViewController.swift
//  Authentication
//
//  Created by <PERSON><PERSON> on 3/6/67.
//
import CUIModule
import UIKit
import SnapKit
import RxSwift
import Core
import Storage
import SharedData
import LocalAuthentication
import Utils

final class BiometricSetupViewController: BaseViewController, VMView {

    private lazy var stackView = UIStackView(
        axis: .vertical,
        spacing: 0
    )

    private lazy var bannerImageView = UIImageView(
        contentMode: .scaleAspectFit
    )

    private lazy var titleLabel = UILabel(
        font: Font.semiBold.of(size: 18),
        numberOfLines: 0,
        text: "key0222".localized(),
        textColor: Color.txtTitle,
        textAlignment: .center
    )

    private lazy var descLabel: UILabel = {
        let label = UILabel(
            font: Font.light.of(size: 14),
            numberOfLines: 0,
            text: "key0223".localized(),
            textColor: Color.txtParagraph
        )
        label.setLineSpacing(lineSpacing: 1.2)
        return label
    }()

    private lazy var buttonStackView = UIStackView(
        axis: .horizontal,
        distribution: .fillEqually,
        spacing: 8
    )

    private lazy var skipButton = ThemeableButton(
        titleFont: Font.medium.of(size: 14),
        title: "key0084".localized(),
        normalTitleColor: Color.txtTitle,
        normalBgColor: Color.bgDefault,
        cornerRadius: 22,
        borderColor: Color.txtTitle,
        borderWidth: 1
    )

    private lazy var enableButton = ThemeableButton(
        titleFont: Font.medium.of(size: 14),
        title: "key0221".localized(),
        normalTitleColor: Color.txtInverted,
        normalBgColor: Color.btn2nd,
        cornerRadius: 22
    )

    var viewModel: BiometricSetupViewModel!

    init(cancelActionTitle: String) {
        super.init(nibName: nil, bundle: nil)
        skipButton.setTitle(cancelActionTitle, for: .normal)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        bindActions()
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        navigationBarHidden(true)
    }

    func setupUI() {
        let screenHeight = UIScreen.main.bounds.height

        view.addSubviews([stackView, buttonStackView])
        stackView.snp.makeConstraints { make in
            make.top.equalTo(screenHeight*0.059)
            make.left.equalTo(12)
            make.right.equalTo(-12)
        }

        buttonStackView.snp.makeConstraints { make in
            make.left.equalTo(12)
            make.right.equalTo(-12)
            make.height.equalTo(44)
            make.bottom.equalTo(view.safeAreaLayoutGuide.snp.bottom).offset(-16)
        }

        stackView.addArrangedSubviews([
            bannerImageView, titleLabel, descLabel
        ])

        bannerImageView.snp.makeConstraints { make in
            make.height.equalTo(screenHeight*0.18)
        }

        stackView.setCustomSpacing(screenHeight*0.04, after: bannerImageView)
        stackView.setCustomSpacing(16, after: titleLabel)

        buttonStackView.addArrangedSubviews([skipButton, enableButton])
    }

    func bind(viewModel: BiometricSetupViewModel) {
        self.viewModel = viewModel

        let input = BiometricSetupViewModel.Input(
            onViewAppear: rx.viewWillAppear.mapToVoid()
        )

        let output = viewModel.transform(input: input)

        output.displayData.drive(onNext: { [weak self] in
            self?.displayUI()
        }).disposed(by: disposeBag)
    }

    func bindActions() {
        enableButton.rx.tap.subscribe(onNext: { [weak self] in
            LAPolicyUtil.evaluatePolicy(reason: "key0947".localized(), result: {
                guard let self = self else { return }
                switch $0 {
                case .none: break
                case .touchID, .faceID:
                    LocalPreference.biometricEnable = true
                    self.viewModel.onCompleted.onNext(true)
                }
            })
        }).disposed(by: disposeBag)

        skipButton.rx.tap.subscribe(onNext: { [weak self] in
            guard let self = self else { return }
            self.viewModel.onCompleted.onNext(false)
        }).disposed(by: disposeBag)
    }

    private func displayUI() {
        LocalPreference.biometricEnable = false
        switch LAContext().biometricType {
        case .none:
            break
        case .touchID:
            bannerImageView.image = .image(named: "merit_touchid")
        case .faceID:
            bannerImageView.image = .image(named: "merit_faceid")
        }
    }
}
