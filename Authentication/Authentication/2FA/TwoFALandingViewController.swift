//
//  TwoFALandingViewController.swift
//  Authentication
//
//  Created by <PERSON><PERSON> on 3/1/67.
//
import CUIModule
import UIKit
import SnapKit
import RxSwift
import Core
import Storage
import SharedData
import APILayer

final class TwoFALandingViewController: BaseViewController, VMView {
    
    // MARK: UI properties
    private lazy var backButton = UIButton(normalImage: .image(named: "merit_ic_back"))
    
    private lazy var stackView = UIStackView(axis: .vertical)
    
    private lazy var bannerImageView = UIImageView(contentMode: .scaleAspectFit,
                                                   image: .image(named: "merit_2fa_image"))
    
    private lazy var titleLabel = UILabel(font: Font.semiBold.of(size: 18),
                                          text: "key0191".localized(),
                                          textColor: Color.txtTitle,
                                          textAlignment: .center)
    
    private lazy var descLabel = UILabel(font: Font.light.of(size: 14),
                                         numberOfLines: 0,
                                         text: "key0193".localized() + "\n\n" + "key0194".localized(),
                                         textColor: Color.txtTitle)
    
    private lazy var buttonStackView = UIStackView(axis: .vertical,
                                                   spacing: 12)

//    private lazy var phoneButton = ThemeableButton(titleFont: Font.medium.of(size: 14),
//                                                   title: "key0195".localized(),
//                                                   normalTitleColor: Color.txtInverted,
//                                                   normalBgColor: Color.btnPrimary,
//                                                   cornerRadius: 22)
    
    private lazy var emailButton = ThemeableButton(titleFont: Font.medium.of(size: 14),
                                                   title: "key0196".localized(),
                                                   normalTitleColor: Color.txtInverted,
                                                   normalBgColor: Color.btn2nd,
                                                   cornerRadius: 22)
    
    // MARK: Properties
    var viewModel: TwoFALandingViewModel!
    private let successActionTitle: String
    private let requestOTP = PublishSubject<OTPAuthType>()
    
    // MARK: Life cycle
    init(successActionTitle: String) {
        self.successActionTitle = successActionTitle
        
        super.init(nibName: nil, bundle: nil)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    public override func viewDidLoad() {
        super.viewDidLoad()
        
        setupUI()
        bindActions()
        bindViewModelLogic()
    }
    
    public func setupUI() {
        let screenHeight = UIScreen.main.bounds.height
        
        view.addSubviews([stackView,
                          backButton])
        
        stackView.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.left.equalTo(12)
            make.right.equalTo(-12)
        }
        
        stackView.addArrangedSubviews([bannerImageView,
                                       titleLabel,
                                       descLabel,
                                       emailButton])
        
        bannerImageView.snp.makeConstraints { make in
            make.height.equalTo(screenHeight*0.18)
        }
        
        stackView.setCustomSpacing(screenHeight*0.04, after: bannerImageView)
        stackView.setCustomSpacing(16, after: titleLabel)
        stackView.setCustomSpacing(screenHeight*0.04, after: descLabel)
        
//        phoneButton.snp.makeConstraints { make in
//            make.height.equalTo(44)
//        }
        
//        stackView.setCustomSpacing(12, after: phoneButton)
        
        emailButton.snp.makeConstraints { make in
            make.height.equalTo(44)
        }
        
        backButton.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide)
            make.left.equalToSuperview()
            make.width.height.equalTo(44)
        }
    }
    
    func bindViewModelLogic() {
        let input = TwoFALandingViewModel.Input(onRequestOTP: requestOTP.asObservable())
        
        let output = viewModel.transform(input: input)
        
        output.loading.drive(onNext: { [weak self] loading in
            loading ? self?.showLoading() : self?.hiddenLoading()
        }).disposed(by: disposeBag)
        
        output.error.drive(onNext: { [weak self] error in
            let message = (error as? APIError)?.message ?? error.localizedDescription
            self?.showAlert(title: "key0895".localized(),
                            message: message,
                            completion: {
                self?.backPress()
            })
        }).disposed(by: disposeBag)
        
        output.didRequestOTP.drive(onNext: { [unowned self] type, response in
            viewModel.navigate(to: .otp(type: viewModel.getOTPScreenType(type),
                                        otpResponse: response,
                                        completion: { [unowned self] _ in
                viewModel.navigate(to: .twoFAEnabled(type: type,
                                                     actionTitle: successActionTitle))
            }))
        }).disposed(by: disposeBag)
    }
    
    public func bindActions() {
//        phoneButton.rx.tap.subscribe(onNext: { [unowned self] in
//            requestOTP.onNext(.mobile)
//        }).disposed(by: disposeBag)
        
        emailButton.rx.tap.subscribe(onNext: { [unowned self] in
            requestOTP.onNext(.email)
        }).disposed(by: disposeBag)
        
        backButton.rx.tap
            .subscribe(onNext: { [unowned self] in
                LocalPreference.clearCurrentSession()
                backPress()
            }).disposed(by: disposeBag)
    }
}
