//
//  TwoFAEnabledViewModel.swift
//  Authentication
//
//  Created by <PERSON><PERSON><PERSON> on 3/4/67.
//
import APILayer
import Core
import CUIModule
import RxCocoa
import RxSwift
import RxR<PERSON>y
import XCoordinator
import SharedData

public class TwoFAEnabledViewModel: AnyViewModel {

    private let router: UnownedRouter<AuthenticationRoute>

    public struct Input {
        let onViewAppear: Observable<Void>
        let startExplore: Observable<Void>
    }

    public struct Output {
        let displayData: Driver<Void>
        let startExplore: Driver<OTPAuthType>
    }

    private let type: OTPAuthType

    init(
        router: UnownedRouter<AuthenticationRoute>,
        type: OTPAuthType
    ) {
        self.router = router
        self.type = type
    }

    public func transform(input: Input) -> Output {

        let displayData = input.onViewAppear

        let startExplore = input.startExplore
            .map { self.type }

        return Output(
            displayData: displayData.asDriverOnErrorNever(),
            startExplore: startExplore.asDriverOnErrorNever()
        )
    }

    func navigate(to route: AuthenticationRoute) {
        router.trigger(route)
    }
}
