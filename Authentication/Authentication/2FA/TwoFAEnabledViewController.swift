//
//  TwoFAEnabledViewController.swift
//  Authentication
//
//  Created by <PERSON><PERSON><PERSON> on 3/4/67.
//
import CUIModule
import UIKit
import SnapKit
import RxSwift
import Core

final class TwoFAEnabledViewController: BaseViewController, VMView {

    private lazy var stackView = UIStackView(
        axis: .vertical,
        spacing: 0
    )

    private lazy var bannerImageView = UIImageView(
        contentMode: .scaleAspectFit,
        image: .image(named: "merit_enable_success")
    )

    private lazy var titleLabel = UILabel(
        font: Font.semiBold.of(size: 18),
        text: "key0209".localized(),
        textColor: Color.txtTitle,
        textAlignment: .center
    )

    private lazy var descLabel: UILabel = {
        let label = UILabel(
            font: Font.light.of(size: 14),
            numberOfLines: 0,
            text: "key0210".localized(),
            textColor: Color.txtParagraph
        )
        label.setLineSpacing(lineHeightMultiple: 1.2)
        return label
    }()

    private lazy var startButton = ThemeableButton(
        titleFont: Font.medium.of(size: 14),
        title: "key0211".localized(),
        normalTitleColor: Color.txtInverted,
        normalBgColor: Color.btn2nd,
        cornerRadius: 22
    )

    var viewModel: TwoFAEnabledViewModel!

    init(actionTitle: String) {
        super.init(nibName: nil, bundle: nil)
        startButton.setTitle(actionTitle, for: .normal)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        bindActions()
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        navigationBarHidden(true)
    }

    func setupUI() {
        let screenHeight = UIScreen.main.bounds.height

        view.addSubviews([stackView, startButton])
        stackView.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide.snp.top).offset(screenHeight*0.13)
            make.left.equalTo(12)
            make.right.equalTo(-12)
        }

        startButton.snp.makeConstraints { make in
            make.left.equalTo(12)
            make.right.equalTo(-12)
            make.bottom.equalTo(view.safeAreaLayoutGuide.snp.bottom).offset(-16)
            make.height.equalTo(44)
        }

        stackView.addArrangedSubviews([
            bannerImageView, titleLabel,
            descLabel
        ])

        bannerImageView.snp.makeConstraints { make in
            make.height.equalTo(screenHeight*0.18)
        }

        stackView.setCustomSpacing(screenHeight*0.04, after: bannerImageView)
        stackView.setCustomSpacing(16, after: titleLabel)
    }

    func bind(viewModel: TwoFAEnabledViewModel) {
        self.viewModel = viewModel

        let input = TwoFAEnabledViewModel.Input(
            onViewAppear: rx.viewWillAppear.take(1).mapToVoid(),
            startExplore: startButton.rx.tap.mapToVoid()
        )

        let output = viewModel.transform(input: input)

        output.displayData.drive(onNext: { [weak self] in
            self?.displayUI()
        }).disposed(by: disposeBag)

        output.startExplore.drive(onNext: { [weak self] in
            self?.viewModel.navigate(to: .twoFASetupCompleted(type: $0))
        }).disposed(by: disposeBag)
    }

    func bindActions() {
    }

    private func displayUI() {

    }
}
