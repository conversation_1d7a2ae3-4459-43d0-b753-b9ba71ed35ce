//
//  TwoFALandingViewModel.swift
//  Authentication
//
//  Created by <PERSON><PERSON><PERSON> on 3/1/67.
//

import APILayer
import Core
import CUIModule
import RxCocoa
import RxSwift
import Rx<PERSON><PERSON>y
import XCoordinator
import SharedData

final class TwoFALandingViewModel: AnyViewModel {
    
    public struct Input {
        let onRequestOTP: Observable<OTPAuthType>
    }
    
    public struct Output {
        let loading: Driver<Bool>
        let error: Driver<Error>
        let didRequestOTP: Driver<(type: OTPAuthType, response: OTPRequestEndPoint.Response)>
    }
    
    // MARK: Properties
    private let router: UnownedRouter<AuthenticationRoute>
    private let isSetUp: Bool
    
    init(router: UnownedRouter<AuthenticationRoute>,
         isSetUp: Bool) {
        self.router = router
        self.isSetUp = isSetUp
    }
    
    public func transform(input: Input) -> Output {
        let activityIndicator = ActivityIndicator()
        let errorTracker = ErrorTracker()
        
        let requestOTP = input.onRequestOTP
            .flatMap { [unowned self] in
                OTPRequestEndPoint.service.requestOTP(for: getOTPScreenType($0))
                    .track(activityIndicator, error: errorTracker)
            }
            .withLatestFrom(input.onRequestOTP) { ($1, $0) }
            .map { (type: $0, response: $1) }
        
        return Output(loading: activityIndicator.asDriver(),
                      error: errorTracker.asDriver(),
                      didRequestOTP: requestOTP.asDriverOnErrorNever())
    }
}

// MARK: - Internal
extension TwoFALandingViewModel {
    
    func navigate(to route: AuthenticationRoute) {
        router.trigger(route)
    }
    
    func getOTPScreenType(_ authType: OTPAuthType) -> OTPScreenType {
        isSetUp ? .twoFactorSetUp(type: authType) : .twoFactorLogin(type: authType)
    }
}
