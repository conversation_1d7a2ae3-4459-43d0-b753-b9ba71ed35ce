//
//  BiometricSettingViewController.swift
//  Authentication
//
//  Created by <PERSON><PERSON> on 3/8/67.
//
import Core
import CUIModule
import RxSwift
import RxCocoa
import Utils

final class BiometricSettingViewController: BaseViewController, VMView {

    private lazy var navigationView = AuthNavigationView(title: "ENABLE BIOMETRIC")

    private lazy var container = UIView()

    private lazy var bioSetupViewController: BiometricSetupViewController = {
        let viewController = BiometricSetupViewController(cancelActionTitle: "key0273".localized())
        viewController.bind(viewModel: bioSetupViewModel)
        return viewController
    }()
    private lazy var bioSetupViewModel = BiometricSetupViewModel(router: viewModel.router)

    var viewModel: BiometricSettingViewModel!

    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        bindActions()
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        navigationBarHidden(true)
    }

    func setupUI() {
        view.addSubviews([navigationView, container])
        navigationView.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide.snp.top)
            make.left.right.equalToSuperview()
        }
        container.snp.makeConstraints { make in
            make.top.equalTo(navigationView.snp.bottom).offset(24)
            make.left.right.bottom.equalToSuperview()
        }

        addChild(controller: bioSetupViewController, container: container)
    }

    func bindActions() {
        navigationView.rx.back.subscribe(onNext: { [weak self] in
            self?.navigationController?.popViewController(animated: true)
        }).disposed(by: disposeBag)

        bioSetupViewModel.onCompleted.subscribe(onNext: { [weak self] in
            if $0 {
                self?.viewModel.navigate(to: .pinSetupCompleted(
                    type: .bioOnly,
                    actionTitle: "GOT IT!",
                    showNew: false
                ))
            } else {
                self?.navigationController?.popViewController(animated: true)
            }
        }).disposed(by: disposeBag)
    }

    func bind(viewModel: BiometricSettingViewModel) {
        self.viewModel = viewModel

        let input = BiometricSettingViewModel.Input(
            onViewAppear: rx.viewWillAppear.mapToVoid()
        )

        let output = viewModel.transform(input: input)

        output.displayData.drive(onNext: { [weak self] in
            self?.displayUI()
        }).disposed(by: disposeBag)
    }
}
// MARK: - Update UI
extension BiometricSettingViewController {

    private func displayUI() {
    }
}
