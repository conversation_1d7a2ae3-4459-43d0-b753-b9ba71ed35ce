//
//  BiometricSettingViewModel.swift
//  Authentication
//
//  Created by <PERSON><PERSON> on 3/8/67.
//
import APILayer
import Core
import CUIModule
import RxCocoa
import RxSwift
import RxR<PERSON>y
import XCoordinator
import SharedData

public class BiometricSettingViewModel: AnyViewModel {

    let router: UnownedRouter<AuthenticationRoute>

    public struct Input {
        let onViewAppear: Observable<Void>
    }

    public struct Output {
        let loading: Driver<Bool>
        let error: Driver<Error>
        let displayData: Driver<Void>
    }

    // Output to Coordinator
    public let onCompleted = PublishSubject<Bool>()

    init(router: UnownedRouter<AuthenticationRoute>) {
        self.router = router
    }

    public func transform(input: Input) -> Output {
        let activityIndicator = ActivityIndicator()
        let errorTracker = ErrorTracker()

        let displayData = input.onViewAppear

        return Output(
            loading: activityIndicator.asDriver(),
            error: errorTracker.asDriver(),
            displayData: displayData.asDriverOnErrorNever()
        )
    }

    func navigate(to route: AuthenticationRoute) {
        self.router.trigger(route)
    }
}
