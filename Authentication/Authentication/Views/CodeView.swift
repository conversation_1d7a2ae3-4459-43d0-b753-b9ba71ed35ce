//
//  CodeView.swift
//  Authentication
//
//  Created by <PERSON><PERSON> on 3/4/67.
//
import UIKit
import SnapKit
import RxSwift
import CUIModule
import Utils

class CodeView: UIView {

    private lazy var stackView = UIStackView(
        axis: .vertical,
        spacing: 4
    )

    private lazy var titleLabel = UILabel(
        font: Font.semiBold.of(size: 12),
        textColor: Color.txtLabel,
        lines: 0
    )

    private lazy var descLabel = UILabel(
        font: Font.medium.of(size: 12),
        textColor: Color.txtParagraph,
        lines: 0
    )

    private lazy var codeContainer = UIView()

    fileprivate lazy var codeInputView = OTPInputView(style: OTPInputView.Style(
        otpBgColor: Color.bgDefault,
        focusAtStart: false
    ))

    public var code: String { codeInputView.code }

    init(title: String, desc: String) {
        super.init(frame: .zero)
        setup()
        setData(title: title, desc: desc)
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setup()
    }

    private func setup() {
        setUpViews()
    }

    private func setUpViews() {
        backgroundColor = Color.bgDefaultTone
        roundCorners(radius: 4)

        addSubview(stackView)
        stackView.snp.makeConstraints { make in
            make.top.left.equalTo(12)
            make.bottom.right.equalTo(-12)
        }
        stackView.addArrangedSubviews([titleLabel, descLabel, codeContainer])
        stackView.setCustomSpacing(16, after: descLabel)

        codeContainer.addSubview(codeInputView)
        codeInputView.snp.makeConstraints { make in
            make.top.bottom.equalToSuperview()
            make.centerX.equalToSuperview()
        }
    }

    func setData(title: String, desc: String) {
        titleLabel.text = title
        descLabel.text = desc
    }

    func setError(_ showError: Bool) {
        codeInputView.setErrorState(isError: showError)
    }
}
// MARK: - Reactive
extension Reactive where Base: CodeView {

    var codeFilled: Observable<Bool> { base.codeInputView.rx.codeFilled }
    
    var complete: Observable<String> { base.codeInputView.rx.complete }
}
