//
//  QRView.swift
//  Authentication
//
//  Created by <PERSON><PERSON><PERSON> on 3/4/67.
//
import UIKit
import SnapKit
import RxSwift
import CUIModule
import Utils

class QRView: UIView {

    private lazy var stackView = UIStackView(
        axis: .vertical,
        spacing: 4
    )

    private lazy var titleLabel = UILabel(
        font: Font.semiBold.of(size: 12),
        textColor: Color.txtLabel,
        lines: 0
    )

    private lazy var descLabel = UILabel(
        font: Font.medium.of(size: 12),
        textColor: Color.txtParagraph,
        lines: 0
    )

    private lazy var qrImageView = UIImageView(
        contentMode: .scaleAspectFit
    )

    init(title: String, desc: String, qrImage: UIImage?) {
        super.init(frame: .zero)
        setup()
        setData(title: title, desc: desc, qrImage: qrImage)
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setup()
    }

    private func setup() {
        setUpViews()
    }

    private func setUpViews() {
        backgroundColor = Color.bgDefaultTone
        roundCorners(radius: 4)

        addSubview(stackView)
        stackView.snp.makeConstraints { make in
            make.top.left.equalTo(12)
            make.bottom.right.equalTo(-12)
        }
        stackView.addArrangedSubviews([titleLabel, descLabel, qrImageView])
        stackView.setCustomSpacing(16, after: descLabel)

        qrImageView.snp.makeConstraints { make in
            make.height.equalTo(175)
        }
    }

    func setData(title: String, desc: String, qrImage: UIImage?) {
        titleLabel.text = title
        descLabel.text = desc
        qrImageView.image = qrImage
    }
}
