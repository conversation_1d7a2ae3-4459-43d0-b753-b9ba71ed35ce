//
//  AuthNavigationView.swift
//  Authentication
//
//  Created by <PERSON><PERSON><PERSON> on 1/9/67.
//
import UIKit
import SnapKit
import RxSwift
import CUIModule

class AuthNavigationView: UIView {
    /// Content View
    private lazy var contentView = UIView()
    /// Back Button
    fileprivate lazy var backButton = UIButton(normalImage: .image(named: "merit_ic_back"))

    private lazy var titleStackView = UIStackView(
        axis: .horizontal,
        spacing: 4
    )

    /// TItle Label
    private lazy var titleLabel = UILabel(
        font: Font.semiBold.of(size: 14),
        textColor: Color.txtTitle
    )

    private lazy var iconView = ThemeableImageView(
        contentMode: .scaleAspectFit,
        themeType: .textHighlight
    )

    private var dropShadowOpacity: Float {
        return currentAppTheme == .light ? 0.07 : 0.5
    }

    override public init(frame: CGRect) {
        super.init(frame: frame)
        setup()
    }

    public required init?(coder: NSCoder) {
        super.init(coder: coder)
        setup()
    }

    public init(icon: UIImage? = nil, title: String? = nil) {
        super.init(frame: .zero)
        setup()
        setIcon(icon)
        setTitle(title)
    }

    public init(icon: UIImage? = nil, fullText: String, highlightText: [String]) {
        super.init(frame: .zero)
        setup()
        setIcon(icon)
        setTitle(fullText: fullText, highlightText: highlightText)
    }

    override func layoutSubviews() {
        super.layoutSubviews()
        self.layer.shadowOpacity = dropShadowOpacity
    }

    private func setup() {
        setUpViews()
        setUpShadow()
    }

    private func setUpViews() {
        backgroundColor = Color.bgDefault
        // Content View
        addSubview(contentView)
        contentView.addSubviews([backButton, titleStackView])
        contentView.snp.makeConstraints { make in
            make.top.equalTo(self.safeAreaLayoutGuide.snp.top)
            make.left.equalTo(12)
            make.right.equalTo(-12)
            make.height.equalTo(44)
            make.bottom.equalToSuperview()
        }
        backButton.snp.makeConstraints { make in
            make.top.bottom.left.equalToSuperview()
            make.width.equalTo(24)
        }
        titleStackView.snp.makeConstraints { make in
            make.centerX.centerY.equalToSuperview()
        }

        titleStackView.addArrangedSubviews([iconView, titleLabel])
        iconView.snp.makeConstraints { make in
            make.width.equalTo(18)
        }
    }

    private func setUpShadow() {
        self.dropShadow(
            alpha: 1, opacity: dropShadowOpacity,
            offset: .init(width: 0, height: 1),
            radius: 1
        )
    }

    public func setTitle(_ title: String?) {
        titleLabel.text = title
    }

    public func setIcon(_ icon: UIImage?) {
        iconView.image = icon
        iconView.isHidden = icon == nil
    }

    public func setTitle(fullText: String, highlightText: [String]) {
        titleLabel.highlight(
            fullText,
            normalColor: Color.txtTitle,
            normalFont: Font.semiBold.of(size: 14),
            highlightText: highlightText,
            highlightColor: Color.txtParagraph,
            highlightFont: Font.regular.of(size: 14)
        )
    }

}

extension Reactive where Base: AuthNavigationView {

    var back: Observable<Void> { base.backButton.rx.tap.mapToVoid() }
}
