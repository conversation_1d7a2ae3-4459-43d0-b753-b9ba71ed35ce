//
//  DeleteMessageEndPoint.swift
//  APILayer
//
//  Created by <PERSON><PERSON> on 27/3/2567 BE.
//

import RxSwift
import Storage
import Networking
import SharedData

public struct MessageEditEndPoint: NetworkEndpoint {

    public static var service = MessageEditEndPoint()

    public var path: String = "/user/message/edit"
    public var method: RequestMethod = .put
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?

    public struct Request: Codable {
        public let messageId: Int
        public let status: String

        public init(
            messageId: Int,
            status: String = "READ"
        ) {
            self.messageId = messageId
            self.status = status
        }
    }

    public struct Response: Codable {
        public init() {}
    }
    
    public mutating func call(parameter: Request) -> Observable<Response> {
        return request(parameters: parameter)
    }
}
