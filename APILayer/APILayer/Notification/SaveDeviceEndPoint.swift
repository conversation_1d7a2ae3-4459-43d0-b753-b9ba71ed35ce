//
//  SaveDeviceEndPoint.swift
//  APILayer
//
//  Created by <PERSON> on 10/12/24.
//

import Networking
import RxSwift

public struct SaveDeviceEndPoint: NetworkEndpoint {
    
    public static var service = SaveDeviceEndPoint()
    
    public var path: String = "/user/device"
    public var method: RequestMethod = .put
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?
    
    public struct Request: Codable {
        private let deviceId: String
        private let jpushRid: String
        
        init(deviceId: String, jpushRid: String) {
            self.deviceId = deviceId
            self.jpushRid = jpushRid
        }
    }
    
    public struct Response: Codable {}
    
    public mutating func call(with deviceId: String, jpushRid: String) -> Observable<Response> {
        request(parameters: Request(deviceId: deviceId, jpushRid: jpushRid))
    }
}
