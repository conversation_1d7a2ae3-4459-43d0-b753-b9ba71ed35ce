//
//  DeleteNotificationEndPoint.swift
//  APILayer
//
//  Created by <PERSON> on 10/12/24.
//

import Networking
import RxSwift

public struct DeleteNotificationEndPoint: NetworkEndpoint {
    
    public static var service = DeleteNotificationEndPoint()
    
    public var path: String = "/user/notification/delete"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?
    
    public struct Request: Codable {
        private let idList: [Int]
        
        init(idList: [Int]) {
            self.idList = idList
        }
    }
    
    public struct Response: Codable {}
    
    public mutating func call(with idList: [Int]) -> Observable<Response> {
        request(parameters: Request(idList: idList))
    }
}
