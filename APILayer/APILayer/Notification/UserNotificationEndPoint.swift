//
//  UserNotificationEndPoint.swift
//  APILayer
//
//  Created by <PERSON> on 21/11/24.
//

import Networking
import RxSwift
import Storage

public struct UserNotificationEndPoint: NetworkEndpoint {
    
    public static var service = UserNotificationEndPoint()
    
    public var path: String = "/user/notification"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?
    
    public struct Request: Codable {
        private let limit: Int
        private let messageType: String
        
        public init(limit: Int, messageType: MessageType) {
            self.limit = limit
            self.messageType = messageType.rawValue
        }
    }
    
    public struct Response: Codable {
        public let list: [Message]?
    }
    
    public struct Message: Codable {
        public let id: Int?
        public let datetime: Int? // Milliseconds
        public let title: String?
        public let subTitle: String?
        public let content: String?
        public let messageType: String?
        public let relateId: String?
        public let relateType: String?
        public let tag: String?
        public let businessType: String?
        public let isRead: Bool?
    }
    
    public enum MessageType: String, Codable {
        case notification = "NOTIFICATIONS"
        case message = "MESSAGE"
    }
    
    public mutating func call(with limit: Int = 0, messageType: MessageType) -> Observable<Response> {
        request(parameters: Request(limit: limit, messageType: messageType))
    }
}
