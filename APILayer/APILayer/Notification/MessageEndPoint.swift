//
//  MessageEndPoint.swift
//  APILayer
//
//  Created by <PERSON><PERSON> on 25/3/2567 BE.
//

import RxSwift
import Storage
import Networking
import SharedData

public struct MessageEndPoint: NetworkEndpoint {

    public static var service = MessageEndPoint()

    public var path: String = "/user/message"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?

    public struct Request: Codable {
        public let pageNum: Int
        public let pageSize: Int

        public init(
            pageNum: Int = 1,
            pageSize: Int = 10
        ) {
            self.pageNum = pageNum
            self.pageSize = pageSize
        }
    }

    public struct Response: Codable {
        public let messageList: [Message]?
        public let total: Int?
    }
    
    public struct Message: Codable {
        public let action: String?
        public let createdAt: Int64?
        public let description: String?
        public let id: Int?
        public let title: String?
        public let updatedAt: Int64?
        public var status: String
    }
    
    public mutating func call(parameter: Request) -> Observable<Response> {
//        guard let result = MockData.messageList.data(type: Response.self) else {
//            return .never()
//        }
//        return .just(result).delay(.milliseconds(500), scheduler: MainScheduler.instance)
        return request(parameters: parameter)
    }
}
