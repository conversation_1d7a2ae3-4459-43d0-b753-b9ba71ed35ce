//
//  UserNotificationReadEndPoint.swift
//  APILayer
//
//  Created by <PERSON> on 21/11/24.
//

import Networking

public struct UserNotificationReadEndPoint: NetworkEndpoint {
    
    public static var service = UserNotificationReadEndPoint()
    
    public var path: String = "/user/notification/read"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?
    
    public struct Request: Codable {
        private let idList: [Int]
        private let messageType: String
        private let isReadAll: Bool
        
        public init(idList: [Int], messageType: UserNotificationEndPoint.MessageType, isReadAll: Bool) {
            self.idList = idList
            self.messageType = messageType.rawValue
            self.isReadAll = isReadAll
        }
    }
    
    public struct Response: Codable {
        public let list: [UserNotificationEndPoint.Message]?
    }
}
