//
//  TradeAccountTypesEndPoint.swift
//  APILayer
//
//  Created by <PERSON><PERSON><PERSON> on 3/21/67.
//
import RxSwift
import Storage
import Networking
import SharedData

public struct TradeAccountTypesEndPoint: NetworkEndpoint {

    public static var service = TradeAccountTypesEndPoint()

    public var path: String = "/common/account-type"
    public var method: RequestMethod = .get
    public var encoding: RequestEncoding = .query
    public var headers: RequestHeaders?

    public struct Request: Codable {}

    public struct Response: Codable {
        public let accountTypeList: [AccountType]?
    }

    public struct AccountType: Codable {
        public let accountType, accountTypeName, description, riskLevel: String?
        public let tag: [String]?
    }

    public mutating func call() -> Observable<Response> {
        return request(parameters: Request())
    }
}
