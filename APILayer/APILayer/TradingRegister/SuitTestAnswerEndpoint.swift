//
//  SuitTestAnswerEndpoint.swift
//  APILayer
//
//  Created by <PERSON><PERSON> on 2/28/67.
//
import RxSwift
import Storage
import Networking
import SharedData

public struct SuitTestAnswerEndpoint: NetworkEndpoint {

    public static var service = SuitTestAnswerEndpoint()

    public var path: String = "/customer/suit-test"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?

    public struct Request: Codable {
        public let id: Int
        public let isDryRun: Bool?
        public let questionList: [Quiz]

        public init(id: Int = 1, isDryRun: Bool? = nil, questionList: [Quiz]) {
            self.id = id
            self.isDryRun = isDryRun
            self.questionList = questionList
        }

    }

    public struct Quiz: Codable {
        public var id: Int
        public var selectedAnswerList: [Int]

        public init(id: Int, selectedAnswerList: [Int]) {
            self.id = id
            self.selectedAnswerList = selectedAnswerList
        }
    }

    public struct Response: Codable {
        public let score: String?
    }

    public mutating func call(parameter: Request) -> Observable<Response> {
        return .just(Response(score: "41"))
//        return request(parameters: parameter)
    }
}
