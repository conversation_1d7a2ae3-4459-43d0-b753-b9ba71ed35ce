//
//  CustomerFileEndpoint.swift
//  APILayer
//
//  Created by <PERSON><PERSON> on 2/28/67.
//
import RxSwift
import Storage
import Networking
import SharedData

public struct CustomerFileEndpoint: NetworkEndpoint {

    public static var service = CustomerFileEndpoint()

    public var path: String = "/customer/file"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?

    public struct Request: Codable {}

    public struct Response: Codable {
        public let dosKey: String
    }

    public mutating func upload(data: Data) -> Observable<Response> {
//        return uploadBinary(data: data)
        .just(Response(dosKey: "mock docKey"))
    }
}
