//
//  SuitTestQuestionEndpoint.swift
//  APILayer
//
//  Created by <PERSON><PERSON> on 2/28/67.
//
import RxSwift
import Storage
import Networking
import SharedData

public struct SuitTestQuestionEndpoint: NetworkEndpoint {

    public static var service = SuitTestQuestionEndpoint()

    public var path: String = "/common/questionnaire"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?

    public struct Request: Codable {
        public let pageNum: Int
        public let pageSize: Int
        public let questionnaireCode: String

        public init(
            pageNum: Int = 1,
            pageSize: Int = 10,
            questionnaireCode: String = "SUIT_TEST"
        ) {
            self.pageNum = pageNum
            self.pageSize = pageSize
            self.questionnaireCode = questionnaireCode
        }

    }

    public struct Response: Codable {
        public let code, name: String?
        public let id, questionCount: Int?
        public let questionList: [Question]?
    }

    public struct Question: Codable {
        public let answerList: [Answer]?
        public let content: String?
        public let id, sort: Int?
    }

    public struct Answer: Codable {
        public let content: String?
        public let id, sort: Int?
        public let score: String?
    }

    public mutating func call(parameter: Request) -> Observable<Response> {
        return .just(Response(code: "", name: "", id: 0, questionCount: 0, questionList: []))
//        return request(parameters: parameter)
    }
}
