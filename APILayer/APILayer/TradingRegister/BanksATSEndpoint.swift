//
//  BanksATSEndpoint.swift
//  APILayer
//
//  Created by <PERSON><PERSON> on 2/28/67.
//
import RxSwift
import Storage
import Networking
import SharedData

public struct BanksATSEndpoint: NetworkEndpoint {

    public static var service = BanksATSEndpoint()

    public var path: String = "/common/bank/ats"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?

    public struct Request: Codable {}

    public struct Response: Codable {
        public let list: [BankAccount]?
    }

    public struct BankAccount: Codable {
        public let bankCode: String?
        public let bankLogo: String?
        public let bankShortName: String?
    }

    public mutating func call() -> Observable<Response> {
        return request(parameters: Request())
    }
}
