//
//  TradingAccountOpeningEndPoint.swift
//  APILayer
//
//  Created by <PERSON><PERSON><PERSON> on 2/28/67.
//
import RxSwift
import Storage
import Networking
import SharedData

public struct TradingAccountOpeningEndPoint: NetworkEndpoint {

    public static var service = TradingAccountOpeningEndPoint()

    public var path: String = "/customer/account-opening"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?

    public struct Request: Codable {
        public let accountTypeList: [String]
        public let documentKeyList: [String]

        public init(
            accountTypeList: [String],
            documentKeyList: [String]
        ) {
            self.accountTypeList = accountTypeList
            self.documentKeyList = documentKeyList
        }
    }

    public struct Response: Codable {}

    public mutating func call(parameter: Request) -> Observable<Response> {
//        return request(parameters: parameter)
        .just(Response())
    }
}
