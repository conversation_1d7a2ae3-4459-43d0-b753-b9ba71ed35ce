//
//  MonthlyReportGenerationEndPoint.swift
//  APILayer
//
//  Created by <PERSON> on 27/03/2024.
//

import Networking

public struct MonthlyReportGenerationEndPoint: NetworkEndpoint {
    
    public static var service = MonthlyReportGenerationEndPoint()
    
    public var path: String = "/account/monthly-report"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?
    
    public struct Request: Codable {
        private let accountType: String
        private let generationType: String
        private let start: Int
        private let end: Int
        
        public init(accountType: String, generationType: String, start: Int, end: Int) {
            self.accountType = accountType
            self.generationType = generationType
            self.start = start
            self.end = end
        }
    }
    
    public struct Response: Codable {
        public let fileKey: String?
        public let path: String?
    }
}
