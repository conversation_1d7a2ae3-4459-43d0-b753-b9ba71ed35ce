//
//  UserStatementGenerateEndPoint.swift
//  APILayer
//
//  Created by <PERSON> on 27/11/24.
//

import Networking

public struct UserStatementGenerateEndPoint: NetworkEndpoint {
    
    public static var service = UserStatementGenerateEndPoint()
    
    public var path: String = "/user/statement/generate"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?
    
    public struct Request: Codable {
        private let reportType: String
        private let statementDate: Int
        private let statementType: String
        private let currency: String
        private let exchangeRate: String
        
        public init(reportType: String,
                    statementDate: Int,
                    statementType: String,
                    currency: String,
                    exchangeRate: String) {
            self.reportType = reportType
            self.statementDate = statementDate
            self.statementType = statementType
            self.currency = currency
            self.exchangeRate = exchangeRate
        }
    }
    
    public struct Response: Codable {
        public let message: String?
        public let fileKey: String?
    }
}

