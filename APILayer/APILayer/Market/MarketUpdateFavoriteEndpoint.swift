//
//  MarketUpdateFavoriteEndpoint.swift
//  APILayer
//
//  Created by <PERSON><PERSON><PERSON> on 12/6/66.
//
import RxSwift
import Storage
import Networking

public struct MarketUpdateFavoriteEndpoint: NetworkEndpoint {
    
    public static var service = MarketUpdateFavoriteEndpoint()
    
    public var path: String = "/user/instrument/favorite/update"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?
    
    public struct Request: Codable {
        private let instrumentId: Int
        
        public init(instrumentId: Int) {
            self.instrumentId = instrumentId
        }
    }
    
    public struct Response: Codable {}
    
    public mutating func update(for instrumentId: Int) -> Observable<Response> {
        request(parameters: Request(instrumentId: instrumentId))
    }
}
