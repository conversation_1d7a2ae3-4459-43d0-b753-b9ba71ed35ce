//
//  MarketSimpleChartEndPoint.swift
//  APILayer
//
//  Created by <PERSON><PERSON><PERSON> on 1/11/67.
//
import RxSwift
import Storage
import Networking

public struct MarketSimpleChartEndPoint: NetworkEndpoint {
    
    public static var service = MarketSimpleChartEndPoint()
    
    public var path: String = "/market/chart/simple"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?
    
    public struct Request: Codable {
        private let interval: String
        private let instrumentId: Int
        
        public init(interval: String, instrumentId: Int) {
            self.interval = interval
            self.instrumentId = instrumentId
        }
    }
    
    public struct Response: Codable {
        public var candles: [[String]]?
    }
    
    public mutating func call(parameter: Request) -> Observable<Response> {
        return request(parameters: parameter)
    }
}
