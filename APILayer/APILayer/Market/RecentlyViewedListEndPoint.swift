//
//  RecentlyViewedListEndPoint.swift
//  APILayer
//
//  Created by <PERSON><PERSON><PERSON> on 1/16/67.
//
import RxSwift
import Storage
import Networking
import SharedData

public struct RecentlyViewedListEndPoint: NetworkEndpoint {
    
    public static var service = RecentlyViewedListEndPoint()
    
    public var path: String = "/user/instrument/view"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .query
    public var headers: RequestHeaders?
    public var auth: AuthType = .basic
    
    public struct Request: Codable {}
    
    public struct Response: Codable {
        public var instrumentList: [MarketInstrument]
    }
    
    public mutating func call() -> Observable<Response> {
        request(parameters: Request())
    }
}
