//
//  MarketInstrumentDetailEndPoint.swift
//  APILayer
//
//  Created by Chung C<PERSON> on 26/10/24.
//

import Networking
import SharedData

public struct MarketInstrumentDetailEndPoint: NetworkEndpoint {
    
    public static var service = MarketInstrumentDetailEndPoint()
    
    public var path: String = "/market/instrument/detail"
    
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?
    
    public struct Request: Codable {
        private let instrumentId: Int
        
        public init(instrumentId: Int) {
            self.instrumentId = instrumentId
        }
    }
    
    public typealias Response = MarketInstrumentDetail
}

