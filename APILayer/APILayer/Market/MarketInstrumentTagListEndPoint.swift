//
//  MarketInstrumentTagListEndPoint.swift
//  APILayer
//
//  Created by Chung Cr on 25/03/2024.
//

import Networking

public struct MarketInstrumentTagListEndPoint: NetworkEndpoint {
    
    public static var service = MarketInstrumentTagListEndPoint()
    
    public var path: String = "/market/list/filter/tag"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?
    
    public struct Request: Codable {
        private let tagLabel: String
        
        public init(tagLabel: String) {
            self.tagLabel = tagLabel
        }
    }
    
    public struct Response: Codable {
        public let list: [InstrumentTag]?
    }
    
    public struct InstrumentTag: Codable {
        public let tagId: Int?
        public let tagLabel: String?
    }
}
