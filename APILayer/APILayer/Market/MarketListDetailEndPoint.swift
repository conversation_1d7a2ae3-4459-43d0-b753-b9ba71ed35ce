//
//  MarketListDetailEndPoint.swift
//  APILayer
//
//  Created by <PERSON><PERSON><PERSON> on 11/22/66.
//
import RxSwift
import Storage
import Networking
import SharedData

public struct MarketListDetailEndpoint: NetworkEndpoint {
    
    public static var service = MarketListDetailEndpoint()
    
    public var path: String = "/market/list/detail"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?
    public var auth: AuthType = .basic
    
    public struct Request: Codable {
        public let listCode: String?
        private var allocationList: [String]?
        private var riskLevelList: [String]?
        private var productTypeList: [String]?
        private var productCategoryList: [String]?
        
        public init(listCode: String? = nil,
                    allocationList: [String]? = nil,
                    riskLevelList: [String]? = nil,
                    productTypeList: [String]? = nil,
                    productCategoryList: [String]? = nil) {
            self.listCode = listCode
            self.allocationList = allocationList
            self.riskLevelList = riskLevelList
            self.productTypeList = productTypeList
            self.productCategoryList = productCategoryList
        }
    }
    
    public struct Response: Codable {
        public var instruments: [MarketInstrument]
    }
    
    public mutating func call(parameter: Request) -> Observable<Response> {
        return request(parameters: parameter)
    }
}
