//
//  MarketWatchListEndPoint.swift
//  APILayer
//
//  Created by <PERSON> on 23/11/24.
//

import Networking
import RxSwift
import SharedData

public struct MarketWatchListEndPoint: NetworkEndpoint {
    
    public static var service = MarketWatchListEndPoint()
    
    public var path: String = "/market/watch/list"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?
    
    public struct Request: Codable {
        private let topType: String
        
        init(sortingType: String) {
            self.topType = sortingType
        }
    }
    
    public struct Response: Codable {
        public let assetList: [WatchListAsset]?
    }
    
    public mutating func call(with sortingType: WatchListSortingType) -> Observable<[WatchListAsset]> {
        request(parameters: Request(sortingType: sortingType.rawValue))
            .map { $0.assetList ?? [] }
    }
}
