//
//  MarketProfileFinancialsEndPoint.swift
//  APILayer
//
//  Created by Chung <PERSON> on 06/12/2023.
//

import Foundation
import Networking

public struct MarketProfileFinancialsEndPoint: NetworkEndpoint {
    public static var service = MarketProfileFinancialsEndPoint()
    
    public var path: String = "/market/profile/financials"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?
    
    public struct Request: Codable {
        private let exchange: String
        private let symbol: String
        
        public init(exchange: String, symbol: String) {
            self.exchange = exchange
            self.symbol = symbol
        }
    }
    
    public struct Response: Codable {
        public let period: String?
        public let fiscalDate: String?
        public let earningsPerShare: FinancialData?
        public let grossProfit: FinancialData?
        public let netIncome: FinancialData?
        public let operatingIncome: FinancialData?
        public let operatingMargin: FinancialData?
        public let sales: FinancialData?
        public let totalAssets: FinancialData?
        public let totalLiabilities: FinancialData?
    }
    
    public struct FinancialData: Codable {
        // swiftlint:disable identifier_name
        public let yy: String?
        // swiftlint:enable identifier_name
        public let list: [String]?
    }
}
