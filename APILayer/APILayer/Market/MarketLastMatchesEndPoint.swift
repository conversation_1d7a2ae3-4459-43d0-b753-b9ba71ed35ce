//
//  MarketLastMatchesEndPoint.swift
//  APILayer
//
//  Created by <PERSON><PERSON><PERSON> on 23/11/2566 BE.
//

import Networking

public struct MarketLastMatchesEndPoint: NetworkEndpoint {
    
    public static var service = MarketLastMatchesEndPoint()
    
    public var path: String = "/market/matches"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?
    public var auth: AuthType = .basic
    
    public struct Request: Codable {
        private let exchange: String
        private let symbol: String
        private let lastSeqNo: Int64
        private let size: Int
        
        public init(exchange: String,
                    symbol: String,
                    lastSeqNo: Int64,
                    size: Int) {
            self.exchange = exchange
            self.symbol = symbol
            self.lastSeqNo = lastSeqNo
            self.size = size
        }
    }
    
    public struct Response: Codable {
        public let matches: [[String]]
    }
}
