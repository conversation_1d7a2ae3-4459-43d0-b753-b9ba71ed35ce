//
//  MarketProfileFundamentalsEndPoint.swift
//  APILayer
//
//  Created by Chung <PERSON> on 06/12/2023.
//

import Foundation
import Networking

public struct MarketProfileFundamentalsEndPoint: NetworkEndpoint {
    public static var service = MarketProfileFundamentalsEndPoint()
    
    public var path: String = "/market/profile/fundamentals"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?
    
    public struct Request: Codable {
        private let exchange: String
        private let symbol: String
        
        public init(exchange: String, symbol: String) {
            self.exchange = exchange
            self.symbol = symbol
        }
    }
    
    public struct Response: Codable {
        public let country: String?
        public let divDate: String?
        public let divYield: String?
        public let exDivDate: String?
        public let industry: String?
        public let marketCap: String?
        public let priceToBookRatio: String?
        public let priceToSalesRatio: String?
        public let priceToEarningsRatio: String?
        public let sector: String?
    }
}
