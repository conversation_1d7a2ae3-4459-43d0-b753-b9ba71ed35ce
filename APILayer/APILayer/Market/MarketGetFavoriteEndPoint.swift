//
//  MarketGetFavoriteEndPoint.swift
//  APILayer
//
//  Created by <PERSON><PERSON><PERSON> on 12/6/66.
//
import RxSwift
import Storage
import Networking
import SharedData

public struct MarketGetFavoriteEndPoint: NetworkEndpoint {
    
    public static var service = MarketGetFavoriteEndPoint()
    
    public var path: String = "/user/instrument/favorite"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?
    
    public struct Request: Codable {}
    
    public struct Response: Codable {
        public var instruments: [MarketInstrument]?
    }
    
    public mutating func call() -> Observable<Response> {
        return request(parameters: Request())
    }
}
