//
//  MarketChartCandlesEndPoint.swift
//  APILayer
//
//  Created by <PERSON><PERSON><PERSON> on 11/29/66.
//
import RxSwift
import Storage
import Networking

public struct MarketChartCandlesEndPoint: NetworkEndpoint {

    public static var service = MarketChartCandlesEndPoint()

    public var path: String = "/market/candles"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?

    public struct Request: Codable {
        private let candleType: String
        private let instrumentId: Int
        private let limit: Int
//        private let fromTime: Int
        private let toTime: Int

        public init(candleType: String, instrumentId: Int, limit: Int, fromTime: Int, toTime: Int) {
            self.candleType = candleType
            self.instrumentId = instrumentId
            self.limit = limit
//            self.fromTime = fromTime
            self.toTime = toTime
        }
    }

    public struct Response: Codable {
        public var candles: [[String]]?
    }

    public mutating func call(parameter: Request) -> Observable<Response> {
//        guard let result = MockData.marketChart.data(type: Response.self) else {
//            return .never()
//        }
//        return .just(result).delay(.milliseconds(500), scheduler: MainScheduler.instance)
        return request(parameters: parameter)
    }
}
