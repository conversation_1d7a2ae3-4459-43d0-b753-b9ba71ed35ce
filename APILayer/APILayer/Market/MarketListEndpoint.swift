//
//  MarketListEndpoint.swift
//  Data
//
//  Created by <PERSON><PERSON> on 10/27/66.
//
import RxSwift
import Storage
import Networking

public struct MarketListEndpoint: NetworkEndpoint {

    public static var service = MarketListEndpoint()

    public var path: String = "/market/list"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders? = [:]

    public struct Request: Codable {
        public var market: String

        public init(market: String) {
            self.market = market
        }
    }

    public struct Response: Codable {
        public var list: [Filter]?

        // Local Field
        public var sortedList: [Filter] {
            return list?.sorted { $0.ordering ?? 0 < $1.ordering ?? 0 } ?? []
        }
    }

    public struct Filter: Codable {
        public var ordering: Int?
        public var filterCode: String?
        public var filterName: String?
        public var list: [Item]?

        // Local Field
        public var sortedList: [Item] {
            return list?.sorted { $0.ordering ?? 0 < $1.ordering ?? 0 } ?? []
        }
    }

     public struct Item: Codable {
         public var ordering: Int?
         public var itemCode: String?
         public var itemName: String?
    }

    public mutating func call(parameter: Request) -> Observable<Response> {
//        guard let result = MockData.marketList.data(type: Response.self) else {
//            return .never()
//        }
//        return .just(result).delay(.milliseconds(500), scheduler: MainScheduler.instance)
        return request(parameters: parameter)
    }
}
