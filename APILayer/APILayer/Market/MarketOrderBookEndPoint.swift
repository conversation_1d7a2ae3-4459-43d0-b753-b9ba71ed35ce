//
//  MarketOrderBookEndPoint.swift
//  APILayer
//
//  Created by <PERSON><PERSON><PERSON> on 23/11/2566 BE.
//

import RxSwift
import Storage
import Networking

public struct MarketOrderBookEndPoint: NetworkEndpoint {

    public static var service = MarketOrderBookEndPoint()

    public var path: String = "/market/orderbook"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?
    public var auth: AuthType = .basic

    public struct Request: Codable {
        public var exchange: String
        public var symbol: String

        public init(exchange: String, symbol: String) {
            self.exchange = exchange
            self.symbol = symbol
        }
    }
    
    public struct Response: Codable {
        public var exchange: String?
        public var symbol: String?
        public var offer: [[String]]?
        public var timestamp: Int?
        public var bid: [[String]]?
    }

    public mutating func call(parameter: Request) -> Observable<Response> {
        guard let result = MockData.marketOrderBook.data(type: Response.self) else {
            return .never()
        }
        return .just(result).delay(.milliseconds(500), scheduler: MainScheduler.instance)
//        return request(parameters: parameter)
    }
}
