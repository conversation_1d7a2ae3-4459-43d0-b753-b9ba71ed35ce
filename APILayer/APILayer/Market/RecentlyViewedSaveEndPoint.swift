//
//  RecentlyViewedSaveEndPoint.swift
//  APILayer
//
//  Created by <PERSON><PERSON><PERSON> on 1/16/67.
//
import RxSwift
import Storage
import Networking
import SharedData

public struct RecentlyViewedSaveEndPoint: NetworkEndpoint {
    
    public static var service = RecentlyViewedSaveEndPoint()
    
    public var path: String = "/user/instrument/view"
    public var method: RequestMethod = .put
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?
    public var auth: AuthType = .basic
    
    public struct Request: Codable {
        private let instrumentIdList: [Int]
        
        init(instrumentIdList: [Int]) {
            self.instrumentIdList = instrumentIdList
        }
    }
    
    public struct Response: Codable {}
    
    public mutating func call(with instrumentIdList: [Int]) -> Observable<Void> {
        request(parameters: Request(instrumentIdList: instrumentIdList))
            .mapToVoid()
    }
}
