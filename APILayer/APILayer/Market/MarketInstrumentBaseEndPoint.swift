//
//  MarketInstrumentBaseEndPoint.swift
//  APILayer
//
//  Created by <PERSON><PERSON><PERSON> on 28/11/2566 BE.
//

import RxSwift
import Storage
import Networking

public struct MarketInstrumentBaseEndPoint: NetworkEndpoint {

    public static var service = MarketInstrumentBaseEndPoint()

    public var path: String = "/market/base"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?
    public var auth: AuthType = .basic

    public struct Request: Codable {
        public var exchange: String
        public var symbol: String

        public init(exchange: String, symbol: String) {
            self.exchange = exchange
            self.symbol = symbol
        }
    }
    
    public struct Response: Codable {
        public var country: String?
        public var countryAbbreviation: String?
        public var countryLogo: String?
        public var currency: String?
        public var exchange: String?
        public var exchangeTimezone: String?
        public var logo: String?
        public var utcOffsetSeconds: Int?
        public var friendlyName: String?
        public var riskLevel: String?
    }

    public mutating func call(parameter: Request) -> Observable<Response> {
//        guard let result = MockData.marketInstrumentBase.data(type: Response.self) else {
//            return .never()
//        }
//        return .just(result).delay(.milliseconds(500), scheduler: MainScheduler.instance)
        return request(parameters: parameter)
    }
}
