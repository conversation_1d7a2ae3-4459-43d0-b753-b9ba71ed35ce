//
//  MarketProductCategoryListEndPoint.swift
//  APILayer
//
//  Created by Augment Agent on 24/08/2025.
//

import RxSwift
import Storage
import Networking
import SharedData

public struct MarketProductCategoryListEndPoint: NetworkEndpoint {
    
    public static var service = MarketProductCategoryListEndPoint()
    
    public var path: String = "/market/product/category/list"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?
    
    public struct Request: Codable {
        public init() {}
    }
    
    public struct Response: Codable {
        public let list: [ProductCategory]
        
        public init(list: [ProductCategory]) {
            self.list = list
        }
        
        private enum CodingKeys: String, CodingKey {
            case list
        }
        
        public init(from decoder: Decoder) throws {
            // Try to decode as a bare array first
            if let singleValue = try? decoder.singleValueContainer(),
               let categories = try? singleValue.decode([ProductCategory].self) {
                self.list = categories
                return
            }
            // Fallback to decoding an object that contains a `list` field
            let container = try decoder.container(keyedBy: CodingKeys.self)
            self.list = try container.decode([ProductCategory].self, forKey: .list)
        }
        
        public func encode(to encoder: Encoder) throws {
            // Encode as an object with `list` for consistency
            var container = encoder.container(keyedBy: CodingKeys.self)
            try container.encode(list, forKey: .list)
        }
    }
    
    public struct ProductCategory: Codable {
        public let id: Int
        public let categoryName: String
        public let categoryEnName: String
        public let categoryLevel: Int
        public let children: [CategoryChild]
    }
    
    public struct CategoryChild: Codable {
        public let id: Int
        public let categoryName: String
        public let categoryEnName: String
        public let categoryLevel: Int
        public let instruments: [FundInstrument]
    }
    
    public struct FundInstrument: Codable {
        public let id: Int
        public let symbol: String
        public let instrumentName: String
        public let period: String
        public let allocationClass: String
        public let riskLevel: String
        public let priceChangeRate: String
    }
    
    public mutating func call() -> Observable<Response> {
        request(parameters: Request())
    }
}
