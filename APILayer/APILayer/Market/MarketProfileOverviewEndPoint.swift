//
//  MarketProfileOverview.swift
//  APILayer
//
//  Created by Chung <PERSON> on 27/11/2023.
//

import Foundation
import Networking

public struct MarketProfileOverviewEndPoint: NetworkEndpoint {
    
    public static var service = MarketProfileOverviewEndPoint()
    
    public var path: String = "/market/profile/overview"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?

    // MARK: Request
    public struct Request: Codable {
        private let exchange: String
        private let symbol: String
        
        public init(exchange: String, symbol: String) {
            self.exchange = exchange
            self.symbol = symbol
        }
    }
    
    // MARK: Response
    public struct Response: Codable {
        public let desc: String?
        public let lastPrice: String?
        public let market: String?
        public let minOrderSize: String?
        public let priorClose: String?
        public let volume: String?
        public let tagList: [String]?
    }
}
