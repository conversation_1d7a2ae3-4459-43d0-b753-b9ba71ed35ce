//
//  MarketIntradayChartEndPoint.swift
//  APILayer
//
//  Created by <PERSON><PERSON><PERSON> on 1/11/67.
//
import RxSwift
import Storage
import Networking

public struct MarketIntradayChartEndPoint: NetworkEndpoint {

    public static var service = MarketIntradayChartEndPoint()

    public var path: String = "/market/intraday"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?

    public struct Request: Codable {
        public var exchange: String
        public var symbol: String

        public init(exchange: String, symbol: String) {
            self.exchange = exchange
            self.symbol = symbol
        }
    }

    public struct Response: Codable {
        public var candles: [[String]]?
    }

    public mutating func call(parameter: Request) -> Observable<Response> {
        guard let result = MockData.marketChart.data(type: Response.self) else {
            return .never()
        }
        return .just(Response(candles: Array(result.candles?[40...70] ?? [])))
            .delay(.milliseconds(500), scheduler: MainScheduler.instance)
//        return request(parameters: parameter)
    }
}
