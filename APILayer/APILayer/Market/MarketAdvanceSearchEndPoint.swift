//
//  MarketAdvanceSearchEndPoint.swift
//  APILayer
//
//  Created by Chung <PERSON> on 14/12/2023.
//

import Foundation
import Networking
import SharedData

public struct MarketAdvanceSearchEndPoint: NetworkEndpoint {
    public static var service = MarketAdvanceSearchEndPoint()
    
    public var path: String = "/market/advance/query"
    
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?
    
    public struct Request: Codable {
        private let exchange: String
        private let boll: Boll
        private let change: Change
        private let consecutive: Consecutive
        private let dividend: Dividend
        private let eps: Eps
        private let maCross: MACross
        private let marketCap: MarketCap
        private let pe: PriceEarning
        private let price: StockPrice
     
        public init(exchange: String = "SET",
                    boll: Boll = Boll(isSelected: 1),
                    change: Change = Change(),
                    consecutive: Consecutive = Consecutive(),
                    dividend: Dividend = Dividend(),
                    eps: Eps = Eps(),
                    maCross: MACross = MACross(isSelected: 1),
                    marketCap: MarketCap = MarketCap(),
                    pe: PriceEarning = PriceEarning(),
                    price: StockPrice = StockPrice()) {
            self.exchange = exchange
            self.boll = boll
            self.change = change
            self.consecutive = consecutive
            self.dividend = dividend
            self.eps = eps
            self.maCross = maCross
            self.marketCap = marketCap
            self.pe = pe
            self.price = price
        }
    }
    
    // MARK: - Request Objects
    public struct Boll: Codable {
        private let bollLine: String
        private let breakoutDirection: String
        private let dateEnd: String
        private let dateStart: String
        private let isSelected: Int
        private let multipilier: Int
        private let period: Int
        
        public init(bollLine: String = "UPPER",
                    breakoutDirection: String = "BREAk UP",
                    dateEnd: String = "2023-12-13",
                    dateStart: String = "2023-10-01",
                    isSelected: Int = 0,
                    multipilier: Int = 2,
                    period: Int = 20) {
            self.bollLine = bollLine
            self.breakoutDirection = breakoutDirection
            self.dateEnd = dateEnd
            self.dateStart = dateStart
            self.isSelected = isSelected
            self.multipilier = multipilier
            self.period = period
        }
    }
    
    public struct Change: Codable {
        private let dateEnd: String
        private let dateStart: String
        private let isSelected: Int
        private let max: String
        private let min: String
        
        public init(dateEnd: String = "2023-01-02",
                    dateStart: String = "2023-03-02",
                    isSelected: Int = 0,
                    max: String = "20",
                    min: String = "-20") {
            self.dateEnd = dateEnd
            self.dateStart = dateStart
            self.isSelected = isSelected
            self.max = max
            self.min = min
        }
    }
    
    public struct Consecutive: Codable {
        private let days: Int
        private let field: String
        private let isSelected: Int
        private let type: String
        
        public init(days: Int = 7,
                    field: String = "",
                    isSelected: Int = 0,
                    type: String = "") {
            self.days = days
            self.field = field
            self.isSelected = isSelected
            self.type = type
        }
    }
    
    public struct Dividend: Codable {
        private let max: String
        private let min: String
        private let isSelected: Int
        
        public init(max: String = "20",
                    min: String = "1",
                    isSelected: Int = 0) {
            self.max = max
            self.min = min
            self.isSelected = isSelected
        }
    }
    
    public struct Eps: Codable {
        private let max: String
        private let min: String
        private let isSelected: Int
        
        public init(max: String = "20",
                    min: String = "1",
                    isSelected: Int = 0) {
            self.max = max
            self.min = min
            self.isSelected = isSelected
        }
    }
    
    public struct MACross: Codable {
        private let crossType: String
        private let dateEnd: String
        private let dateStart: String
        private let long: Int
        private let short: Int
        private let isSelected: Int
        
        public init(crossType: String = "GOLDEN CROSS",
                    dateEnd: String = "2023-12-12",
                    dateStart: String = "2023-10-05",
                    long: Int = 20,
                    short: Int = 5,
                    isSelected: Int = 0) {
            self.crossType = crossType
            self.dateEnd = dateEnd
            self.dateStart = dateStart
            self.long = long
            self.short = short
            self.isSelected = isSelected
        }
    }
    
     public struct PriceEarning: Codable {
         private let max: String
         private let min: String
         private let isSelected: Int
         
         public init(max: String = "999",
                     min: String = "1",
                     isSelected: Int = 0) {
             self.max = max
             self.min = min
             self.isSelected = isSelected
         }
    }
    
     public struct StockPrice: Codable {
         private let dateEnd: String
         private let dateStart: String
         private let max: String
         private let min: String
         private let isSelected: Int
         
         public init(dateEnd: String = "2023-01-02",
                     dateStart: String = "2023-01-02",
                     max: String = "1000000",
                     min: String = "0",
                     isSelected: Int = 0) {
             self.dateEnd = dateEnd
             self.dateStart = dateStart
             self.max = max
             self.min = min
             self.isSelected = isSelected
         }
    }
    
    public struct MarketCap: Codable {
        private let max: String
        private let min: String
        private let isSelected: Int
        
        public init(max: String = "1000",
                    min: String = "1",
                    isSelected: Int = 0) {
            self.max = max
            self.min = min
            self.isSelected = isSelected
        }
    }
    
    public struct Response: Codable {
        public let instruments: [MarketInstrument]?
    }
}
