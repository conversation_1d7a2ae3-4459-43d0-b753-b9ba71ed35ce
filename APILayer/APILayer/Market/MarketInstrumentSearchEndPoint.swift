//
//  MarketInstrumentSearchEndPoint.swift
//  APILayer
//
//  Created by Chung <PERSON> on 07/12/2023.
//

import Foundation
import Networking
import SharedData

public struct MarketInstrumentSearchEndPoint: NetworkEndpoint {
    public static var service = MarketInstrumentSearchEndPoint()
    
    public var path: String = "/market/instrument/search"
    
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?
    
    public struct Request: Codable {
        private let keyword: String
        
        public init(keyword: String) {
            self.keyword = keyword
        }
    }
    
    public struct Response: Codable {
        public let instruments: [MarketInstrument]?
    }
}
