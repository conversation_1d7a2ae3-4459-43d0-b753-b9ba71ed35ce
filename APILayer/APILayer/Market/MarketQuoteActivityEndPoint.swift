//
//  MarketQuoteActivityEndPoint.swift
//  APILayer
//
//  Created by <PERSON><PERSON><PERSON> on 22/11/2566 BE.
//

import RxSwift
import Storage
import Networking

public struct MarketQuoteActivityEndPoint: NetworkEndpoint {
    
    public static var service = MarketQuoteActivityEndPoint()
    
    public var path: String = "/market/quote"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?
    public var auth: AuthType = .basic
    
    public struct Request: Codable {
        private let instrumentId: Int
        
        public init(instrumentId: Int) {
            self.instrumentId = instrumentId
        }
    }
    
    public struct Response: Codable {
        public let averageBuyPrice: String?
        public let averagePrice: String?
        public let averageSellPrice: String?
        public let ceilingPrice: String?
        public let floorPrice: String?
        public let highPrice: String?
        public let lastMatchedTime: Int?
        public let lowPrice: String?
        public let marketStatus: String?
        public let minimumOrder: String?
        public let openPrice: String?
        public let price: String?
        public let priceChange: String?
        public let priceChangeRate: String?
        public let priorClosePrice: String?
        public let totalAmount: String?
        public let totalVolume: String?
        
        public init(from ticker: WSTicker) {
            averageBuyPrice = ticker.averageBuyPrice
            averagePrice = ticker.averagePrice
            averageSellPrice = ticker.averageSellPrice
            ceilingPrice = ticker.ceilingPrice
            floorPrice = ticker.floorPrice
            highPrice = ticker.highPrice
            lastMatchedTime = ticker.lastMatchedTime
            lowPrice = ticker.lowPrice
            marketStatus = ticker.marketStatus
            minimumOrder = ticker.minimumOrder
            openPrice = ticker.openPrice
            price = ticker.price
            priceChange = ticker.priceChange
            priceChangeRate = ticker.priceChangeRate
            priorClosePrice = ticker.priorClosePrice
            totalAmount = ticker.totalAmount
            totalVolume = ticker.totalVolume
        }
    }

    public mutating func call(parameter: Request) -> Observable<Response> {
//        guard let result = MockData.marketQuote.data(type: Response.self) else {
//            return .never()
//        }
//        return .just(result).delay(.milliseconds(500), scheduler: MainScheduler.instance)
        return request(parameters: parameter)
    }
}
