//
//  DeclarationCRSUpdateEndpoint.swift
//  APILayer
//
//  Created by <PERSON><PERSON> on 7/9/67.
//
import RxSwift
import Storage
import Networking
import SharedData

public struct DeclarationCRSUpdateEndpoint: NetworkEndpoint {

    public static var service = DeclarationCRSUpdateEndpoint()

    public var path: String = "/user/declaration-crs"
    public var method: RequestMethod = .put
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?

    public struct Request: Codable {
        public let list: [Section]

        public init(list: [Section]) {
            self.list = list
        }
    }

    public struct Section: Codable {
        public let residence, hasTIN: String
        public let TIN, reason: String
        public let explain: String

        public init(
            residence: String, hasTIN: String,
            TIN: String, reason: String,
            explain: String
        ) {
            self.residence = residence
            self.hasTIN = hasTIN
            self.TIN = TIN
            self.reason = reason
            self.explain = explain
        }

    }

    public struct Response: Codable {
        public let docuSignUrl: String?
    }

    public mutating func call(parameter: Request) -> Observable<Response> {
        return request(parameters: parameter)
    }
}
