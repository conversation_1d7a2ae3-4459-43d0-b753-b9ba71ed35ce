//
//  GenerationRegistrationInfoPdfEndPoint.swift
//  APILayer
//
//  Created by Chung C<PERSON> on 17/1/25.
//

import Networking
import RxSwift

public struct GenerationRegistrationInfoPdfEndPoint: NetworkEndpoint {
    
    public static var service = GenerationRegistrationInfoPdfEndPoint()
    
    public var path: String = "/user/reg-pdf/generate"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?

    public struct Request: Codable {}
    
    public struct Response: Codable {}
    
    public mutating func call() -> Observable<Void> {
        request(parameters: Request()).mapToVoid()
    }
}
