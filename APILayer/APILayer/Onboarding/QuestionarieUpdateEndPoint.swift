//
//  QuestionarieUpdateEndPoint.swift
//  APILayer
//
//  Created by <PERSON><PERSON><PERSON> on 6/20/67.
//
import RxSwift
import Storage
import Networking
import SharedData

public struct QuestionarieUpdateEndPoint: NetworkEndpoint {

    public static var service = QuestionarieUpdateEndPoint()

    public var path: String = "/user/questionnaire"
    public var method: RequestMethod = .put
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?

    public struct Request: Codable {

        public let id: Int
        public let isDryRun: Bool
        public let questionList: [Question]

        public init(id: Int, isDryRun: Bool = false, questionList: [Question]) {
            self.id = id
            self.isDryRun = isDryRun
            self.questionList = questionList
        }
    }

    public struct Question: Codable {
        public let id: Int
        public let questionType: String
        public var answerList: [Answer]

        public init(id: Int, questionType: String, answerList: [Answer]) {
            self.id = id
            self.questionType = questionType
            self.answerList = answerList
        }
    }

    public struct Answer: Codable {
        public let id: Int
        public var selected: Bool
        public var value: String

        public init(id: Int, selected: Bool, value: String) {
            self.id = id
            self.selected = selected
            self.value = value
        }
    }

    public struct Response: Codable {
        public let score,
                   riskLevel: String?
    }

    public mutating func call(parameter: Request) -> Observable<Response> {
        request(parameters: parameter)
    }
}
