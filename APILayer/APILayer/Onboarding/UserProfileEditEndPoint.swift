//
//  UserProfileEditEndPoint.swift
//  APILayer
//
//  Created by <PERSON> on 3/7/24.
//

import Networking

public struct UserProfileEditEndPoint: NetworkEndpoint {
    
    public static var service = UserProfileEditEndPoint()
    
    public var path: String = "/user/profile"
    public var method: RequestMethod = .put
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?
    
    public struct Request: Codable {
        let region: String
        let chineseName: String
        let englishName: String
        let gender: String
        let dateOfBirth: String
        let maritalStatus: String
        let educationLevel: String
        let additionalPhoneNumber: String?
        let deliveryMethod: String
        let residentialAddress: String
        let residentialAddressPostalCode: String
        let mainlingAddressRegion: String
        let mainlingAddress: String
        let mainlingAddressPostalCode: String
        let accountStatementLanguage: String
        let base: Base
        
        public init(region: String, 
                    chineseName: String,
                    englishName: String,
                    gender: String,
                    dateOfBirth: String,
                    maritalStatus: String,
                    educationLevel: String,
                    additionalPhoneNumber: String?,
                    deliveryMethod: String,
                    residentialAddress: String,
                    residentialAddressPostalCode: String,
                    mainlingAddressRegion: String,
                    mainlingAddress: String,
                    mainlingAddressPostalCode: String,
                    accountStatementLanguage: String,
                    base: Base) {
            self.region = region
            self.chineseName = chineseName
            self.englishName = englishName
            self.gender = gender
            self.dateOfBirth = dateOfBirth
            self.maritalStatus = maritalStatus
            self.educationLevel = educationLevel
            self.additionalPhoneNumber = additionalPhoneNumber
            self.deliveryMethod = deliveryMethod
            self.residentialAddress = residentialAddress
            self.residentialAddressPostalCode = residentialAddressPostalCode
            self.mainlingAddressRegion = mainlingAddressRegion
            self.mainlingAddress = mainlingAddress
            self.mainlingAddressPostalCode = mainlingAddressPostalCode
            self.accountStatementLanguage = accountStatementLanguage
            self.base = base
        }
    }
    
    public struct Base: Codable {
        let phoneNumber: String
        let email: String
        
        public init(phoneNumber: String, email: String) {
            self.phoneNumber = phoneNumber
            self.email = email
        }
    }
    
    public struct Response: Codable {}
}
