//
//  UserInformationEndPoint.swift
//  APILayer
//
//  Created by <PERSON><PERSON> on 7/1/67.
//
import RxSwift
import Networking
import Storage

public struct UserInformationEndPoint: NetworkEndpoint {
    
    public static var service = UserInformationEndPoint()
    
    public var path: String = "/user/information"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?
    
    public struct Request: Codable {}
    
    public typealias Response = UserInformation
    
    public mutating func call() -> Observable<Response> {
        request(parameters: Request())
    }
}
