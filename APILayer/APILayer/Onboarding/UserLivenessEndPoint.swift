//
//  UserLivenessEndPoint.swift
//  APILayer
//
//  Created by <PERSON><PERSON> on 6/27/67.
//
import RxSwift
import Storage
import Networking
import SharedData

public struct UserLivenessEndPoint: NetworkEndpoint {
    
    public static var service = UserLivenessEndPoint()
    
    public var path: String = "/common/liveness"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?
    
    public struct Request: Codable {
        public let videoDosKey, imageDosKey, idType, idCode: String
        
        public init(videoDosKey: String, imageDosKey: String, idType: String, idCode: String) {
            self.videoDosKey = videoDosKey
            self.imageDosKey = imageDosKey
            self.idType = idType
            self.idCode = idCode
        }
    }
    
    public struct Response: Codable {
        public let pass: Bool?
    }
    
    public mutating func call(parameter: Request) -> Observable<Response> {
        request(parameters: parameter)
    }
}
