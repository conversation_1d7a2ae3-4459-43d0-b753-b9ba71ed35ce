//
//  UpdateCustomerStatusEndPoint.swift
//  APILayer
//
//  Created by <PERSON> on 6/12/24.
//

import Networking
import RxSwift
import SharedData

public struct UpdateCustomerStatusEndPoint: NetworkEndpoint {
    
    public static var service = UpdateCustomerStatusEndPoint()
    
    public var path: String = "/user/status"
    public var method: RequestMethod = .put
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?
    
    public struct Request: Codable {
        private let status: String
        private let reason: String?
        private let password: String?
        
        init(status: String, reason: String? = nil, password: String? = nil) {
            self.status = status
            self.reason = reason
            self.password = password
        }
    }
    
    public struct Response: Codable {}
    
    public mutating func call(with status: AccountStatus,
                              reason: String? = nil,
                              password: String? = nil) -> Observable<Response> {
        request(parameters: Request(status: status.rawValue,
                                    reason: reason,
                                    password: password))
    }
}
