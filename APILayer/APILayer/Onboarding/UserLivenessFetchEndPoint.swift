//
//  UserLivenessFetchEndPoint.swift
//  APILayer
//
//  Created by <PERSON><PERSON><PERSON> on 7/4/67.
//
import RxSwift
import Storage
import Networking
import SharedData

public struct UserLivenessFetchEndPoint: NetworkEndpoint {
    
    public static var service = UserLivenessFetchEndPoint()
    
    public var path: String = "/user/liveness"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?
    
    public struct Request: Codable {}
    
    public struct Response: Codable {
        public let livenessApprovalStatus,
                   customerName: String?
        public let similarityScore: Double?
        public let birthDay: Int?
        public let region: String?
        public let idExpirationDate: Int?
        public let idImage, 
                   livenessVideo: String?
        public let cardList: [CardList]?
    }
    
    public struct CardList: Codable {
        public let cardType,
                   cardNumber,
                   cardImage: String?
    }
    
    public mutating func call() -> Observable<Response> {
        request(parameters: Request())
    }
}
