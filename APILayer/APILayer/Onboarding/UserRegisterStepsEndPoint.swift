//
//  UserRegisterStepsEndPoint.swift
//  APILayer
//
//  Created by <PERSON><PERSON> on 7/1/67.
//
import RxSwift
import Storage
import Networking
import SharedData

public struct UserRegisterStepsEndPoint: NetworkEndpoint {
    
    public static var service = UserRegisterStepsEndPoint()
    
    public var path: String = "/user/register/steps"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?
    
    public struct Request: Codable {
        public init() {}
    }
    
    public struct Response: Codable {
        public let list: [List]?
        
        public var incompleteSteps: [RegisterStep] {
            let statusList = list?.map {
                RegisterStepState(code: $0.code ?? "",
                                  status: $0.status ?? "")
            } ?? []
            
            return RegisterStep.incompletedSteps(from: statusList)
        }        
        
        public var completeSteps: [RegisterStep] {
            let statusList = list?.map {
                RegisterStepState(code: $0.code ?? "",
                                  status: $0.status ?? "")
            } ?? []
            
            return RegisterStep.completedSteps(from: statusList)
        }
    }
    
    public struct List: Codable {
        public let code, 
                   desc,
                   status: String?
    }
    
    public mutating func request() -> Observable<Response> {
        request(parameters: Request())
    }
}
