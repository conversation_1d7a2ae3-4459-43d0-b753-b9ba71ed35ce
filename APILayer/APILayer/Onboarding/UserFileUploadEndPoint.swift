//
//  UserFileUploadEndPoint.swift
//  APILayer
//
//  Created by <PERSON><PERSON><PERSON> on 6/26/67.
//
import RxSwift
import Storage
import Networking
import SharedData

public struct UserFileUploadEndPoint: NetworkEndpoint {
    
    public static var service = UserFileUploadEndPoint()
    
    public var path: String = "/user/file"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?
    
    public struct Request: Codable {}
    
    public struct Response: Codable {
        public let dosKey: String?
        
        public init(dosKey: String?) {
            self.dosKey = dosKey
        }
    }
    
    public mutating func upload(data: Data) -> Observable<Response> {
        uploadBinary(data: data)
    }
}
