//
//  UserEmploymentEditEndPoint.swift
//  APILayer
//
//  Created by <PERSON> on 4/7/24.
//

import Networking

public struct UserEmploymentEditEndPoint: NetworkEndpoint {
    
    public static var service = UserEmploymentEditEndPoint()
    
    public var path: String = "/user/employment"
    public var method: RequestMethod = .put
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?
    
    public struct Request: Codable {
        let employmentStatus: String
        let employmentStatusText: String
        let companyName: String
        let position: String
        let yearsInService: Int
        let industry: String
        let companyRegion: String
        let companyAddress: String
        let companyPostalCode: String
        
        public init(employmentStatus: String,
                    employmentStatusText: String,
                    companyName: String,
                    position: String,
                    yearsInService: Int,
                    industry: String,
                    companyRegion: String,
                    companyAddress: String,
                    companyPostalCode: String) {
            self.employmentStatus = employmentStatus
            self.employmentStatusText = employmentStatusText
            self.companyName = companyName
            self.position = position
            self.yearsInService = yearsInService
            self.industry = industry
            self.companyRegion = companyRegion
            self.companyAddress = companyAddress
            self.companyPostalCode = companyPostalCode
        }
    }
    
    public struct Response: Codable {}
}
