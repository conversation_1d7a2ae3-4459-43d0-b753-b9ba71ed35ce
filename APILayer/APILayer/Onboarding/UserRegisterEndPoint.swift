//
//  UserRegisterEndPoint.swift
//  APILayer
//
//  Created by <PERSON><PERSON><PERSON> on 6/28/67.
//
import RxSwift
import Storage
import Networking
import SharedData

public struct UserRegisterEndPoint: NetworkEndpoint {

    public static var service = UserRegisterEndPoint()

    public var path: String = "/user/register"
    public var method: RequestMethod = .put
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?
    
    public struct Request: Codable {
        
        public let chineseName, englishName, mobileRegion, phoneNumber, email: String
        public let referenceNumber, password, otpBizType, otpType: String
        public let otpValue: String
        public let refCode: String
        
        public init(chineseName: String,
                    englishName: String,
                    mobileRegion: String,
                    phoneNumber: String,
                    email: String,
                    referenceNumber: String,
                    password: String,
                    otpBizType: String,
                    otpType: String,
                    otpValue: String,
                    refCode: String) {
            self.chineseName = chineseName
            self.englishName = englishName
            self.mobileRegion = mobileRegion
            self.phoneNumber = phoneNumber
            self.email = email
            self.referenceNumber = referenceNumber
            self.password = password
            self.otpBizType = otpBizType
            self.otpType = otpType
            self.otpValue = otpValue
            self.refCode = refCode
        }
    }

    public struct Response: Codable {
        public let customerId: Int?
    }

    public mutating func call(parameter: Request) -> Observable<Response> {
         request(parameters: parameter)
    }
}
