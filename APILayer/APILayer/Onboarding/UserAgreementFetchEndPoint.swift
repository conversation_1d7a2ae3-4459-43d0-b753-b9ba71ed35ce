//
//  UserAgreementFetchEndPoint.swift
//  APILayer
//
//  Created by <PERSON><PERSON> on 6/20/67.
//
import RxSwift
import Storage
import Networking
import SharedData

public struct UserAgreementFetchEndPoint: NetworkEndpoint {

    public static var service = UserAgreementFetchEndPoint()

    public var path: String = "/user/agreement"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?

    public struct Request: Codable {

        let agreementCodeList: [String]

        public init(codes: [AgreementCode]) {
            self.agreementCodeList = codes.map { $0.rawValue }
        }
    }

    public struct Response: Codable {
        public let list: [Agreement]?
    }

    public struct Agreement: Codable {
        public let agreementCode: String?
        public let isAgree: String?
        public let isRequired: Bool?

        public var code: AgreementCode? {
            return AgreementCode(rawValue: agreementCode?.uppercased() ?? "")
        }
    }

    public enum AgreementCode: String {
        case valnerableClient = "VULNERABLE_CLIENT_DECLARATION"
    }

    public mutating func call(parameter: Request) -> Observable<Response> {
//        let response = Response(agreementList: [Agreement(agreementCode: "0", isAgree: true, isRequired: true)])
//        return .just(response).delay(.milliseconds(500), scheduler: MainScheduler.instance)
        return request(parameters: parameter)
    }

}
