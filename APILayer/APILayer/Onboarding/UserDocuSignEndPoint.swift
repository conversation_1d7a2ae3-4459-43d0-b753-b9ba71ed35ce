//
//  UserDocuSignEndPoint.swift
//  APILayer
//
//  Created by <PERSON> on 11/7/24.
//

import Networking
import RxSwift
import SharedData

public struct UserDocuSignEndPoint: NetworkEndpoint {
    
    public static var service = UserDocuSignEndPoint()
    
    public var path: String = "/user/docu-sign"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?
    
    public struct Request: Codable {}
    
    public typealias Response = DocuSignData
    
    public mutating func call() -> Observable<Response> {
        request(parameters: Request())
    }
}
