//
//  UserIdentityEndPoint.swift
//  APILayer
//
//  Created by <PERSON><PERSON><PERSON> on 6/27/67.
//
import RxSwift
import Storage
import Networking
import SharedData

public struct UserIdentityEndPoint: NetworkEndpoint {
    
    public static var service = UserIdentityEndPoint()
    
    public var path: String = "/user/identity"
    public var method: RequestMethod = .put
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?
    
    public struct Request: Codable {
        private let region: String
        private let cardList: [CardList]
        private let customerID: Int?
        
        public init(region: String, cardList: [CardList], customerID: Int? = nil) {
            self.region = region
            self.cardList = cardList
            self.customerID = customerID
        }
    }
    
    public struct CardList: Codable {
        public let cardType,
                    cardNumber,
                    cardImage: String
        private let expiration: Int?
        
        public init(cardType: String,
                    cardNumber: String,
                    cardImage: String,
                    expiration: Int? = nil) {
            self.cardType = cardType
            self.cardNumber = cardNumber
            self.cardImage = cardImage
            self.expiration = expiration
        }
    }
    
    public struct Response: Codable {}
    
    public mutating func call(parameter: Request) -> Observable<Response> {
        request(parameters: parameter)
    }
}
