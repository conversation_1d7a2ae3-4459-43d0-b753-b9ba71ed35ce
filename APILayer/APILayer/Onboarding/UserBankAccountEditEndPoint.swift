//
//  UserBankAccountEditEndPoint.swift
//  APILayer
//
//  Created by <PERSON> on 4/7/24.
//

import Networking

public struct UserBankAccountEditEndPoint: NetworkEndpoint {
    
    public static var service = UserBankAccountEditEndPoint()
    
    public var path: String = "/user/bank"
    public var method: RequestMethod = .put
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?
    
    public struct Request: Codable {
        let bankAccountId: Int?
        let accountType: String
        let bankName: String
        let accountName: String
        let accountNumber: String
        let swiftCode: String
        let currency: [String]
        let bankAddress: BankAddress
        let remark: String?
        let isPrimary: String
        let action: String
        
        public init(bankAccountId: Int?,
                    accountType: String,
                    bankName: String,
                    accountName: String,
                    accountNumber: String,
                    swiftCode: String,
                    currency: [String],
                    bankAddress: BankAddress,
                    remark: String?,
                    isPrimary: String,
                    action: String) {
            self.bankAccountId = bankAccountId
            self.accountType = accountType
            self.bankName = bankName
            self.accountName = accountName
            self.accountNumber = accountNumber
            self.swiftCode = swiftCode
            self.currency = currency
            self.bankAddress = bankAddress
            self.remark = remark
            self.isPrimary = isPrimary
            self.action = action
        }
    }
    
    public struct BankAddress: Codable {
        public let countryRegion: String
        public let address: String
        public let postCode: String?
        
        public init(countryRegion: String,
                    address: String,
                    postCode: String?) {
            self.countryRegion = countryRegion
            self.address = address
            self.postCode = postCode
        }
    }
    
    public struct Response: Codable {}
}

