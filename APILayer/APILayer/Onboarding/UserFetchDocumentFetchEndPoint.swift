//
//  UserDocumentFetchEndPoint.swift
//  APILayer
//
//  Created by <PERSON><PERSON> on 7/3/67.
//
import RxSwift
import Storage
import Networking
import SharedData

public struct UserDocumentFetchEndPoint: NetworkEndpoint {

    public static var service = UserDocumentFetchEndPoint()

    public var path: String = "/user/document"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?

    public struct Request: Codable {
        public init() {}
    }

    public struct Response: Codable {
        public let customerDocumentList: [CustomerDocumentList]?
    }

    public struct CustomerDocumentList: Codable {
        public let id: Int?
        public let documentType, otherDocumentType, status, rejectReason: String?
        public let updatedBy, createdAt, updateAt: Int?
        public let documentFileKey, documentFileType, documentFileName: String?
    }

    public mutating func call(parameter: Request) -> Observable<Response> {
//        return .just(Response()).delay(.milliseconds(500), scheduler: MainScheduler.instance)
        return request(parameters: parameter)
    }

}

