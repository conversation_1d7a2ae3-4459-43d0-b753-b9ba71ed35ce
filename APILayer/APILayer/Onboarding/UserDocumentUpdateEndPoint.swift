//
//  UserDocumentUpdateEndPoint.swift
//  APILayer
//
//  Created by <PERSON><PERSON> on 6/26/67.
//
import RxSwift
import Networking

public struct UserDocumentUpdateEndPoint: NetworkEndpoint {
    
    public static var service = UserDocumentUpdateEndPoint()
    
    public var path: String = "/user/document/onboarding"
    public var method: RequestMethod = .put
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?
    
    public struct Request: Codable {
        private let scenario: String
        private let addressProof: String?
        private let assetProof: String?
        private let fatca: String?
        private let fatcaProof: String?
        
        public init(scenario: String,
                    addressProof: String? = nil,
                    assetProof: String? = nil,
                    fatca: String? = nil,
                    fatcaProof: String? = nil) {
            self.scenario = scenario
            self.addressProof = addressProof
            self.assetProof = assetProof
            self.fatca = fatca
            self.fatcaProof = fatcaProof
        }
    }
    
    public struct Response: Codable {
        public init() {}
    }
    
    public mutating func call(parameter: Request) -> Observable<Response> {
        request(parameters: parameter)
    }
}
