//
//  DeclarationCRSFetchEndpoint.swift
//  APILayer
//
//  Created by <PERSON><PERSON> on 7/9/67.
//
import RxSwift
import Storage
import Networking
import SharedData

public struct DeclarationCRSFetchEndpoint: NetworkEndpoint {

    public static var service = DeclarationCRSFetchEndpoint()
    
    public var path: String = "/user/declaration-crs"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?
    
    public struct Request: Codable {}
    
    public struct Response: Codable {
        public let list: [Section]?
    }
    
    public struct Section: Codable {
        public let residence, hasTIN, TIN, reason: String?
        public let explain: String?
    }
    
    public mutating func call() -> Observable<Response> {
        request(parameters: Request())
    }
}
