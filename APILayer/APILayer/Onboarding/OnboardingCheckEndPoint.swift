//
//  OnboardingCheckEndPoint.swift
//  APILayer
//
//  Created by <PERSON> on 22/1/25.
//

import Networking
import RxSwift

public struct OnboardingCheckEndPoint: NetworkEndpoint {
    
    public static var service = OnboardingCheckEndPoint()
    
    public var path: String = "/user/onboarding/check"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?
    
    public struct Request: Codable {
        private let email: String
        private let mobile: String
        private let mobileRegion: String
        
        public init(email: String, mobile: String, mobileRegion: String) {
            self.email = email
            self.mobile = mobile
            self.mobileRegion = mobileRegion
        }
    }
    
    public struct Response: Codable {}
    
    public mutating func call(with request: Request) -> Observable<Void> {
        self.request(parameters: request).mapToVoid()
    }
}
