//
//  QuestionarieFetchEndpoint.swift
//  APILayer
//
//  Created by <PERSON><PERSON> on 6/18/67.
//
import RxSwift
import Storage
import Networking
import SharedData

public struct QuestionarieFetchEndpoint: NetworkEndpoint {

    public static var service = QuestionarieFetchEndpoint()

    public var path: String = "/user/questionnaire"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?

    public struct Request: Codable {
        let questionnaireCode: String

        public init(code: QuestionCode) {
            questionnaireCode = code.rawValue
        }
    }

    public struct Response: Codable {
        public let id: Int?
        public let score, riskLevel, code, name: String?
        public let questionList: [QuestionList]?
    }

    public struct QuestionList: Codable {
        public let subQuestionList: [QuestionList]?
        public let answerList: [AnswerList]?
        public let content: String?
        public let id, sort: Int?
        public let sortTitle: String?
        public let questionType: String?

        public var type: QuestionType? {
            QuestionType(rawValue: questionType?.uppercased() ?? "")
        }
    }

    public struct AnswerList: Codable {
        public let subAnswerList: [AnswerList]?
        public let id, sort: Int?
        public let sortTitle, content: String?
        public let selected: Bool?
        public let value: String?
        public let answerType: String?

        public var type: QuestionType? {
            QuestionType(rawValue: answerType?.uppercased() ?? "")
        }
    }
    
    public mutating func call(with code: QuestionCode) -> Observable<Response> {
        request(parameters: Request(code: code))
    }

    public enum QuestionCode: String {
        case riskAssessment = "RISK_PROFILING_ASSESSMENT"
        case associatedAccounts = "DISCLOSURE_OF_ASSOCIATED_ACCOUNTS"
        case identity = "DISCLOSURE_OF_IDENTITY"
        case crs = "CRS_DECLARATION"
        case usIndicia = "US_INDICIA_QUESTIONNAIRE"
        case fatca = "FATCA_IDENTITY_DECLARATION"
        case proInvestor = "PROFESSIONAL_INVESTOR_ASSESSMENT"
    }

    public enum QuestionType: String {
        case radio = "RADIO"
        case checkbox = "CHECKBOX"
        case textbox = "TEXTBOX"
    }
}
