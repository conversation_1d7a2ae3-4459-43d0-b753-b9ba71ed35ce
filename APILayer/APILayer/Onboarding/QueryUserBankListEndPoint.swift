//
//  QueryUserBankListEndPoint.swift
//  APILayer
//
//  Created by <PERSON> on 4/7/24.
//

import Networking
import RxSwift
import SharedData

public struct QueryUserBankListEndPoint: NetworkEndpoint {
    
    public static var service = QueryUserBankListEndPoint()
    
    public var path: String = "/user/bank/list"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?
    
    public struct Request: Codable {}
    
    public struct Response: Codable {
        public let bankAccountList: [BankAccount]?
    }
    
    public struct BankAccount: Codable {
        public let id: Int?
        public let customerId: Int?
        public let accountType: String?
        public let bankName: String?
        public let accountName: String?
        public let accountNumber: String?
        public let swiftCode: String?
        public let currency: [String]?
        public let bankAddress: UserBankAccountEditEndPoint.BankAddress?
        public let remark: String?
        public let isPrimary: Bool?
        public let status: String?
        public let createdAt: Int?
        public let updateAt: Int?
        
        public var accountStatus: BankAccountStatus {
            BankAccountStatus(rawValue: status ?? "") ?? .unknown
        }
    }
    
    public mutating func call() -> Observable<Response> {
        request(parameters: Request())
    }
}

