//
//  CustomerAgreementUpdateEndPoint.swift
//  APILayer
//
//  Created by <PERSON><PERSON> on 6/20/67.
//
import RxSwift
import Storage
import Networking
import SharedData

public struct UserAgreementUpdateEndPoint: NetworkEndpoint {

    public static var service = UserAgreementUpdateEndPoint()

    public var path: String = "/user/agreement"
    public var method: RequestMethod = .put
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?

    public struct Request: Codable {

        let agreementList: [Agreement]

        public init(agreementList: [Agreement]) {
            self.agreementList = agreementList
        }
    }

    public struct Agreement: Codable {
        public let agreementCode: String
        public let isAgree: Bool

        public init(agreementCode: String, isAgree: Bool) {
            self.agreementCode = agreementCode
            self.isAgree = isAgree
        }
    }

    public struct Response: Codable {
    }

    public mutating func call(parameter: Request) -> Observable<Response> {
//        return .just(Response()).delay(.milliseconds(500), scheduler: MainScheduler.instance)
        return request(parameters: parameter)
    }

}
