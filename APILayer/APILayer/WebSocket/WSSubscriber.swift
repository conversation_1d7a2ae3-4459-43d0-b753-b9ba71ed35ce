//
//  WSSubscriber.swift
//  APILayer
//
//  Created by <PERSON> on 30/11/2023.
//

import Foundation

// MARK: - WebSocketSubscriber
public protocol WSSubscriber: AnyObject {
    var wsEvents: [WSEvent] { get set }
    var wsParams: [WSEvent: [WSAssetParam]] { get set }
    
    func subscribeWebSocket(for event: WSEvent, params: [WSAssetParam])
    func unsubscribeWebSocket(for event: WSEvent)
    func unsubscribeAllWebSocket()
    func receiveData(_ datas: [WSResponse])
}

// MARK: - Implementations
public extension WSSubscriber {
    
    func subscribeWebSocket(for event: WSEvent, params: [WSAssetParam]) {
        let param = WSParam(operator: .subscribe,
                            event: event,
                            params: params)
        
        WebSocketController.shared.sendRequest(with: param,
                                               subscriber: self)
        // Save subscription indo
        if !self.wsEvents.contains(event) {
            self.wsEvents.append(event)
        }
        self.wsParams[event] = params
    }
    
    func unsubscribeWebSocket(for event: WSEvent) {
        guard let subParams = self.wsParams[event] else { return }
        
        let param = WSParam(operator: .unsubscribe,
                            event: event,
                            params: subParams)
        
        WebSocketController.shared.sendRequest(with: param,
                                               subscriber: self)
        
        self.wsParams[event] = nil
        self.wsEvents.removeAll(where: { $0 == event })
    }
    
    func unsubscribeAllWebSocket() {
        guard !self.wsEvents.isEmpty else { return }
        
        self.wsEvents.forEach { event in
            self.unsubscribeWebSocket(for: event)
        }
    }
}
