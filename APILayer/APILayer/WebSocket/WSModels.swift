//
//  WSModels.swift
//  APILayer
//
//  Created by <PERSON> C<PERSON> on 04/12/2023.
//

import Foundation

public protocol WSResponse: Decodable {}

public struct WSTicker: WSResponse {
    public let exchange: String
    public let symbol: String
    public var averageBuyPrice: String?
    public var averagePrice: String?
    public var averageSellPrice: String?
    public var ceilingPrice: String?
    public var floorPrice: String?
    public var highPrice: String?
    public var lastMatchedTime: Int?
    public var lowPrice: String?
    public var marketStatus: String?
    public var minimumOrder: String?
    public var openPrice: String?
    public var price: String?
    public var priceChange: String?
    public var priceChangeRate: String?
    public var priorClosePrice: String?
    public var totalAmount: String?
    public var totalVolume: String?
}

public struct WSLastMatches: WSResponse {
    public let exchange: String
    public let symbol: String
    public let publicTrades: [[String]]
}

public struct WSOrderBook: WSResponse {
    public let exchange: String
    public let symbol: String
    public let bid: [[String]]
    public let offer: [[String]]
}

// MARK: Structs for parsing ws response to objects
protocol WSResponseData: Decodable {}

struct WSTickerResponse: WSResponseData {
    let data: [WSTicker]
}

struct WSOrderBookResponse: WSResponseData {
    let data: [WSOrderBook]
}

struct WSLastMatchesResponse: WSResponseData {
    let data: [WSLastMatches]
}

// MARK: - Response object receive from WS
struct WSInternalResponse: Decodable {
    let `operator`: WSOperator
    let code: String
    let event: WSEvent
    let response: WSResponseData?
    
    enum CodingKeys: String, CodingKey {
        case `operator` = "op"
        case code
        case event
        case response
    }
    
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        
        self.operator = try container.decode(WSOperator.self, forKey: .operator)
        self.code = try container.decode(String.self, forKey: .code)
        self.event = try container.decode(WSEvent.self, forKey: .event)
        
        switch event {
        case .ticker:
            self.response = try container.decodeIfPresent(WSTickerResponse.self, forKey: .response)
        case .orderBook:
            self.response = try container.decodeIfPresent(WSOrderBookResponse.self, forKey: .response)
        case .publicTrade:
            self.response = try container.decodeIfPresent(WSLastMatchesResponse.self, forKey: .response)
        }
    }
}

enum WSOperator: String, Codable {
    case subscribe
    case subscribeReply = "subscribe-reply"
    case unsubscribe
    case push
}

public enum WSEvent: String, Codable {
    case ticker
    case orderBook
    case publicTrade
}

public struct WSAssetParam: Encodable {
    private let exchange: String
    private let symbol: String
    
    public init(exchange: String, symbol: String) {
        self.exchange = exchange
        self.symbol = symbol
    }
}

/// Web socket param
struct WSParam: Encodable {
    let `operator`: WSOperator
    let event: WSEvent
    let params: [WSAssetParam]
    
    enum CodingKeys: String, CodingKey {
        case `operator` = "op"
        case event
        case params
    }
}
