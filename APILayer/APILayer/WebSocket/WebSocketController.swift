//
//  WebSocketController.swift
//  APILayer
//
//  Created by <PERSON> on 30/11/2023.
//

import Foundation
import Starscream
import Storage

final class WebSocketController {
    
    static let shared = WebSocketController()
    
    private init() {
        self.start()
    }
    
    private var currentSession: String?
    
    private var webSocket: WebSocket?
    private var isConnected = false
    private var pingSchedule: Timer?
    weak var subscriber: WSSubscriber?
    private var lastParam: WSParam?
    
    private func start() {
        self.renewWebSocket()
        self.connectWS()
    }
    
    private func renewWebSocket() {
        guard
            let sessionId = LocalPreference.sessionId,
            !sessionId.isEmpty
        else { return }
        
        let path = WSConfiguration.urlEnvironment.url
        var urlComps = URLComponents(string: path)
        let items = [URLQueryItem(name: "sid", value: sessionId)]
        urlComps?.queryItems = items
        
        guard let url = urlComps?.url else { return }
        
        var request = URLRequest(url: url)
        request.setValue(url.host, forHTTPHeaderField: "Host")
        
        self.webSocket = WebSocket(request: request)
        self.webSocket?.delegate = self
        self.currentSession = sessionId
    }
    
    private func connectWS() {
        webSocket?.connect()
        pingSchedule?.invalidate()
        pingSchedule = Timer.scheduledTimer(timeInterval: 5,
                                            target: self,
                                            selector: #selector(ping),
                                            userInfo: nil,
                                            repeats: true)
    }
    
    @objc private func ping() {
        guard self.isConnected else {
            self.connectWS()
            return
        }
        
        self.webSocket?.write(string: WSConfiguration.PING)
    }
    
    private func updateDataToWS(_ param: WSParam) {
        let encoder = JSONEncoder()
        guard
            self.isConnected,
            let data = try? encoder.encode(param)
        else { return }
        
        self.webSocket?.write(data: data)
    }
    
    private func didConnectWebSocket() {
        if let lastParam = self.lastParam {
            self.sendRequest(with: lastParam, subscriber: subscriber)
        }
    }
    
    private func receiveResponseText(_ text: String) {
        guard
            let delegate = self.subscriber,
            let data = text.data(using: String.Encoding.utf8),
            let responseData = try? JSONDecoder().decode(WSInternalResponse.self, from: data),
            responseData.operator == .push
        else { return }
        
        switch responseData.event {
        case .ticker:
            if let tickerData = responseData.response as? WSTickerResponse {
                delegate.receiveData(tickerData.data)
            }
            
        case .orderBook:
            if let orderBookData = responseData.response as? WSOrderBookResponse {
                delegate.receiveData(orderBookData.data)
            }
            
        case .publicTrade:
            if let lastMatchesData = responseData.response as? WSLastMatchesResponse {
                delegate.receiveData(lastMatchesData.data)
            }
        }
    }
}

// MARK: Public
extension WebSocketController {
    
    func sendRequest(with param: WSParam, subscriber: WSSubscriber?) {
        guard self.currentSession == LocalPreference.sessionId else {
            self.webSocket?.disconnect()
            self.start()
            
            return
        }
        
        self.subscriber = subscriber
        self.updateDataToWS(param)
        self.lastParam = param
    }
}

// MARK: - WebSocketDelegate
extension WebSocketController: WebSocketDelegate {
    
    func didReceive(event: Starscream.WebSocketEvent, client: Starscream.WebSocketClient) {
        switch event {
        case .connected:
            isConnected = true
            self.didConnectWebSocket()
            
        case.text(let text):
            guard text != WSConfiguration.PONG else {
                if self.currentSession != LocalPreference.sessionId {
                    self.webSocket?.disconnect()
                }
                return
            }
            
            self.receiveResponseText(text)
            
        case .disconnected,
                .error,
                .viabilityChanged,
                .cancelled:
            isConnected = false
            
        case .reconnectSuggested:
            connectWS()
            
        default:
            break
        }
    }
}

// MARK: - WSConfiguration
public struct WSConfiguration {
    
    public struct URLEnvironment: Equatable {
        public var url: String
        public var cert: String?
        public var name: String
        public var version: String
        public var allowInsecureConnection: Bool = false
        
        public init(url: String,
                    cert: String?,
                    name: String,
                    version: String,
                    allowInsecureConnection: Bool = false) {
            self.url = url
            self.cert = cert
            self.name = name
            self.version = version
            self.allowInsecureConnection = allowInsecureConnection
        }
    }
    
    static private var environment: URLEnvironment?
    
    static public func config(url: URLEnvironment) {
        WSConfiguration.environment = url
    }
    
    public static func config(env: Environment) {
        WSConfiguration.config(url: env.wsEnvironment)
    }
    
    public static var urlEnvironment: URLEnvironment {
        guard let env = WSConfiguration.environment else {
            fatalError("Environment is not configured for Endpoint.")
        }
        return env
    }
    
    static let PING = "PING"
    static let PONG = "PONG"
}
