//
//  EditCustomerProfileEndPoint.swift
//  APILayer
//
//  Created by <PERSON> on 12/03/2024.
//

import Networking

public struct EditCustomerProfileEndPoint: NetworkEndpoint {
    public static var service = EditCustomerProfileEndPoint()
    
    public var path: String = "/user/info/edit"
    public var method: RequestMethod = .put
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?
    
    // MARK: Request
    public struct Request: Codable {
        private let deletePicture: Bool
        private let email: String?
        private let mobile: String?
        private let otp: String
        private let otpType: String
        private let profilePicture: String?
        
        public init(deletePicture: Bool = false,
                    email: String? = nil,
                    mobile: String? = nil,
                    otp: String,
                    otpType: String,
                    profilePicture: String? = nil) {
            self.deletePicture = deletePicture
            self.email = email
            self.mobile = mobile
            self.otp = otp
            self.otpType = otpType
            self.profilePicture = profilePicture
        }
    }
    
    // MARK: Response
    public struct Response: Codable {}
}
