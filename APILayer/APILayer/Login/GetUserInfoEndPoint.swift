//
//  GetUserInfoEndPoint.swift
//  APILayer
//
//  Created by <PERSON> on 19/12/24.
//

import Networking
import RxSwift

public struct GetUserInfoEndPoint: NetworkEndpoint {
    
    public static var service = GetUserInfoEndPoint()
    
    public var path: String = "/user/info"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?
    
    public struct Request: Codable {}
    
    public struct Response: Codable {
        public let email: String?
        public let mobile: String?
        public let mobileRegion: String?
        public let fullName: String?
        public let profilePicture: String?
        public let language: String?
    }
    
    public mutating func call() -> Observable<Response> {
        request(parameters: Request())
    }
}
