//
//  QueryUserProfileEndPoint.swift
//  APILayer
//
//  Created by <PERSON> on 21/12/24.
//

import Networking
import RxSwift
import Storage

public struct QueryUserProfileEndPoint: NetworkEndpoint {
    
    public static var service = QueryUserProfileEndPoint()
    
    public var path: String = "/user/profile"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?
    
    public struct Request: Codable {}
    
    public typealias Response = UserProfile
    
    public mutating func call() -> Observable<UserProfile> {
        request(parameters: Request())
    }
}
