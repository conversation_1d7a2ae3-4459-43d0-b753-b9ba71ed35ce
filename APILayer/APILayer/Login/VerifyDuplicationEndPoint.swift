//
//  VerifyDuplicationEndPoint.swift
//  APILayer
//
//  Created by <PERSON> on 06/02/2024.
//

import Networking

public struct VerifyDuplicationEndPoint: NetworkEndpoint {
    public static var service = VerifyDuplicationEndPoint()
    private init() {}
    
    public var path: String = "/user/verify"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?
    
    // MARK: Request
    public struct Request: Codable {
        private let email: String?
        private let phoneNumber: String?
        
        public init(email: String? = nil, phoneNumber: String? = nil) {
            self.email = email
            self.phoneNumber = phoneNumber
        }
    }
    
    // MARK: Response
    public struct Response: Codable {}
}
