//
//  UserLoginEndPoint.swift
//  APILayer
//
//  Created by <PERSON><PERSON><PERSON> on 6/28/67.
//

import Networking

public struct UserLoginEndPoint: NetworkEndpoint {
    public static var service = UserLoginEndPoint()
    private init() {}

    public var path: String = "/user/login"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?

    // MARK: Request
    public struct Request: Codable {
        public let username, deviceId: String
        public let password, pin, otp, token: String?
        public let otpType, refCode: String?
        
        public init(username: String,
                    deviceId: String,
                    password: String?, 
                    pin: String? = nil,
                    otp: String? = nil,
                    token: String? = nil,
                    otpType: String? = nil,
                    refCode: String? = nil) {
            self.username = username
            self.deviceId = deviceId
            self.password = password
            self.pin = pin
            self.otp = otp
            self.token = token
            self.otpType = otpType
            self.refCode = refCode
        }
    }
    
    // MARK: Response
    public struct Response: Codable {
        public let sessionId, 
                   token: String?
        public let require2Fa, 
                   newDevice,
                   boOnboarding,
                   configured2Fa: Bool?
        public let configuredPin: Bool?
        public let mobile, 
                   email: String?
    }
}
