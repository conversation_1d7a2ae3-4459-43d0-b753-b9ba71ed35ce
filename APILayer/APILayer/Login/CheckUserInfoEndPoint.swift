//
//  CheckUserInfoEndPoint.swift
//  APILayer
//
//  Created by <PERSON> on 19/12/24.
//

import Networking
import RxSwift

public struct CheckUserInfoEndPoint: NetworkEndpoint {
    
    public static var service = CheckUserInfoEndPoint()
    
    public var path: String = "/user/info/check"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?
    
    public struct Request: Codable {
        private let email: String?
        private let mobile: String?
        
        public init(email: String? = nil,
                    mobile: String? = nil) {
            self.email = email
            self.mobile = mobile
        }
    }
    
    public struct Response: Codable {}
}
