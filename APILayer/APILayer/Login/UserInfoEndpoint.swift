//
//  UserInfoEndpoint.swift
//  APILayer
//
//  Created by <PERSON><PERSON> on 2/7/67.
//
import RxSwift
import Storage
import Networking
import SharedData

public struct UserInfoEndpoint: NetworkEndpoint {

    public static var service = UserInfoEndpoint()

    public var path: String = "/user/info"
    public var method: RequestMethod = .get
    public var encoding: RequestEncoding = .query
    public var headers: RequestHeaders?

    public struct Request: Codable {}

    public class Response: UserInfo {}

    public mutating func call() -> Observable<Response> {
        return request(parameters: Request())
    }
}
