//
//  UpdateUserInfoEndPoint.swift
//  APILayer
//
//  Created by <PERSON> on 19/12/24.
//

import Networking
import RxSwift

public struct UpdateUserInfoEndPoint: NetworkEndpoint {
    
    public static var service = UpdateUserInfoEndPoint()
    
    public var path: String = "/user/info"
    public var method: RequestMethod = .put
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?
    
    public struct Request: Codable {
        private let deletePicture: Bool?
        private let email: String?
        private let mobile: String?
        private let mobileRegion: String?
        /// Required when update email or mobile
        private let otp: String?
        /// EMAIL/PHONE, Required when update email or mobile
        private let otpType: String?
        /// uired when update email or mobile
        private let refCode: String?
        private let profilePicture: String?
        private let language: String?
        
        public init(deletePicture: Bool? = nil,
                    email: String? = nil,
                    mobile: String? = nil,
                    mobileRegion: String? = nil,
                    otp: String? = nil,
                    otpType: String? = nil,
                    refCode: String? = nil,
                    profilePicture: String? = nil,
                    language: String? = nil) {
            self.deletePicture = deletePicture
            self.email = email
            self.mobile = mobile
            self.mobileRegion = mobileRegion
            self.otp = otp
            self.otpType = otpType
            self.refCode = refCode
            self.profilePicture = profilePicture
            self.language = language
        }
    }
    
    public struct Response: Codable {}
}
