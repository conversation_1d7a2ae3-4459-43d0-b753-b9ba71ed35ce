//
//  UserLogoutEndPoint.swift
//  APILayer
//
//  Created by Chung <PERSON> on 16/12/24.
//

import Networking
import RxSwift

public struct UserLogoutEndPoint: NetworkEndpoint {
    
    public static var service = UserLogoutEndPoint()
    
    public var path: String = "/user/logout"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?
    
    
    public struct Request: Codable {}
    public struct Response: Codable {}
    
    public mutating func call() -> Observable<Void> {
        request(parameters: Request()).mapToVoid()
    }
}

