//
//  BenchmarkListEndpoint.swift
//  APILayer
//
//  Created by <PERSON><PERSON><PERSON> on 8/1/67.
//
import RxSwift
import Storage
import Networking
import SharedData

public struct BenchmarkListEndpoint: NetworkEndpoint {
    
    public static var service = BenchmarkListEndpoint()
    
    public var path: String = "/market/list/detail"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?
    
    public struct Request: Codable {
        public var listCode: String = "BENCHMARK"
    }
    
    public struct Response: Codable {
        public let instruments: [Instrument]?
    }
    
    public struct Instrument: Codable {
        public let id: Int?
        public let symbol, exchange, timeZone, market: String?
        public let instrumentName, currency, logo, riskLevel: String?
        public let riskRating, instrumentClass, instrumentType, instrumentCategory: String?
        public let allocation, assetClass, lastPrice, priceChange: String?
        public let priceChangePercentage: String?
        public let favorite: Bool?
        public let totalVolume, totalAmount: String?
        
        enum CodingKeys: String, CodingKey {
            case id, symbol, exchange
            case timeZone = "TimeZone"
            case market, instrumentName, currency
            case logo, riskLevel, riskRating
            case instrumentClass, instrumentType, instrumentCategory
            case allocation, assetClass, lastPrice
            case priceChange, priceChangePercentage
            case favorite, totalVolume, totalAmount
        }
    }
    
    
    public mutating func call() -> Observable<Response> {
        request(parameters: Request())
    }
}
