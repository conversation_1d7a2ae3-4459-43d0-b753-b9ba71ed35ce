//
//  WalletStatisticEndpoint.swift
//  APILayer
//
//  Created by <PERSON><PERSON> on 7/15/67.
//
import RxSwift
import Storage
import Networking
import SharedData

public struct WalletStatisticEndpoint: NetworkEndpoint {

    public static var service = WalletStatisticEndpoint()

    public var path: String = "/wallet/statistic"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?

    public struct Request: Codable {}

    public struct Response: Codable {
        public let mtdReturn, ytdReturn, itdReturn, beta: String?
        public let sharp, annualizedReturn, annualizedVolatility: String?
    }

    public mutating func call() -> Observable<Response> {
//        guard let result = MockData.walletStatistic.data(type: Response.self) else {
//            return .never()
//        }
//        return .just(result).delay(.milliseconds(500), scheduler: MainScheduler.instance)
        return request(parameters: Request())
    }
}
