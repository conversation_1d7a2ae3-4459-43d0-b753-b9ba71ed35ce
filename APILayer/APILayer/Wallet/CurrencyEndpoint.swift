//
//  CurrencyEndpoint.swift
//  APILayer
//
//  Created by <PERSON><PERSON><PERSON> on 7/11/67.
//
import RxSwift
import Storage
import Networking
import SharedData

public struct CurrencyEndpoint: NetworkEndpoint {
    
    public static var service = CurrencyEndpoint()
    
    public var path: String = "/market/currency"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?
    
    public struct Request: Codable {
        public var currencyList: [String] = [
            "USD/HKD",
            "USD/EUR",
            "USD/CNY",
            "USD/JPY"
        ]
    }
    
    public struct Response: Codable {
        public let list: [Exchange]?
    }
    
    public struct Exchange: Codable {
        public let exchangeRate, exchange: String?
    }
    
    public mutating func call() -> Observable<Response> {
        request(parameters: Request())
    }
}
