//
//  WalletSummaryEndpoint.swift
//  APILayer
//
//  Created by <PERSON><PERSON><PERSON> on 12/19/66.
//
import RxSwift
import Storage
import Networking
import SharedData

public struct WalletSummaryEndpoint: NetworkEndpoint {
    
    public static var service = WalletSummaryEndpoint()
    
    public var path: String = "/wallet/summary"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?
    
    public struct Request: Codable {
        private let summaryBy: String
        
        public init(summaryBy: String) {
            self.summaryBy = summaryBy
        }
    }
    
    public struct Response: Codable {
        public let totalValue: String?
        public let cashBalance: String?
        public let marketValue: String?
        public let costValue: String?
        public let unrealizedGl: String?
        public let unrealizedGlRate: String?
        public let currency: String?
        public let summaryList: [Summary]?
    }
    
    public struct Summary: Codable {
        public let summaryName,
                   riskTag: String?
        
        public let marketValue,
                   costValue: String?
        
        public let unrealizedGl,
                   unrealizedGlRate,
                   percentage: String?
        public let currency: String?
        
        public let assetList: [Asset]?
    }

    public struct Asset: Codable {
        public let asOf: Int?
        public let instrumentId: Int?
        public let symbol,
                   name,
                   logo: String?
        
        public let exchange,
                   riskLevel,
                   currency,
                   unit: String?
        
        public let marketPrice,
                   marketValue,
                   costPrice,
                   costValue: String?
        
        public let unrealizedGl,
                   unrealizedGlRate: String?
    }
    
    public mutating func call(with summaryBy: SummaryType) -> Observable<Response> {
        request(parameters: Request(summaryBy: summaryBy.rawValue))
    }
}

// MARK: - Public functions
public extension WalletSummaryEndpoint.Response {
    
    func sortedSummaries() -> [WalletSummaryEndpoint.Summary] {
        summaryList?.sorted(by: { $0.percentage?.toNumber() ?? 0 > $1.percentage?.toNumber() ?? 0 }) ?? []
    }
}
