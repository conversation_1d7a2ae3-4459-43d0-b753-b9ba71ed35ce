//
//  CashWithdrawEndPoint.swift
//  APILayer
//
//  Created by <PERSON><PERSON><PERSON> on 1/10/67.
//
import RxSwift
import Storage
import Networking
import SharedData

public struct CashWithdrawEndPoint: NetworkEndpoint {

    public static var service = CashWithdrawEndPoint()

    public var path: String = "/account/withdraw"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?

    public struct Request: Codable {
        public let refCode, otpCode, otpType, otpAddress: String
        public let currency, amount: String
        public let bankAccountId: Int

        public init(
            refCode: String, otpCode: String,
            otpType: String, otpAddress: String,
            currency: String, amount: String,
            bankAccountId: Int
        ) {
            self.refCode = refCode
            self.otpCode = otpCode
            self.otpType = otpType
            self.otpAddress = otpAddress
            self.currency = currency
            self.amount = amount
            self.bankAccountId = bankAccountId
        }
    }

    public struct Response: Codable {}

    public mutating func call(parameter: Request) -> Observable<Response> {
//        return Observable.just(Response())
//            .delay(.milliseconds(500), scheduler: MainScheduler.instance)
        return request(parameters: parameter)
    }
}
