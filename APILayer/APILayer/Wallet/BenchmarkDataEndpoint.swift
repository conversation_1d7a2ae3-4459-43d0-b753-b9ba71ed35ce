//
//  BenchmarkDataEndpoint.swift
//  APILayer
//
//  Created by <PERSON><PERSON><PERSON> on 8/1/67.
//
import RxSwift
import Storage
import Networking
import SharedData

public struct BenchmarkDataEndpoint: NetworkEndpoint {
    
    public static var service = BenchmarkDataEndpoint()
    
    public var path: String = "/market/data/benchmark"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?
    
    public struct Request: Codable {
        private let instrumentIdList: [Int]
        private let period: String
        
        public init(instrumentIdList: [Int], period: String) {
            self.instrumentIdList = instrumentIdList
            self.period = period
        }
    }
    
    public struct Response: Codable {
        public let list: [BenchmarkData]?
    }
    
    public struct BenchmarkData: Codable {
        public let instrumentId: Int?
        public let symbol: String?
        public let name: String?
        public let logo: String?
        public let exchange: String?
        public let dataList: [Balance]?
    }
    
    public struct Balance: Codable {
        ///  Date
        public let d: String?
        /// Value
        public let v: String?
        /// Return
        public let r: String?
        /// Return Percentage
        public let rp: String?
        
        public var date: Date {
            d?.toDate(withFormat: "yyyy-MM-dd") ?? Date()
        }
    }
    
    public mutating func call(with instrumentIdList: [Int], period: String) -> Observable<Response> {
        request(parameters: Request(instrumentIdList: instrumentIdList, period: period))
    }
}
