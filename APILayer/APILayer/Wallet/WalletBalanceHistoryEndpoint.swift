//
//  WalletBalanceHistoryEndpoint.swift
//  APILayer
//
//  Created by <PERSON><PERSON><PERSON> on 12/19/66.
//
import RxSwift
import Networking

public struct WalletBalanceHistoryEndpoint: NetworkEndpoint {

    public static var service = WalletBalanceHistoryEndpoint()

    public var path: String = "/wallet/balance/history"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?

    public struct Request: Codable {
        private let period: String

        public init(period: String) {
            self.period = period
        }
    }

    public struct Response: Codable {
        public var balances: [Balance]?
    }

    public struct Balance: Codable {
        ///  Date
        public let d: String?
        /// Balance
        public let b: String?
        /// Return
        public let r: String?
        /// Return Percentage
        public let rp: String?
        
        public var date: Date {
            d?.toDate(withFormat: "yyyy-MM-dd") ?? Date()
        }
    }
    
    public mutating func call(with period: String) -> Observable<Response> {
        request(parameters: Request(period: period))
    }
}
