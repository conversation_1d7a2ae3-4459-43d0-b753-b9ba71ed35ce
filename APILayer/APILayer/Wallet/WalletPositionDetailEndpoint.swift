//
//  WalletPositionDetailEndpoint.swift
//  APILayer
//
//  Created by <PERSON><PERSON><PERSON> on 12/22/66.
//
import RxSwift
import Storage
import Networking
import SharedData

public struct WalletPositionDetailEndpoint: NetworkEndpoint {

    public static var service = WalletPositionDetailEndpoint()

    public var path: String = "/wallet/position/detail"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?

    public struct Request: Codable {
        public let exchange, symbol: String

        public init(exchange: String, symbol: String) {
            self.exchange = exchange
            self.symbol = symbol
        }
    }

    public struct Response: Codable {
        public let costPrice, costValue, country, countryAbbreviation: String?
        public let exchange, instrumentName: String?
        public let lastPriceTime: Int?
        public let marketStatus, marketValue, price, priceChange: String?
        public let priceChangeRate, realizedGLRate, realizedGLValue, symbol: String?
        public let unit, unrealizedGLRate, unrealizedGLValue: String?
    }

    public mutating func call(parameter: Request) -> Observable<Response> {
        guard let result = MockData.walletPositionDetail.data(type: Response.self) else {
            return .never()
        }
        return .just(result).delay(.milliseconds(500), scheduler: MainScheduler.instance)
//        return request(parameters: parameter)
    }
}
