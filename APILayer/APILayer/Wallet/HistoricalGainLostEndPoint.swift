//
//  HistoricalGainLostEndPoint.swift
//  APILayer
//
//  Created by <PERSON> on 16/12/24.
//

import Networking
import RxSwift

public struct HistoricalGainLostEndPoint: NetworkEndpoint {
    
    public static var service = HistoricalGainLostEndPoint()
    
    public var path: String = "/wallet/gainLoss/history"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?
    
    public struct Request: Codable {
        private let period: String
        private let summaryBy: String
        
        init(period: String, summaryBy: String) {
            self.period = period
            self.summaryBy = summaryBy
        }
    }
    
    public struct Response: Codable {
        public let summaryList: [Summary]?
    }
    
    public struct Summary: Codable {
        public let summaryName: String?
        public let dataList: [GainLossData]?
    }
    
    public struct GainLossData: Codable {
        ///  Date
        public let d: String?
        /// Value
        public let v: String?
        /// Return
        public let r: String?
        /// Return Percentage
        public let rp: String?
        
        public var date: Date {
            d?.toDate(withFormat: "yyyy-MM-dd") ?? Date()
        }
    }
    
    public mutating func call(with period: String, summaryBy: String) -> Observable<Response> {
        request(parameters: Request(period: period, summaryBy: summaryBy))
    }
}
