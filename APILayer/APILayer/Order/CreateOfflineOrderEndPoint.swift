//
//  CreateOfflineOrderEndPoint.swift
//  APILayer
//
//  Created by <PERSON><PERSON> on 10/1/2567 BE.
//

import Networking

public struct CreateOfflineOrderEndPoint: NetworkEndpoint {
    
    public static var service = CreateOfflineOrderEndPoint()
    
    public var path: String = "/account/offline/order/create"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?
    
    public struct Request: Codable {
        private let accountId: Int
        private let exchange: String
        private let symbol: String
        private let orderType: String
        private let pin: String
        private let price: String
        private let side: String
        private let stopPrice: String?
        private let volume: Double
        
        public init(accountId: Int,
                    exchange: String,
                    symbol: String,
                    orderType: String,
                    pin: String,
                    price: String,
                    side: String,
                    stopPrice: String? = nil,
                    volume: Double) {
            self.accountId = accountId
            self.exchange = exchange
            self.symbol = symbol
            self.orderType = orderType
            self.pin = pin
            self.price = price
            self.side = side
            self.stopPrice = stopPrice
            self.volume = volume
        }
    }
    
    public struct Response: Codable {}
}
