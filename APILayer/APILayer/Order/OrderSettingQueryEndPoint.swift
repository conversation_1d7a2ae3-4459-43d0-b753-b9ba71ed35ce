//
//  OrderSettingQueryEndPoint.swift
//  APILayer
//
//  Created by <PERSON> on 03/01/2024.
//

import Networking
import RxSwift

public struct OrderSettingQueryEndPoint: NetworkEndpoint {
    
    public static var service = OrderSettingQueryEndPoint()
    
    public var path: String = "/account/order/setting/query"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?
    
    public struct Request: Codable {}
    
    public struct Response: Codable {
        public let lastUpdatedPinTimeStamp: Int?
        public let nvdr: Bool?
        public let orderConfirmation: Bool?
        public let tradingPin: String?
        public let tradingPinDuration: String?
    }
    
    public mutating func call() -> Observable<Response> {
        request(parameters: Request())
    }
}
