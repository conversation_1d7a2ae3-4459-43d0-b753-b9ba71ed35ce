//
//  CancelOrderThaiEquityEndPoint.swift
//  APILayer
//
//  Created by Chung Cr on 02/01/2024.
//

import Networking

public struct CancelOrderThaiEquityEndPoint: NetworkEndpoint {
    
    public static var service = CancelOrderThaiEquityEndPoint()
    
    public var path: String = "/account/thai/equity/order/cancel"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?
    
    public struct Request: Codable {
        private let accountId: Int
        private let orderId: Int
        private let pin: String
        
        public init(accountId: Int, orderId: Int, pin: String) {
            self.accountId = accountId
            self.orderId = orderId
            self.pin = pin
        }
    }
    
    public struct Response: Codable {}
}
