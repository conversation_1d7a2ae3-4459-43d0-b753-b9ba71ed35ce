//
//  CancelOfflineOrderEndPoint.swift
//  APILayer
//
//  Created by <PERSON><PERSON> on 10/1/2567 BE.
//

import Networking

public struct CancelOfflineOrderEndPoint: NetworkEndpoint {
    
    public static var service = CancelOfflineOrderEndPoint()
    
    public var path: String = "/account/offline/order/delete"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?
    
    public struct Request: Codable {
        private let accountId: Int
        private let orderId: Int
        private let pin: String
        
        public init(accountId: Int, orderId: Int, pin: String) {
            self.accountId = accountId
            self.orderId = orderId
            self.pin = pin
        }
    }
    
    public struct Response: Codable {}
}

