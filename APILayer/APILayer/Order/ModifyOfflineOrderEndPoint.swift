//
//  ModifyOfflineOrderEndPoint.swift
//  APILayer
//
//  Created by <PERSON><PERSON> on 10/1/2567 BE.
//

import Networking

public struct ModifyOfflineOrderEndPoint: NetworkEndpoint {
    
    public static var service = ModifyOfflineOrderEndPoint()
    
    public var path: String = "/account/offline/order/modify"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?
    
    public struct Request: Codable {
        private let accountId: Int
        private let orderId: Int
        private let orderType: String
        private let pin: String
        private let price: String
        private let stopPrice: String
        private let tradeSide: String
        private let volume: String
        
        public init(accountId: Int,
                    orderId: Int,
                    orderType: String,
                    pin: String,
                    price: String,
                    stopPrice: String,
                    tradeSide: String,
                    volume: String) {
            self.accountId = accountId
            self.orderId = orderId
            self.orderType = orderType
            self.pin = pin
            self.price = price
            self.stopPrice = stopPrice
            self.tradeSide = tradeSide
            self.volume = volume
        }
    }
    
    public struct Response: Codable {}
}
