//
//  ModifyOrderThaiEquityEndPoint.swift
//  APILayer
//
//  Created by Chung Cr on 02/01/2024.
//

import Networking

public struct ModifyOrderThaiEquityEndPoint: NetworkEndpoint {
    
    public static var service = ModifyOrderThaiEquityEndPoint()
    
    public var path: String = "/account/thai/equity/order/modification"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?
    
    public struct Request: Codable {
        private let accountId: Int
        private let orderId: Int
        private let pin: String
        private let price: String
        private let volume: Double
        
        public init(accountId: Int,
                    orderId: Int,
                    pin: String,
                    price: String,
                    volume: Double) {
            self.accountId = accountId
            self.orderId = orderId
            self.pin = pin
            self.price = price
            self.volume = volume
        }
    }
    
    public struct Response: Codable {}
}
