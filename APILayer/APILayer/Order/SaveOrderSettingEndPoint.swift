//
//  SaveOrderSettingEndPoint.swift
//  APILayer
//
//  Created by <PERSON> on 03/01/2024.
//

import Networking
import RxSwift

public struct SaveOrderSettingEndPoint: NetworkEndpoint {
    
    public static var service = SaveOrderSettingEndPoint()
    
    public var path: String = "/account/order/setting/save"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?
    
    public struct Request: Codable {
        private let nvdr: Bool
        private let orderConfirmation: Bool
        private let tradingPin: String
        private let tradingPinDuration: String
        private let tradingPinTimeStamp: Int
        
        public init(nvdr: Bool?,
                    orderConfirmation: Bool?,
                    tradingPin: String,
                    tradingPinDuration: String,
                    tradingPinTimeStamp: Int = Int(Date().timeIntervalSince1970 * 1000)) {
            self.nvdr = nvdr ?? false
            self.orderConfirmation = orderConfirmation ?? false
            self.tradingPin = tradingPin
            self.tradingPinDuration = tradingPinDuration
            self.tradingPinTimeStamp = tradingPinTimeStamp
        }
    }
    
    public struct Response: Codable {}
}
