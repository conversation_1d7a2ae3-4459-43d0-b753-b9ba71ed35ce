//
//  QuickOrderSaveEndPoint.swift
//  APILayer
//
//  Created by <PERSON><PERSON> on 1/3/67.
//
import RxSwift
import Storage
import Networking
import SharedData

public struct QuickOrderSaveEndPoint: NetworkEndpoint {

    public static var service = QuickOrderSaveEndPoint()

    public var path: String = "/account/quick/order/setting/save"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?

    public struct Request: Codable {
        public var id: String?
        public var name: String = ""
        public var orderSide: String = ""
        public var orderType: String = ""
        public var priceOption: String = ""
        public var priceValue: String = ""
        public var stopPriceOption: String?
        public var stopPriceValue: String?
        public var unitType: String = ""
        public var unitValue: String = ""

        public init() {}
    }

    public struct Response: Codable {}

    public mutating func call(parameter: Request) -> Observable<Response> {
//        return .just(Response()).delay(.milliseconds(500), scheduler: MainScheduler.instance)
        return request(parameters: parameter)
    }
}
