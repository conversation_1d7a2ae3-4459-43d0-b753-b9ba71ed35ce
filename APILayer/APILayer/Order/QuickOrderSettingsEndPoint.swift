//
//  QuickOrderSettingsEndPoint.swift
//  APILayer
//
//  Created by <PERSON><PERSON> on 1/3/67.
//
import RxSwift
import Storage
import Networking
import SharedData

public struct QuickOrderSettingsEndPoint: NetworkEndpoint {

    public static var service = QuickOrderSettingsEndPoint()

    public var path: String = "/account/quick/order/setting/query"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?

    public struct Request: Codable {}

    public struct Response: Codable {
        public var settings: [Setting]?
    }

    public struct Setting: Codable {
        public let id, name, orderSide, orderType: String?
        public let priceOption, priceValue, stopPriceOption, stopPriceValue: String?
        public let unitType, unitValue: String?
    }

    public mutating func call() -> Observable<Response> {
//        guard let result = MockData.quickOrderSettings.data(type: Response.self) else {
//            return .never()
//        }
//        return .just(result).delay(.milliseconds(500), scheduler: MainScheduler.instance)
        return request(parameters: Request())
    }
}
