//
//  ListAutoOrderEndPoint.swift
//  APILayer
//
//  Created by <PERSON> C<PERSON> on 11/01/2024.
//

import Networking
import RxSwift

public struct ListAutoOrderEndPoint: NetworkEndpoint {
    
    public static var service = ListAutoOrderEndPoint()
    
    public var path: String = "/account/auto/order/list"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?
    
    public struct Request: Codable {}
    
    public struct Response: Codable {
        public let autoOrderItem: [AutoOrderItem]?
    }
    
    public struct AutoOrderItem: Codable {
        public let symbol: String?
        public let tradingPlanId: Int
        public let autoOrderBase: AutoOrderBase?
        public let costPrice: String?
        public let currency: String?
        public let exchange: String?
        public let logo: String?
    }
        
    public struct AutoOrderBase: Codable {
        public let accountId: Int
        public let volume: String?
        public let orderType: String?
        public let orderPriceType: String?
        public let orderPriceDelta: String?
        public let cancelOtherBuy: Bool?
        public let cancelOtherSell: Bool?
        public let autoOrderName: String?
        public let autoOrderTemplateCode: String?
        public let tradingPlanStatus: String?
        public let startDate: Int?
        public let endDate: Int?
        public let buyStop: BuyStop?
        public let sellStop: SellStop?
        public let buyTrailingStop: BuyTrailingStop?
        public let sellTrailingStop: SellTrailingStop?
    }
       
    public struct BuyStop: Codable {
        public let stopPrice: String?
        public let stopPriceOperator: String?
    }
       
    public struct SellStop: Codable {
        public let takeProfitPrice: String?
        public let cutLossPrice: String?
        public let profitPreservePrice: String?
    }
       
    public struct BuyTrailingStop: Codable {
        public let startPriceType: String?
        public let startPrice: String?
        public let trailingType: String?
        public let trailingDelta: String?
    }
         
    public struct SellTrailingStop: Codable {
        public let startPrice: String?
        public let startPriceType: String?
        public let trailingType: String?
        public let trailingDelta: String?
        public let cutLossPrice: String?
    }
    
    public mutating func call() -> Observable<Response> {
        request(parameters: Request())
    }
}
