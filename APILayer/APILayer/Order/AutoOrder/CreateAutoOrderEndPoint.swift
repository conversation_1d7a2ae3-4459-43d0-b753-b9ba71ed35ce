//
//  CreateAutoOrderEndPoint.swift
//  APILayer
//
//  Created by <PERSON> on 18/01/2024.
//

import Networking
import SharedData

public struct CreateAutoOrderEndPoint: NetworkEndpoint {
    
    public static var service = CreateAutoOrderEndPoint()
    
    public var path: String = "/account/auto/order/create"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?
    
    public struct Request: Codable {
        private let autoOrderBase: AutoOrderBase
        private let exchange: String
        private let pin: String
        private let symbol: String
        
        public init(model: CreateAutoOrderModel, pin: String) {
            self.autoOrderBase = AutoOrderBase(from: model)
            self.exchange = model.exchange.queryValue
            self.pin = pin
            self.symbol = model.symbol
        }
    }
    
    // MARK: - Response
    public struct Response: Codable {
        public let tradingPlanId: Int?
    }
}
