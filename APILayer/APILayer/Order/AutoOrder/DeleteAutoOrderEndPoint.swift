//
//  DeleteAutoOrderEndPoint.swift
//  APILayer
//
//  Created by <PERSON> on 18/01/2024.
//

import Networking

public struct DeleteAutoOrderEndPoint: NetworkEndpoint {
    
    public static var service = DeleteAutoOrderEndPoint()
    
    public var path: String = "/account/auto/order/delete"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?
    
    public struct Request: Codable {
        private let accountId: Int
        private let pin: String
        private let tradingPlanId: Int
        
        public init(accountId: Int,
                    pin: String,
                    tradingPlanId: Int) {
            self.accountId = accountId
            self.pin = pin
            self.tradingPlanId = tradingPlanId
        }
    }
    
    public struct Response: Codable {}
}
