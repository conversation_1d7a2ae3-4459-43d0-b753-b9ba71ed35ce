//
//  CreateUpdateAutoOrderBase.swift
//  APILayer
//
//  Created by <PERSON> on 22/01/2024.
//

import SharedData

// MARK: - BuyStopModel
struct BuyStopRequest: Codable {
    var stopPrice: String?
    var stopPriceOperator: String?
    
    init(from model: BuyStopModel?) {
        stopPrice = model?.stopPrice
        stopPriceOperator = model?.stopPriceOperator?.queryValue
    }
}

// MARK: - SellStopModel
struct SellStopRequest: Codable {
    var cutLossPrice: String?
    var profitPreservePrice: String?
    var takeProfitPrice: String?
    
    init(from model: SellStopModel?) {
        cutLossPrice = model?.cutLossPrice
        profitPreservePrice = model?.profitPreservePrice
        takeProfitPrice = model?.takeProfitPrice
    }
}

// MARK: - BuyTrailingStopModel
struct BuyTrailingStopRequest: Codable {
    var startPrice: String?
    var startPriceType: String?
    var trailingDelta: String?
    var trailingType: String?
    
    init(from model: BuyTrailingStopModel?) {
        startPrice = model?.startPrice
        startPriceType = model?.startPriceType?.queryValue
        trailingDelta = model?.trailingDelta
        trailingType = model?.trailingType?.queryValue
    }
}

// MARK: - SellTrailingStopModel
struct SellTrailingStopRequest: Codable {
    var cutLossPrice: String?
    var startPrice: String?
    var startPriceType: String?
    var trailingDelta: String?
    var trailingType: String?
    
    init(from model: SellTrailingStopModel?) {
        cutLossPrice = model?.cutLossPrice
        startPrice = model?.startPrice
        startPriceType = model?.startPriceType?.queryValue
        trailingDelta = model?.trailingDelta
        trailingType = model?.trailingType?.queryValue
    }
}

// MARK: - AutoOrderBase model
struct AutoOrderBase: Codable {
    let accountId: Int
    let autoOrderName: String
    let autoOrderTemplateCode: String
    var buyStop: BuyStopRequest?
    var buyTrailingStop: BuyTrailingStopRequest?
    var sellStop: SellStopRequest?
    var sellTrailingStop: SellTrailingStopRequest?
    let cancelOtherBuy: Bool
    let cancelOtherSell: Bool
    let tradingPlanStatus: String
    let startDate: Int
    let endDate: Int
    let orderPriceDelta: String?
    let orderPriceType: String
    let orderType: String?
    let volume: String?
    
    init(from model: CreateAutoOrderModel) {
        self.accountId = model.accountId
        self.autoOrderName = model.autoOrderName ?? ""
        self.autoOrderTemplateCode = model.autoOrderTemplateCode.queryValue
        self.cancelOtherBuy = model.cancelOtherBuy
        self.cancelOtherSell = model.cancelOtherSell
        
        let status: AutoOrderState
        switch model.tradingPlanStatus {
        case .inactive:
            status = .inactive
            
        default:
            status = .active
        }
        self.tradingPlanStatus = status.queryValue
        self.startDate = model.startDate ?? 0
        self.endDate = model.endDate ?? 0
        self.orderPriceDelta = model.orderPriceDelta?.isEmpty != false ? nil : model.orderPriceDelta
        self.orderPriceType = model.orderPriceType?.queryValue ?? ""
        self.orderType = model.orderPriceType?.marketValue
        self.volume = model.volume
        
        switch model.autoOrderTemplateCode {
        case .buyStop:
            self.buyStop = BuyStopRequest(from: model.buyStop)
            
        case .sellStop:
            self.sellStop = SellStopRequest(from: model.sellStop)
            
        case .buyTrailingStop:
            self.buyTrailingStop = BuyTrailingStopRequest(from: model.buyTrailingStop)
            
        case .sellTrailingStop:
            self.sellTrailingStop = SellTrailingStopRequest(from: model.sellTrailingStop)
        }
    }
}
