//
//  ModifyAutoOrderEndPoint.swift
//  APILayer
//
//  Created by <PERSON> on 22/01/2024.
//

import Networking
import SharedData

public struct ModifyAutoOrderEndPoint: NetworkEndpoint {
    
    public static var service = ModifyAutoOrderEndPoint()
    
    public var path: String = "/account/auto/order/modify"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?
    
    public struct Request: Codable {
        private let autoOrderBase: AutoOrderBase
        private let exchange: String
        private let pin: String
        private let symbol: String
        private let tradingPlanId: Int
        
        public init(model: CreateAutoOrderModel, pin: String) {
            self.autoOrderBase = AutoOrderBase(from: model)
            self.exchange = model.exchange.queryValue
            self.pin = pin
            self.symbol = model.symbol
            self.tradingPlanId = model.autoOrderId
        }
    }
    
    // MARK: - Response
    public struct Response: Codable {
        public let tradingPlanId: Int?
    }
}
