//
//  OfflineOrderListEndPoint.swift
//  APILayer
//
//  Created by <PERSON><PERSON><PERSON> on 10/1/2567 BE.
//

import Networking
import SharedData
import Storage
import RxSwift

public struct OfflineOrderListEndPoint: NetworkEndpoint {
    public static var service = OfflineOrderListEndPoint()
    
    public var path: String = "/account/offline/order/list"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders? = [:]

    public struct Request: Codable {
        private let pageNum: Int
        private let pageSize: Int
        private let side: String
        private let start: Int
        private let end: Int
        private let market: String
        private let orderTimeSortBy: String
        
        public init(pageNum: Int = 1, // Page starts at 1
                    pageSize: Int = 20,
                    side: OrderSide? = nil,
                    start: Int,
                    end: Int,
                    market: String? = nil,
                    orderTimeSortBy: String? = nil ) {
            self.pageNum = pageNum
            self.pageSize = pageSize
            self.side = side?.queryValue ?? ""
            self.start = start
            self.end = end
            self.market = market ?? ""
            self.orderTimeSortBy = orderTimeSortBy ?? ""
        }
    }
    
    public struct Response: Codable {
        public let total: Int
        public let list: [Order]?
    }
    
    public struct Order: Codable {
        public let id: Int
        public let accountId: Int
        public let exchange: String?
        public let logo: String?
        public let currency: String?
        public let orderType: String?
        public let price: String?
        public let side: String?
        public let symbol: String?
        public let timestamp: Int?
        public let volume: String?
        public let stopPrice: String?
        public let status: String?
    }
    
    public mutating func call(parameter: Request) -> Observable<Response> {
        return request(parameters: parameter)
    }
}
