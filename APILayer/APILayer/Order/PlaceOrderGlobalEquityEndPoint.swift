//
//  PlaceOrderGlobalEquityEndPoint.swift
//  APILayer
//
//  Created by <PERSON> on 02/01/2024.
//

import Networking

public struct PlaceOrderGlobalEquityEndPoint: NetworkEndpoint {
    
    public static var service = PlaceOrderGlobalEquityEndPoint()
    
    public var path: String = "/account/global/equity/order"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?
    
    public struct Request: Codable {
        private let accountId: Int
        private let exchange: String
        private let symbol: String
        private let orderType: String
        private let pin: String
        private let price: String
        private let side: String
        private let stopPrice: String?
        private let volume: Double
        
        public init(accountId: Int,
                    exchange: String,
                    symbol: String,
                    orderType: String,
                    pin: String,
                    price: String,
                    side: String,
                    stopPrice: String? = nil,
                    volume: Double) {
            self.accountId = accountId
            self.exchange = exchange
            self.symbol = symbol
            self.orderType = orderType
            self.pin = pin
            self.price = price
            self.side = side
            self.stopPrice = stopPrice
            self.volume = volume
        }
    }
    
    public struct Response: Codable {}
}
