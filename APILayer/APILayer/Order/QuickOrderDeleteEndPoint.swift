//
//  QuickOrderDeleteEndPoint.swift
//  APILayer
//
//  Created by <PERSON><PERSON><PERSON> on 1/3/67.
//
import RxSwift
import Storage
import Networking
import SharedData

public struct QuickOrderDeleteEndPoint: NetworkEndpoint {

    public static var service = QuickOrderDeleteEndPoint()

    public var path: String = "/account/quick/order/setting/delete"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?

    public struct Request: Codable {
        private let id: String

        public init(id: String) {
            self.id = id
        }
    }

    public struct Response: Codable {}

    public mutating func call(parameter: Request) -> Observable<Response> {
//        return Observable.just(Response()).delay(.milliseconds(500), scheduler: MainScheduler.instance)
        return request(parameters: parameter)
    }
}
