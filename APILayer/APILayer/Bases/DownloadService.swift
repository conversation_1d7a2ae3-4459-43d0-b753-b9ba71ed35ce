//
//  DownloadService.swift
//  APILayer
//
//  Created by <PERSON> on 28/03/2024.
//

import Alamofire
import Networking
import RxSwift
import Storage

public struct DownloadConfiguration {
    let baseUrl: String
    let sessionConfiguration: URLSessionConfiguration
    let enableSSLPinning: Bool
    let allowInsecureConnection: Bool
    let debugLogging: Bool
    let evaluator: ServerTrustEvaluating
    
    public init(baseUrl: String,
                sessionConfiguration: URLSessionConfiguration,
                enableSSLPinning: Bool,
                allowInsecureConnection: Bool,
                debugLogging: Bool,
                evaluator: ServerTrustEvaluating = PinnedCertificatesTrustEvaluator()) {
        self.baseUrl = baseUrl
        self.sessionConfiguration = sessionConfiguration
        self.enableSSLPinning = enableSSLPinning
        self.allowInsecureConnection = allowInsecureConnection
        self.debugLogging = debugLogging
        self.evaluator = evaluator
    }
    
    func host() -> String {
        if #available(iOS 16.0, *) {
            return URL(string: baseUrl)?.host() ?? ""
        } else {
            return URL(string: baseUrl)?.host ?? ""
        }
    }
}

// MARK: - DownloadService
public struct DownloadService {
    
    public static var shared = DownloadService()
    
    private let configuration: DownloadConfiguration
    
    private lazy var serverTrustManager: ServerTrustManager? = {
        guard configuration.enableSSLPinning else { return nil }
        
        let shouldEvaluated = !configuration.allowInsecureConnection
        let host = configuration.host()
        let serverTrustManager = ServerTrustManager(allHostsMustBeEvaluated: shouldEvaluated,
                                                    evaluators: [host: configuration.evaluator])
        return serverTrustManager
    }()
    
    public lazy var session: Session = {
        return Session(configuration: configuration.sessionConfiguration,
                       serverTrustManager: serverTrustManager)
    }()
    
    init() {
        let session = URLSessionConfiguration.default
        session.timeoutIntervalForRequest = 60
        
        let urlEnv = APIConstants.shared.baseUrl
        
        configuration = DownloadConfiguration(
            baseUrl: urlEnv.url,
            sessionConfiguration: session,
            enableSSLPinning: urlEnv.enableSSLPinning,
            allowInsecureConnection: urlEnv.allowInsecureConnection,
            debugLogging: urlEnv.debugMode,
            evaluator: DisabledTrustEvaluator()
        )
    }
    
    mutating func download(endpoint: any NetworkEndpoint, request: Codable) -> Observable<Data> {
        guard
            let endpointUrl = endpoint.asURL(for: configuration.baseUrl)
        else {
            let networkError = NetworkError.invalidURL(endpoint.path)
            return .error(networkError)
        }
        
        var headers: [String: String] = [:]
        if let sid = LocalPreference.sessionId {
            headers["sid"] = sid
        }
        
        let task = session.download(endpointUrl,
                                    method: HTTPMethod(rawValue: endpoint.method.rawValue),
                                    parameters: try? request.toParameters(),
                                    encoding: URLEncoding.queryString,
                                    headers: HTTPHeaders(headers))
        
        return task.download()
    }
}

// MARK: - DownloadRequest
extension DownloadRequest {
    
    func download() -> Observable<Data> {
        Observable<Data>.create {
            self.handle(with: $0).resume()
            
            return Disposables.create {
                self.cancel()
            }
        }
    }
    
    func handle(with observer: AnyObserver<Data>) -> Self {
        responseData { responseData in
            switch responseData.result {
            case .success(let data):
                observer.onNext(data)
            case .failure(let error):
                let networkError = NetworkError.error(error.localizedDescription)
                observer.onError(networkError)
            }
        }
    }
}

// MARK: - Encodable
extension Encodable {
    
    func toParameters() throws -> Parameters {
        let encoder = JSONEncoder()
        encoder.keyEncodingStrategy = .useDefaultKeys
        let data = try encoder.encode(self)
        guard let dictionary = try JSONSerialization.jsonObject(with: data, options: .allowFragments) as? [String: Any] else {
            throw NSError()
        }
        return dictionary
    }
}
