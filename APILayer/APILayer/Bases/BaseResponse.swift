//
//  BaseResponse.swift
//  Data
//
//  Created by <PERSON><PERSON> on 11/2/66.
//

import Storage

// MARK: - BaseResponse
public struct BaseResponse<T: Decodable>: Decodable {
    public var code: String?
    public var message: String?
    public var response: T?
}

// MARK: - MockData
extension MockData {
    func data<T: Decodable>(type: T.Type) -> T? {
        guard let mockData = self.mock() else {
            return nil
        }
        do {
            let decoder = JSONDecoder()
            let result = try decoder.decode(BaseResponse<T>.self, from: mockData).response
            return result
        } catch {
            print(error)
            return nil
        }
    }
}

// MARK: - APIResponseWrapper
struct APIResponseWrapper<Response: Decodable>: Decodable {
    public let code: String
    public let message: String
    public let response: Response
}

public struct ErrorResponse: Decodable {
    public let errorCode: String
    public let errorMessage: String
}

public struct APIError: LocalizedError {
    public let code: String
    public let message: String

    public static let `default` = APIErro<PERSON>(code: "", message: "")

    init(code: String, message: String) {
        self.code = code
        self.message = message
    }

    public var errorDescription: String? {
        return "\(code): \(message)"
    }
}

// MARK: - CommonErrorCode
public enum CommonErrorCode: String {
    /// Incorrect username or password
    case TXCU000202
    
    public var message: String {
        switch self {
        case .TXCU000202:
            return "Your email or password seems incorrect. Please double check your credentials."
        }
    }
}
