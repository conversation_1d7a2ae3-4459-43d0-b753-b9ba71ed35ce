//
//  Extensions.swift
//  APILayer
//
//  Created by <PERSON> on 14/11/2023.
//

import Foundation
import RxSwift
import Networking
import Storage

public extension Observable where Element == NetworkResponse {
    
    func validate(network: NetworkService) -> Observable<Element> {
        self.flatMap { response -> Observable<Element> in
            // Session TimeOut
            if response.statusCode == 403 {
                network.cancelRequests()
                NotificationCenter.default.post(name: .sessionTimeout, object: nil)
                throw APIError(code: "key1044".localized(), message: "key1045".localized())
            }
            
            let decoder = JSONDecoder()
            do {
                let appResponse = try decoder.decode(AppResponse<EmptyResponse>.self, from: response.data)
                if appResponse.code == "0" {
                    // Save sid from response header
                    if let sid = response.headers?["sid"] {
                        LocalPreference.sid = sid
                    }
                    
                    return .just(response)
                } else {
                    let error = APIError(code: appResponse.code, message: appResponse.message)
                    if appResponse.code == "TXCU000203" {
                        network.cancelRequests()
                        NotificationCenter.default.post(name: .sessionTimeout, object: nil)
                        throw APIError(code: "key1046".localized(), message: "key1045".localized())
                    }
                    
                    return .error(error)
                }
            } catch {
                if let errorResponse = try? decoder.decode(ErrorResponse.self, from: response.data) {
                    throw APIError(code: errorResponse.errorCode, message: errorResponse.errorMessage)
                } else {
                    throw APIError(code: "Decoding", message: "An error occurs in decoding.")
                }
            }
        }
    }

    func decode<Response: Decodable>(with decoder: JSONDecoder = JSONDecoder()) -> Observable<Response> {
        self.flatMap { response -> Observable<Response> in
            do {
                let appResponse = try decoder.decode(APIResponseWrapper<Response>.self, from: response.data)
                return .just(appResponse.response)
            } catch {
                throw APIError(code: "Decoding", message: "An error occurs in decoding.")
            }
        }
    }
    
    func decodeCustom<Response: Decodable>(with decoder: JSONDecoder = JSONDecoder()) -> Observable<Response> {
        self.flatMap { response -> Observable<Response> in
            do {
                let appResponse = try decoder.decode(Response.self, from: response.data)
                return .just(appResponse)
            } catch {
                throw APIError(code: "Decoding", message: "An error occurs in decoding.")
            }
        }
    }
}
