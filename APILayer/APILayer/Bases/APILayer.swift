//
//  APILayer.swift
//  APILayer
//
//  Created by <PERSON><PERSON> on 11/8/66.
//
import RxSwift
import Networking
import Storage
import Utils
import Alamofire
import SharedData

final class APILayer {
    
    static private var networks = [String: NetworkService]()
    
    static private func network<T: NetworkEndpoint>(for endpoint: T) -> NetworkService {
        let urlEnv = APIConstants.shared.urlFor(endpoint: endpoint)
        if let network = networks[urlEnv.identifier] {
            return network
        }

        let session = URLSessionConfiguration.default
        session.timeoutIntervalForRequest = 60

        let configuration = NetworkConfiguration(
            baseUrl: urlEnv.url,
            sessionConfiguration: session,
            enableSSLPinning: urlEnv.enableSSLPinning,
            allowInsecureConnection: urlEnv.allowInsecureConnection,
            debugLogging: urlEnv.debugMode,
            evaluator: DisabledTrustEvaluator() // TODO: For testing purpose only, should remove for prod.
        )
        let network = NetworkService(configuration: configuration)
        networks[urlEnv.identifier] = network
        
        return network
    }
    
    static func defaultHeaders() -> RequestHeaders {
        var headers: [String: String] = [:]
        headers["Accept"] = "application/json"
        headers["Content-Type"] = "application/json"
        headers["Device"] = "iOS"
        headers["DeviceId"] = LocalPreference.deviceId
        headers["AppVersion"] = LocalPreference.appVersion
        headers["Language"] = LocalizeManager.currentLanguage.requestValue
        headers["IpAddress"] = APILayer.getIPAddress()
        
        if let sid = LocalPreference.sessionId {
            headers["sid"] = sid
        }
        
        return headers
    }
    
    static func request<Endpoint: NetworkEndpoint, Response: Codable>(endpoint: Endpoint,
                                                                      parameters: Endpoint.Request?) -> Observable<Response> {
        let service = network(for: endpoint)
        return service
            .request(endpoint: endpoint, request: parameters)
            .validate(network: service)
            .decode()
    }
    
    static func requestRaw<Endpoint: NetworkEndpoint>(endpoint: Endpoint,
                                                      parameters: Endpoint.Request?) -> Observable<NetworkResponse> {
        let service = network(for: endpoint)
        return service
            .request(endpoint: endpoint, request: parameters)
    }
    
    static func upload<Endpoint: NetworkEndpoint, Response: Codable>(endpoint: Endpoint,
                                                                     formData: [NetworkFormData]) -> Observable<Response> {
        let service = network(for: endpoint)
        return service
            .upload(endpoint: endpoint, formData: formData)
            .validate(network: service)
            .decode()
    }
    
    static func uploadBinary<Endpoint: NetworkEndpoint, Response: Codable>(endpoint: Endpoint,
                                                                           data: Data) -> Observable<Response> {
        let service = network(for: endpoint)
        return service
            .uploadBinary(endpoint: endpoint, data: data)
            .validate(network: service)
            .decode()
    }
}

// MARK: - Private for APILayer
private extension APILayer {
    
    static func getIPAddress() -> String? {
        var address: String?
        
        // Get list of all interfaces on the local machine:
        var ifaddr: UnsafeMutablePointer<ifaddrs>?
        guard getifaddrs(&ifaddr) == 0 else { return nil }
        guard let firstAddr = ifaddr else { return nil }
        
        // For each interface ...
        for ifptr in sequence(first: firstAddr, next: { $0.pointee.ifa_next }) {
            let interface = ifptr.pointee
            
            // Check for IPv4 or IPv6 interface:
            let addrFamily = interface.ifa_addr.pointee.sa_family
            if addrFamily == UInt8(AF_INET) || addrFamily == UInt8(AF_INET6) {
                
                // Check interface name
                let name = String(cString: interface.ifa_name)
                if InterfaceName.allCases.contains(where: { $0.rawValue == name }) {
                    // Convert interface address to a human readable string:
                    var hostname = [CChar](repeating: 0, count: Int(NI_MAXHOST))
                    getnameinfo(interface.ifa_addr, socklen_t(interface.ifa_addr.pointee.sa_len),
                                &hostname, socklen_t(hostname.count),
                                nil, socklen_t(0), NI_NUMERICHOST)
                    address = String(cString: hostname)
                }
            }
        }
        freeifaddrs(ifaddr)
        
        return address
    }
    
    /// Interface name
    enum InterfaceName: String, CaseIterable {
        // wifi = ["en0"]
        case en0
        // wired = ["en2", "en3", "en4"]
        case en2
        case en3
        case en4
        // cellular = ["pdp_ip0","pdp_ip1","pdp_ip2","pdp_ip3"]
        case pdpIp0 = "pdp_ip0"
        case pdpIp1 = "pdp_ip1"
        case pdpIp2 = "pdp_ip2"
        case pdpIp3 = "pdp_ip3"
    }
}

// MARK: - NetworkEndpoint implements requests
public extension NetworkEndpoint {
    
    mutating func request(parameters: Request) -> Observable<Response> {
        prepareHeaders(values: APILayer.defaultHeaders())
        prepareAuthorization()
        return APILayer.request(endpoint: self, parameters: parameters)
    }
    
    mutating func requestCustomResponse(parameters: Request) -> Observable<Response> {
        prepareHeaders(values: APILayer.defaultHeaders())
        prepareAuthorization()
        return APILayer.requestRaw(endpoint: self, parameters: parameters)
            .decodeCustom()
    }
    
    mutating func upload(formData: [NetworkFormData]) -> Observable<Response> {
        prepareHeaders(values: APILayer.defaultHeaders())
        prepareAuthorization()
        return APILayer.upload(endpoint: self, formData: formData)
    }

    mutating func uploadBinary(data: Data) -> Observable<Response> {
        prepareHeaders(values: APILayer.defaultHeaders())
        prepareAuthorization()
        return APILayer.uploadBinary(endpoint: self, data: data)
    }

    // TODO: Update later
    mutating func prepareAuthorization() {
        switch auth {
        case .basic:
            self.setAuthorization("")
        case .bearer:
            self.setAuthorization("")
        case .none:
            return
        }
    }
}

// MARK: - extension Bundle
internal extension Bundle {

    static var this: Bundle {
        Bundle(for: APILayer.self)
    }
}
