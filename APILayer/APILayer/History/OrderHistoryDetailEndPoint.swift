//
//  OrderDetailEndPoint.swift
//  APILayer
//
//  Created by <PERSON> on 28/11/24.
//

import Networking
import SharedData
import RxSwift

public struct OrderHistoryDetailEndPoint: NetworkEndpoint {
    
    public static var service = OrderHistoryDetailEndPoint()
    
    public var path: String = "/account/order/detail"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?
    
    public struct Request: Codable {
        private let orderId: Int
        
        init(orderId: Int) {
            self.orderId = orderId
        }
    }
    
    public typealias Response = OrderHistoryDetail
    
    public mutating func call(with orderId: Int) -> Observable<Response> {
        request(parameters: Request(orderId: orderId))
    }
}
