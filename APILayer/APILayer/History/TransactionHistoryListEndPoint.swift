//
//  TransactionHistoryListEndPoint.swift
//  APILayer
//
//  Created by <PERSON> on 28/11/24.
//

import Networking
import SharedData

public struct TransactionHistoryListEndPoint: NetworkEndpoint {
    
    public static var service = TransactionHistoryListEndPoint()
    
    public var path: String = "/account/transaction/list"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?
    
    public struct Request: Codable {
        private let transactionType: String
        private let transactionStatus: String
        private let startTime: Int
        private let endTime: Int
        private let currency: String
        
        public init(transactionType: TransactionHistoryType,
                    transactionStatus: TransactionHistoryStatus,
                    startTime: Int,
                    endTime: Int,
                    currency: TransactionHistoryCurrency) {
            self.transactionType = transactionType.queryValue
            self.transactionStatus = transactionStatus.queryValue
            self.startTime = startTime
            self.endTime = endTime
            self.currency = currency.queryValue
        }
    }
    
    public struct Response: Codable {
        public let transactionList: [TransactionHistoryOverview]?
    }
}
