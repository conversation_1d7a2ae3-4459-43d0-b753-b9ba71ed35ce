//
//  MyOrderListEndPoint.swift
//  APILayer
//
//  Created by <PERSON> C<PERSON> on 27/12/2023.
//
import Storage
import Networking
import SharedData

public struct MyOrderListEndPoint: NetworkEndpoint {
    public static var service = MyOrderListEndPoint()
    
    public var path: String = "/account/order/list"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders? = [:]
    
    public struct Request: Codable {
        private let pageNum: Int
        private let pageSize: Int
        private let side: String
        private let status: String
        private let start: Int
        private let end: Int
        
        public init(pageNum: Int = 1, // Page starts at 1
                    pageSize: Int = 20,
                    side: OrderSide? = nil,
                    status: OrderStatus? = nil,
                    start: Int,
                    end: Int) {
            self.pageNum = pageNum
            self.pageSize = pageSize
            self.side = side?.queryValue ?? ""
            self.status = status?.queryValue ?? ""
            self.start = start
            self.end = end
        }
    }
    
    public struct Response: Codable {
        public let total: Int
        public let orders: [Order]?
    }
    
    public struct Order: Codable {
        public let id: Int
        public let accountId: Int
        public let exchange: String?
        public let logo: String?
        public let currency: String?
        public let matchedUnit: String?
        public let matchedValue: String?
        public let matchedPrice: String?
        public let notMatchedUnit: String?
        public let orderTag: String?
        public let orderType: String?
        public let price: String?
        public let side: String?
        public let status: String?
        public let symbol: String?
        public let timestamp: Int?
        public let units: String?

        public func toOrderNotification() -> OrderNotification {
            return OrderNotification(
                id: id, accountId: accountId, exchange: exchange, logo: logo,
                currency: currency, matchedUnit: matchedUnit, matchedValue: matchedValue,
                matchedPrice: matchedPrice, notMatchedUnit: notMatchedUnit,
                orderTag: orderTag, orderType: orderType, price: price, side: side,
                status: status, symbol: symbol, timestamp: timestamp, units: units
            )
        }
    }
}
