//
//  OrderHistoryListEndPoint.swift
//  APILayer
//
//  Created by <PERSON> on 28/11/24.
//

import Networking
import SharedData

public struct OrderHistoryListEndPoint: NetworkEndpoint {
    
    public static var service = OrderHistoryListEndPoint()
    
    public var path: String = "/account/order/list"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?
    
    public struct Request: Codable {
        private let orderSide: String
        private let orderStatus: String
        private let productCategoryList: [String]
        private let orderDateFrom: Int
        private let orderDateTo: Int
        private let searchType: String
        private let keyword: String
        
        public init(orderSide: OrderHistorySide,
                    orderStatus: OrderHistoryStatus,
                    productCategoryList: [String] = [],
                    orderDateFrom: Int,
                    orderDateTo: Int,
                    searchType: String = "",
                    keyword: String = "") {
            self.orderSide = orderSide.queryValue
            self.orderStatus = orderStatus.queryValue
            self.productCategoryList = productCategoryList
            self.orderDateFrom = orderDateFrom
            self.orderDateTo = orderDateTo
            self.searchType = searchType
            self.keyword = keyword
        }
    }
    
    public struct Response: Codable {
        public let list: [OrderHistoryOverview]?
    }
}
