//
//  TransactionDetailEndPoint.swift
//  APILayer
//
//  Created by Chung <PERSON> on 28/11/24.
//

import Networking
import SharedData
import RxSwift

public struct TransactionDetailEndPoint: NetworkEndpoint {
    
    public static var service = TransactionDetailEndPoint()
    
    public var path: String = "/account/transaction/detail"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?
    
    public struct Request: Codable {
        private let transactionId: Int
        
        init(transactionId: Int) {
            self.transactionId = transactionId
        }
    }
    
    public typealias Response = TransactionHistoryDetail
    
    public mutating func call(with transactionId: Int) -> Observable<Response> {
        request(parameters: Request(transactionId: transactionId))
    }
}
