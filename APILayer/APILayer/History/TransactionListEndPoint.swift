//
//  TransactionListEndPoint.swift
//  APILayer
//
//  Created by <PERSON><PERSON><PERSON> on 1/10/67.
//
import RxSwift
import Storage
import Networking
import SharedData

public struct TransactionListEndPoint: NetworkEndpoint {

    public static var service = TransactionListEndPoint()

    public var path: String = "/account/transaction/query"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?

    public struct Request: Codable {
        public let endTime: Int
        public let startTime: Int
        public let paymentMethod: String
        public let transactionStatus: String
        public let transactionType: String

        public init(
            endTime: Int, startTime: Int,
            paymentMethod: String?,
            transactionStatus: String?,
            transactionType: String
        ) {
            self.endTime = endTime
            self.startTime = startTime
            self.paymentMethod = paymentMethod ?? ""
            self.transactionStatus = transactionStatus ?? ""
            self.transactionType = transactionType
        }
    }

    public struct Response: Codable {
        public let transactionList: [Transaction]?
    }

    public struct Transaction: Codable {
        public let amount, paymentMethod, status: String?
        public let transactionId, transactionTimestamp: Int?
        public let transactionType: String?
        public let withdrawBankAccount: BankAccount?

        public func toTransactionNotification() -> TransactionNotification {
            return TransactionNotification(
                amount: amount, paymentMethod: paymentMethod,
                status: status, transactionId: transactionId,
                transactionTimestamp: transactionTimestamp,
                transactionType: transactionType,
                withdrawBankAccount: BankAccountNotification(
                    bankAccount: withdrawBankAccount?.bankAccount,
                    bankAccountId: withdrawBankAccount?.bankAccountId,
                    bankCode: withdrawBankAccount?.bankCode,
                    bankLogo: withdrawBankAccount?.bankLogo,
                    bankShortName: withdrawBankAccount?.bankShortName
                )
            )
        }
    }

    public struct BankAccount: Codable {
        public let bankAccount: String?
        public let bankAccountId: Int?
        public let bankCode, bankLogo, bankShortName: String?
    }

    public mutating func call(parameter: Request) -> Observable<Response> {
//        guard let result = MockData.transactionList.data(type: Response.self) else {
//            return .never()
//        }
//        return .just(result).delay(.milliseconds(500), scheduler: MainScheduler.instance)
        return request(parameters: parameter)
    }
}
