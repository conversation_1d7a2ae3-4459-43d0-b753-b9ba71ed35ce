//
//  FileDownloadEndPoint.swift
//  APILayer
//
//  Created by <PERSON> on 27/11/24.
//

import Networking
import Storage
import RxSwift

public struct FileDownloadEndPoint: NetworkEndpoint {
    
    public static var service = FileDownloadEndPoint()
    
    public var path: String = "/common/file"
    public var method: RequestMethod = .get
    public var encoding: RequestEncoding = .query
    public var headers: RequestHeaders? 
    
    public struct Request: Codable {
        private let fileKey: String
        
        public init(fileKey: String) {
            self.fileKey = fileKey
        }
    }
    
    public struct Response: Codable {}
    
    public mutating func download(fileKey: String) -> Observable<Data> {
        DownloadService.shared.download(endpoint: self,
                                        request: Request(fileKey: fileKey))
    }
}
