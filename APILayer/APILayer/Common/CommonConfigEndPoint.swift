//
//  CommonConfigEndPoint.swift
//  APILayer
//
//  Created by Chung <PERSON> on 24/2/25.
//

import Networking
import RxSwift

public struct CommonConfigEndPoint: NetworkEndpoint {
    
    public static var service = CommonConfigEndPoint()
    
    public var path: String = "/common/config"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?
    
    public struct Request: Codable {}
    
    public struct Response: Codable {
        public let supportMobile: String?
        public let supportEmail: String?
    }
    
    public mutating func call() -> Observable<Response> {
        request(parameters: Request())
    }
}
