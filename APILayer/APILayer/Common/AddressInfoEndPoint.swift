//
//  AddressInfoEndPoint.swift
//  APILayer
//
//  Created by <PERSON> C<PERSON> on 06/02/2024.
//

import Networking

public struct AddressInfoEndPoint: NetworkEndpoint {
    
    public static var service = AddressInfoEndPoint()
    
    public var path: String = "/v1/wallet/common/get-option-info"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?
    
    public struct Request: Codable {
        private let optionClass: OptionClass
        private var langIdentify: String = "en"
        private let parentClassId: String
        private let optionIdList: [String]
        
        public init(optionClass: OptionClass,
                    parentClassId: String = "",
                    optionIdList: [String]) {
            self.optionClass = optionClass
            self.parentClassId = parentClassId
            self.optionIdList = optionIdList
        }
    }
    
    public struct Response: Codable {
        public let records: [Record]?
    }
    
    public struct Record: Codable {
        public let optionId: String?
        public let optionClass: OptionClass?
        public let optionName: String?
        public let parentClassId: String?
    }
    
    public enum OptionClass: String, Codable {
        case zipcode
        case subdistrict
        case district
        case province
    }
}
