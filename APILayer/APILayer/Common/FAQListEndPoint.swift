//
//  FAQListEndPoint.swift
//  APILayer
//
//  Created by <PERSON> on 12/03/2024.
//

import Networking
import RxSwift

public struct FAQListEndPoint: NetworkEndpoint {
    
    public static var service = FAQListEndPoint()
    
    public var path: String = "/v1/common/faq/list"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?
    
    public struct Request: Codable {}
    
    public struct Response: Codable {
        public let faqList: [FaqCategory]?
    }
    
    public struct FaqCategory: Codable {
        public let id: Int?
        public let title: String?
        public let list: [FaqItem]?
    }
    
    public struct FaqItem: Codable {
        public let id: Int?
        public let question: String?
        public let answer: Answer?
    }
    
    public struct Answer: Codable {
        public let title: String?
        public let answerList: [AnswerSubItem]?
    }
    
    public struct AnswerSubItem: Codable {
        public let title: String?
        public let contents: [String]?
    }
    
    public mutating func fetch() -> Observable<Response> {
        request(parameters: Request())
    }
}
