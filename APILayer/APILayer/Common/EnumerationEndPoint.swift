//
//  EnumerationEndPoint.swift
//  APILayer
//
//  Created by <PERSON> on 25/6/24.
//

import RxSwift
import Networking
import Storage
import SharedData

public struct EnumerationEndPoint: NetworkEndpoint {
    public static var service = EnumerationEndPoint()
    
    public var path: String = "/common/enumeration"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?
    
    
    public struct Request: Codable {
        private let codeList: [String]
        
        public init(keys: [EnumerationKey]) {
            codeList = keys.map { $0.rawValue }
        }
    }
    
    public struct Response: Codable {
        public let list: [ObjectList]
    }
    
    public struct ObjectList: Codable {
        public let code: String
        public let list: [Object]
    }
    
    public struct Object: Codable {
        public let value: String
        public let desc: String
        public let sort: Int
    }
    
    
    private func mapCountryCodes(from response: Response) -> [CountryCode] {
        guard
            let objectList = response.list.first?.list,
            !objectList.isEmpty
        else { return [] }
        
        return objectList.map { object in
            CountryCode(id: object.sort,
                        countryName: object.desc,
                        code: object.value)
        }
        .sorted(by: { $0.id < $1.id })
    }
}

// MARK: - Public helper methods
public extension EnumerationEndPoint {
    
    mutating func call(with keys: [EnumerationKey]) -> Observable<[ObjectList]> {
        request(parameters: Request(keys: keys))
            .map { $0.list }
    }
    
    mutating func getCountryCodes() -> Observable<Void> {
        request(parameters: .init(keys: [.mobileRegion]))
            .map(mapCountryCodes(from:))
            .do(onNext: {
                LocalPreference.saveCountryCodes($0)
            })
            .mapToVoid()
    }
    
    mutating func fetchAllocationClasses() -> Observable<Void> {
        guard InstrumentInvestmenentModels.allocationClasses.isEmpty else { return .just(()) }
        
        return call(with: [.assetClass])
            .map {
                ($0.first?.list ?? []).map {
                    AllocationClass(title: $0.desc,
                                    queryValue: $0.value,
                                    sort: $0.sort)
                }
            }
            .do(onNext: {
                InstrumentInvestmenentModels.allocationClasses = $0
            })
            .mapToVoid()
    }
    
    mutating func fetchProdCategories() -> Observable<Void> {
        guard InstrumentInvestmenentModels.productCategories.isEmpty else { return .just(()) }
        
        return call(with: [.prodCategory])
            .map {
                ($0.first?.list ?? []).map {
                    ProductCategory(title: $0.desc,
                                    queryValue: $0.value,
                                    sort: $0.sort)
                }
            }
            .do(onNext: {
                InstrumentInvestmenentModels.productCategories = $0
            })
            .mapToVoid()
    }
}

// MARK: - EnumerationKey
public enum EnumerationKey: String {
    case identityChina = "IDENTITY_REGION_CHINA"
    case identityHK = "IDENTITY_REGION_HK"
    
    case identityOthers = "IDENTITY_REGION_OTHERS"
    case broker = "BROKER"
    
    case region = "REGION"
    case mobileRegion = "MOBILE_REGION"
    case bankAccType = "BANK_ACCOUNT_TYPE"
    case prodCategory = "PRODUCT_CATEGORY"
    case assetClass = "ASSET_CLASS"
    case industry = "INDUSTRY"
    case education = "EDUCATION"
    case deliveryMethod = "STATEMENT_DELIVERY_METHOD"
    case currency = "CURRENCY"
}
