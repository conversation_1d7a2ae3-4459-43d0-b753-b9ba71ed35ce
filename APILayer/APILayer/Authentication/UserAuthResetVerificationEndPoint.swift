//
//  UserAuthResetVerificationEndPoint.swift
//  APILayer
//
//  Created by <PERSON><PERSON> on 7/2/67.
//
import Networking
import RxSwift

public struct UserAuthResetVerificationEndPoint: NetworkEndpoint {

    public static var service = UserAuthResetVerificationEndPoint()

    public var path: String = "/user/auth/reset/verification"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?

    public struct Request: Codable {
        public let username, type, otpType, otp: String
        public let refCode: String

        public init(username: String, type: String, otpType: String, otp: String, refCode: String) {
            self.username = username
            self.type = type
            self.otpType = otpType
            self.otp = otp
            self.refCode = refCode
        }
    }

    public struct Response: Codable {
        public let token: String?
    }

    public mutating func call(parameter: Request) -> Observable<Response> {
//        return .just(Response()).delay(.milliseconds(200), scheduler: MainScheduler.instance)
        return request(parameters: parameter)
    }
}
