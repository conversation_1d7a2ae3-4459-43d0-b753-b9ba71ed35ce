//
//  UserTwoFAEndPoint.swift
//  APILayer
//
//  Created by <PERSON><PERSON><PERSON> on 7/1/67.
//
import Networking
import RxSwift

public struct UserTwoFAEndPoint: NetworkEndpoint {

    public static var service = UserTwoFAEndPoint()

    public var path: String = "/user/2fa"
    public var method: RequestMethod = .put
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?

    public struct Request: Codable {
        public let action, otpType, otp, refCode: String

        public init(action: String, otpType: String, otp: String, refCode: String) {
            self.action = action
            self.otpType = otpType
            self.otp = otp
            self.refCode = refCode
        }
    }

    public struct Response: Codable {

    }

    public mutating func call(parameter: Request) -> Observable<Response> {
//        return .just(Response()).delay(.milliseconds(200), scheduler: MainScheduler.instance)
        return request(parameters: parameter)
    }
}
