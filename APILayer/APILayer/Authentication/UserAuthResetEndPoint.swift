//
//  UserAuthResetEndPoint.swift
//  APILayer
//
//  Created by <PERSON><PERSON><PERSON> on 7/2/67.
//
import Networking
import RxSwift

public struct UserAuthResetEndPoint: NetworkEndpoint {
    
    public static var service = UserAuthResetEndPoint()
    
    public var path: String = "/user/auth/reset"
    public var method: RequestMethod = .put
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?
    
    public struct Request: Codable {
        private let token: String
        private let deviceId: String
        private let password: String?
        private let pin: String?
        
        public init(token: String,
                    deviceId: String,
                    password: String? = nil,
                    pin: String? = nil) {
            self.token = token
            self.deviceId = deviceId
            self.password = password
            self.pin = pin
        }
    }
    
    public struct Response: Codable {}
    
    public mutating func call(parameter: Request) -> Observable<Response> {
        request(parameters: parameter)
    }
}

