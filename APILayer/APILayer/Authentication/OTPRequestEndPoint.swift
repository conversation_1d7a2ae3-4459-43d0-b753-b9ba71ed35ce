//
//  OTPRequestEndPoint.swift
//  APILayer
//
//  Created by <PERSON><PERSON><PERSON> on 6/27/67.
//
import RxSwift
import Networking
import SharedData
import Storage

public struct OTPRequestEndPoint: NetworkEndpoint {
    
    public static var service = OTPRequestEndPoint()
    
    public var path: String = "/common/otp"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?
    
    public struct Request: Codable {
        private let bizType,
                    otpType,
                    otpAddress: String
        private let mobileRegion: String?
        
        public init(bizType: String,
                    otpType: String,
                    otpAddress: String,
                    mobileRegion: String? = nil) {
            self.bizType = bizType
            self.otpType = otpType
            self.otpAddress = otpAddress
            self.mobileRegion = mobileRegion
        }
    }
    
    public struct Response: Codable {
        public var refCode: String?
        public var expireSeconds: Int?
    }
    
    public mutating func call(parameter: Request) -> Observable<Response> {
        request(parameters: parameter)
    }
    
    public mutating func requestOTP(for screenType: OTPScreenType) -> Observable<Response> {
        var bizType: String = ""
        var otpType: String = ""
        var otpAddress: String = ""
        var mobileRegion: String?
        
        switch screenType {
        case .twoFactorSetUp(let type):
            bizType = BizType.config2FA.rawValue
            otpType = type.rawValue
            
            switch type {
            case .mobile:
                otpAddress = Keychain.userInformation?.basic?.base?.phoneNumber ?? ""
                mobileRegion = Keychain.userInformation?.basic?.base?.mobileRegion
            case .email:
                otpAddress = Keychain.userInformation?.basic?.base?.email ?? ""
            }
            
        case .twoFactorLogin(let type):
            bizType = BizType.login2FA.rawValue
            otpType = type.rawValue
            otpAddress = switch type {
            case .mobile:
                Keychain.userMobile ?? ""
            case .email:
                Keychain.userName ?? ""
            }
            
        case .walletCashWithdraw:
            bizType = BizType.cashWithdraw.rawValue
            otpType = OTPAuthType.email.rawValue
            otpAddress = Keychain.userName ?? ""
            
        case .walletDeposit:
            bizType = BizType.cashDeposit.rawValue
            otpType = OTPAuthType.email.rawValue
            otpAddress = Keychain.userName ?? ""
        }
        
        return request(parameters: Request(bizType: bizType,
                                           otpType: otpType,
                                           otpAddress: otpAddress,
                                           mobileRegion: mobileRegion))
        
    }
}
