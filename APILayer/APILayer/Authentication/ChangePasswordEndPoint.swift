//
//  ChangePasswordEndPoint.swift
//  APILayer
//
//  Created by <PERSON> on 11/03/2024.
//

import Networking

public struct ChangePasswordEndPoint: NetworkEndpoint {
    
    public static var service = ChangePasswordEndPoint()
    
    public var path: String = "/user/password"
    public var method: RequestMethod = .put
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?
    
    
    public struct Request: Codable {
        private let password: String
        private let token: String
        
        public init(password: String, token: String) {
            self.password = password
            self.token = token
        }
    }
    
    public struct Response: Codable {}
}
