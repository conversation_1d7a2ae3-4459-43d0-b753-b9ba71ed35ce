//
//  UserAuthEndPoint.swift
//  APILayer
//
//  Created by <PERSON><PERSON><PERSON> on 7/1/67.
//
import Networking
import RxSwift

public struct UserAuthEndPoint: NetworkEndpoint {
    
    public static var service = UserAuthEndPoint()
    
    public var path: String = "/user/auth"
    public var method: RequestMethod = .put
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?
    
    public struct Request: Codable {
        private let password,
                    deviceId: String
        private let newPassword,
                    newPin: String?
        
        public init(password: String,
                    newPassword: String?,
                    newPin: String?,
                    deviceId: String) {
            self.password = password
            self.newPassword = newPassword
            self.newPin = newPin
            self.deviceId = deviceId
        }
    }
    
    public struct Response: Codable {}
    
    public mutating func call(parameter: Request) -> Observable<Response> {
        request(parameters: parameter)
    }    
    
    public mutating func request(password: String,
                                 deviceId: String,
                                 newPassword: String? = nil,
                                 newPin: String? = nil) -> Observable<Response> {
        request(parameters: Request(password: password,
                                    newPassword: newPassword,
                                    newPin: newPin,
                                    deviceId: deviceId))
    }
}
