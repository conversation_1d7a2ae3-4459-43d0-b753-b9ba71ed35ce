//
//  OCRRequestEndPoint.swift
//  APILayer
//
//  Created by <PERSON><PERSON><PERSON> on 6/27/67.
//
import Networking
import RxSwift

/// Send uploaded dosKey of Id card to get card number from BE
public struct OCRRequestEndPoint: NetworkEndpoint {
    
    public static var service = OCRRequestEndPoint()
    
    public var path: String = "/common/ocr"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?
    
    public struct Request: Codable {
        private let idType: String
        /// dosKey from uploaded id card image
        private let idDosKey: String
        
        public init(idType: String, idDosKey: String) {
            self.idType = idType
            self.idDosKey = idDosKey
        }
    }
    
    public struct Response: Codable {
        public let idNumber: String?
    }
    
    public mutating func call(with idType: String, idDosKey: String) -> Observable<String?> {
        request(parameters: Request(idType: idType, idDosKey: idDosKey))
            .map { $0.idNumber }
    }
}
