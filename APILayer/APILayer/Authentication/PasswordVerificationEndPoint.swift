//
//  PasswordVerificationEndPoint.swift
//  APILayer
//
//  Created by <PERSON> on 11/03/2024.
//

import Networking

public struct PasswordVerificationEndPoint: NetworkEndpoint {
    
    public static var service = PasswordVerificationEndPoint()
    
    public var path: String = "/user/password/verification"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?
    
    public struct Request: Codable {
        private let otp: String
        private let otpType: String
        private let username: String
        
        public init(otp: String, otpType: String, username: String) {
            self.otp = otp
            self.otpType = otpType
            self.username = username
        }
    }
    
    public struct Response: Codable {
        public let token: String
    }
}
