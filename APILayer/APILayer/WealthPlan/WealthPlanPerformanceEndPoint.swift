//
//  WealthPlanPerformanceEndPoint.swift
//  APILayer
//
//  Created by <PERSON> on 12/11/24.
//

import Networking
import RxSwift
import SharedData

public struct WealthPlanPerformanceEndPoint: NetworkEndpoint {
    
    public static var service = WealthPlanPerformanceEndPoint()
    
    public var path: String = "/user/wealth-plan/performance"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?
    
    public struct Request: Codable {
        private let wealthPlanId: Int
        private let period: WealthPlanPeriod
        private let summaryBy: SummaryType
        
        public init(wealthPlanId: Int,
                    period: WealthPlanPeriod,
                    summaryBy: SummaryType) {
            self.wealthPlanId = wealthPlanId
            self.period = period
            self.summaryBy = summaryBy
        }
    }
    
    public struct Response: Codable {
        public let overallData: [WealthPlanData]?
        public let summaryList: [Summary]?
    }
    
    public struct Summary: Codable {
        public let summaryName: String?
        public let assetList: [Asset]?
        public let dataList: [WealthPlanData]?
    }
    
    public struct Asset: Codable {
        public let symbol: String?
        public let name: String?
        public let logo: String?
        public let exchange: String?
        public let riskLevel: String?
        public let currency: String?
        public let dataList: [WealthPlanData]?
        
        enum CodingKeys: String, CodingKey {
            case symbol = "Symbol"
            case name
            case logo
            case exchange
            case riskLevel
            case currency
            case dataList
        }
    }
    
    public struct WealthPlanData: Codable {
        public let d: String?
        public let v: String?
        public let r: String?
        public let rp: String?
        
        public var date: Date {
            d?.toDate(withFormat: "yyyy-MM-dd") ?? Date()
        }
    }
}
