//
//  WealthPlanListEndPoint.swift
//  APILayer
//
//  Created by <PERSON> on 12/11/24.
//

import Networking
import RxSwift
import SharedData

public struct WealthPlanListEndPoint: NetworkEndpoint {
    
    public static var service = WealthPlanListEndPoint()
    
    public var path: String = "/user/wealth-plan/list"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?
    
    public struct Request: Codable {}
    
    public struct Response: Codable {
        public let wealthPlanList: [WealthPlanOverview]?
    }
    
    public mutating func call() -> Observable<Response> {
        request(parameters: Request())
    }
}
