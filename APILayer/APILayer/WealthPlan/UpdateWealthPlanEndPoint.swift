//
//  UpdateWealthPlanEndPoint.swift
//  APILayer
//
//  Created by <PERSON> on 12/11/24.
//

import Networking
import RxSwift
import SharedData

public struct UpdateWealthPlanEndPoint: NetworkEndpoint {
    
    public static var service = UpdateWealthPlanEndPoint()
    
    public var path: String = "/user/wealth-plan"
    public var method: RequestMethod = .put
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?
    
    public struct Request: Codable {
        private let name: String?
        private let initialAmount: String?
        private let initialDate: Int?
        private let portfolio: [Portfolio]?
        private let benchmarkList: [Int]?
        private let description: String?
        private let action: WealthPlanAction
        private let id: Int
        
        public init(name: String? = nil,
                    initialAmount: String? = nil,
                    initialDate: Int? = nil,
                    portfolio: [Portfolio]? = nil,
                    benchmarkList: [Int]? = nil,
                    description: String? = nil,
                    action: WealthPlanAction,
                    id: Int) {
            self.name = name
            self.initialAmount = initialAmount
            self.initialDate = initialDate
            self.portfolio = portfolio
            self.benchmarkList = benchmarkList
            self.description = description
            self.action = action
            self.id = id
        }
    }
    
    public struct Response: Codable {}
    
    public struct Portfolio: Codable {
        private let instrumentId: Int
        private let allocation: String
        
        public init(instrumentId: Int, allocation: String) {
            self.instrumentId = instrumentId
            self.allocation = allocation
        }
    }
}
