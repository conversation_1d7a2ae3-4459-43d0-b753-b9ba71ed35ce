//
//  WealthPlanDetailEndPoint.swift
//  APILayer
//
//  Created by <PERSON> on 12/11/24.
//

import Networking
import RxSwift
import SharedData

public struct WealthPlanDetailEndPoint: NetworkEndpoint {
    
    public static var service = WealthPlanDetailEndPoint()
    
    public var path: String = "/user/wealth-plan"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?
    
    public struct Request: Codable {
        private let wealthPlanId: Int
        private let summaryBy: String
        
        public init(wealthPlanId: Int, summaryBy: SummaryType) {
            self.wealthPlanId = wealthPlanId
            self.summaryBy = summaryBy.rawValue
        }
    }
    
    public struct Response: Codable {
        public let asOf: Int?
        public let id: Int?
        public let name: String?
        public let initialAmount: String?
        public let initialDate: Int?
        public let mtdReturn: String?
        public let ytdReturn: String?
        public let itdReturn: String?
        public let beta: String?
        public let sharp: String?
        public let annualizedReturn: String?
        public let annualizedVolatility: String?
        public let totalBalance: String?
        public let cashBalance: String?
        public let unrealizedGl: String?
        public let unrealizedGlRate: String?
        public let currency: String?
        public let description: String?
        public let returnList: [WealthPlanReturn]?
        public let summaryList: [WealthPlanSummary]?
        public let benchmarkList: [WealthPlanBenchmark]?
    }
    
    public struct WealthPlanReturn: Codable {
        public let name: String?
        public let returnValue: String?
        public let volatilityValue: String?
    }
    
    public struct WealthPlanSummary: Codable {
        public let summaryName: String?
        public let marketValue: String?
        public let unrealizedGl: String?
        public let unrealizedGlRate: String?
        public let currency: String?
        public let percentage: String?
        public let riskTag: String?
        public let assetList: [WealthPlanAsset]?
    }
    
    public mutating func call(with wealthPlanId: Int, type: SummaryType) -> Observable<Response> {
        request(parameters: Request(wealthPlanId: wealthPlanId, summaryBy: type))
    }
}

// MARK: - Public functions
public extension WealthPlanDetailEndPoint.Response {
    
    func sortedSummaries() -> [WealthPlanDetailEndPoint.WealthPlanSummary] {
        summaryList?.sorted(by: { $0.percentage?.toNumber() ?? 0 > $1.percentage?.toNumber() ?? 0 }) ?? []
    }
}
