//
//  ContentBannerListEndPoint.swift
//  APILayer
//
//  Created by <PERSON> on 6/2/25.
//


import Networking
import SharedData
import RxSwift

public struct ContentBannerListEndPoint: NetworkEndpoint {
    
    public static var service = ContentBannerListEndPoint()
    
    public var path: String = "/content/banner/list"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?
    
    public struct Request: Codable {}
    
    public struct Response: Codable {
        let list: [Banner]?
    }
    
    public mutating func call() -> Observable<[Banner]> {
        request(parameters: Request())
            .map { $0.list ?? [] }
    }
}
