//
//  GetMarketNewsEndPoint.swift
//  APILayer
//
//  Created by <PERSON> on 29/02/2024.
//

import Networking
import Storage
import RxSwift

public struct GetMarketNewsEndPoint: NetworkEndpoint {
    
    public static var service = GetMarketNewsEndPoint()
    
    public var path: String = "/v1/news/all"
    public var method: RequestMethod = .get
    public var encoding: RequestEncoding = .query
    public var headers: RequestHeaders?
    
    public struct Request: Codable {
        private var filterEntities: Bool = true
        private var language: String = "en"
        private var apiToken: String = "oAqcE6e0oPdE6Qq9U6fNlhipRMap2RIUO2ccXTRH"
        private let publishedOn: String
        private var sort: String = "published_on"
        private var sortOrder: String = "desc"
        
        // swiftlint:disable:next nesting
        enum CodingKeys: String, CodingKey {
            case filterEntities = "filter_entities"
            case language
            case apiToken = "api_token"
            case publishedOn = "published_on"
            case sort
            case sortOrder = "sort_order"
        }
        
        public init(publishedOn: String) {
            self.publishedOn = publishedOn
        }
    }
    
    public struct Response: Codable {
        public let data: [News]
    }
    
    public mutating func call(parameter: Request) -> Observable<Response> {
        return requestCustomResponse(parameters: parameter)
    }
}
