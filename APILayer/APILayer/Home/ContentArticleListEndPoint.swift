//
//  ContentArticleListEndPoint.swift
//  APILayer
//
//  Created by Augment Agent on 23/08/2025.
//

import Networking
import RxSwift
import SharedData

public struct ContentArticleListEndPoint: NetworkEndpoint {
    
    public static var service = ContentArticleListEndPoint()
    
    public var path: String = "/api/content/article/list"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?
    
    public struct Request: Codable {
        public let pageNo: Int
        public let pageSize: Int
        
        public init(pageNo: Int = 1, pageSize: Int = 10) {
            self.pageNo = pageNo
            self.pageSize = pageSize
        }
    }
    
    public struct Response: Codable {
        public let total: Int
        public let list: [MeritReportResponse]
    }
    
    public struct MeritReportResponse: Codable {
        public let id: Int
        public let uuid: String
        public let title: String
        public let status: Int
        public let webUrl: String
        public let banner: Int
        public let created: Int
        public let published: Int64
    }
    
    public mutating func call(pageNo: Int = 1, pageSize: Int = 10) -> Observable<Response> {
        request(parameters: Request(pageNo: pageNo, pageSize: pageSize))
    }
    
    public mutating func callForMeritReports(pageNo: Int = 1, pageSize: Int = 10) -> Observable<[MeritReportResponse]> {
        call(pageNo: pageNo, pageSize: pageSize)
            .map { response in
                response.list
            }
    }
}
