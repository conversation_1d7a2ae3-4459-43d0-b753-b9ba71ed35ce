// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 56;
	objects = {

/* Begin PBXBuildFile section */
		0C8F52A32E5C2102003DAAE0 /* MarketProductCategoryListEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0C8F52A22E5C2102003DAAE0 /* MarketProductCategoryListEndPoint.swift */; };
		1C60BDEBB9F9D8ED5DA46D25 /* Pods_APILayer.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 6F95A0885E483161EB303ACF /* Pods_APILayer.framework */; };
		830D0F362D54ADBC00B5A130 /* ContentBannerListEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = 830D0F352D54ADBC00B5A130 /* ContentBannerListEndPoint.swift */; };
		831943CD2CCCD88F00A46862 /* MarketInstrumentDetailEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = 831943CC2CCCD88F00A46862 /* MarketInstrumentDetailEndPoint.swift */; };
		831C07E02C2A7D9700A1BB94 /* EnumerationEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = 831C07DF2C2A7D9700A1BB94 /* EnumerationEndPoint.swift */; };
		832D80032C35A25C00C37931 /* UserProfileEditEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = 832D80022C35A25C00C37931 /* UserProfileEditEndPoint.swift */; };
		832D80052C35BD7A00C37931 /* UserEmploymentEditEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = 832D80042C35BD7A00C37931 /* UserEmploymentEditEndPoint.swift */; };
		832D80072C35C36400C37931 /* UserBankAccountEditEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = 832D80062C35C36300C37931 /* UserBankAccountEditEndPoint.swift */; };
		832D800F2C3658EE00C37931 /* QueryUserBankListEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = 832D800E2C3658EE00C37931 /* QueryUserBankListEndPoint.swift */; };
		83319F352CE342A000F78916 /* WealthPlanListEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = 83319F342CE342A000F78916 /* WealthPlanListEndPoint.swift */; };
		83319F382CE342DE00F78916 /* WealthPlanDetailEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = 83319F372CE342DE00F78916 /* WealthPlanDetailEndPoint.swift */; };
		83319F3A2CE342F500F78916 /* UpdateWealthPlanEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = 83319F392CE342F500F78916 /* UpdateWealthPlanEndPoint.swift */; };
		83319F3C2CE3430D00F78916 /* WealthPlanPerformanceEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = 83319F3B2CE3430D00F78916 /* WealthPlanPerformanceEndPoint.swift */; };
		833B79122C3FAE4600EAEE0D /* UserDocuSignEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = 833B79112C3FAE4600EAEE0D /* UserDocuSignEndPoint.swift */; };
		833DB8EE2D410025002FCCC7 /* OnboardingCheckEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = 833DB8ED2D410025002FCCC7 /* OnboardingCheckEndPoint.swift */; };
		83443CA52D03330C00BDC625 /* UpdateCustomerStatusEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = 83443CA42D03330C00BDC625 /* UpdateCustomerStatusEndPoint.swift */; };
		8379B3FD2D15E17D004D3006 /* QueryUserProfileEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8379B3FC2D15E17D004D3006 /* QueryUserProfileEndPoint.swift */; };
		83A64E552D3A8F7700E9F3BE /* GenerationRegistrationInfoPdfEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = 83A64E542D3A8F7700E9F3BE /* GenerationRegistrationInfoPdfEndPoint.swift */; };
		83D977B02CEF32BE00A6BC4D /* UserNotificationEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = 83D977AF2CEF32BE00A6BC4D /* UserNotificationEndPoint.swift */; };
		83D977B32CEF39B900A6BC4D /* UserNotificationReadEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = 83D977B22CEF39B900A6BC4D /* UserNotificationReadEndPoint.swift */; };
		83D977B52CF1C9C900A6BC4D /* MarketWatchListEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = 83D977B42CF1C9C900A6BC4D /* MarketWatchListEndPoint.swift */; };
		83D977CA2CF64D5700A6BC4D /* UserStatementGenerateEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = 83D977C92CF64D5700A6BC4D /* UserStatementGenerateEndPoint.swift */; };
		83D977CC2CF75B3700A6BC4D /* FileDownloadEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = 83D977CB2CF75B3700A6BC4D /* FileDownloadEndPoint.swift */; };
		83D977D52CF8C75E00A6BC4D /* OrderHistoryListEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = 83D977D42CF8C75E00A6BC4D /* OrderHistoryListEndPoint.swift */; };
		83D977D72CF8C79900A6BC4D /* OrderHistoryDetailEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = 83D977D62CF8C79900A6BC4D /* OrderHistoryDetailEndPoint.swift */; };
		83D977D92CF8C7BD00A6BC4D /* TransactionDetailEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = 83D977D82CF8C7BD00A6BC4D /* TransactionDetailEndPoint.swift */; };
		83D977DB2CF8C7FB00A6BC4D /* TransactionHistoryListEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = 83D977DA2CF8C7FB00A6BC4D /* TransactionHistoryListEndPoint.swift */; };
		83DA72C22D102C6B00CA4BD7 /* HistoricalGainLostEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = 83DA72C12D102C6B00CA4BD7 /* HistoricalGainLostEndPoint.swift */; };
		83DA72C42D10798700CA4BD7 /* UserLogoutEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = 83DA72C32D10798700CA4BD7 /* UserLogoutEndPoint.swift */; };
		83DA72C92D1345AE00CA4BD7 /* GetUserInfoEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = 83DA72C82D1345AE00CA4BD7 /* GetUserInfoEndPoint.swift */; };
		83DA72CB2D13467D00CA4BD7 /* UpdateUserInfoEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = 83DA72CA2D13467D00CA4BD7 /* UpdateUserInfoEndPoint.swift */; };
		83DA72CD2D13475000CA4BD7 /* CheckUserInfoEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = 83DA72CC2D13475000CA4BD7 /* CheckUserInfoEndPoint.swift */; };
		83F6C6472D0875BA001158C0 /* DeleteNotificationEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = 83F6C6462D0875BA001158C0 /* DeleteNotificationEndPoint.swift */; };
		83F6C6492D087676001158C0 /* SaveDeviceEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = 83F6C6482D087676001158C0 /* SaveDeviceEndPoint.swift */; };
		83FE18C82D6C2E780022819E /* CommonConfigEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = 83FE18C72D6C2E780022819E /* CommonConfigEndPoint.swift */; };
		DF0AD8492BD769570098FDAF /* WSSubscriber.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD7EF2BD769570098FDAF /* WSSubscriber.swift */; };
		DF0AD84A2BD769570098FDAF /* WSModels.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD7F02BD769570098FDAF /* WSModels.swift */; };
		DF0AD84B2BD769570098FDAF /* WebSocketController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD7F12BD769570098FDAF /* WebSocketController.swift */; };
		DF0AD84C2BD769570098FDAF /* MarketUpdateFavoriteEndpoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD7F32BD769570098FDAF /* MarketUpdateFavoriteEndpoint.swift */; };
		DF0AD84D2BD769570098FDAF /* MarketListDetailEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD7F42BD769570098FDAF /* MarketListDetailEndPoint.swift */; };
		DF0AD84E2BD769570098FDAF /* MarketGetFavoriteEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD7F52BD769570098FDAF /* MarketGetFavoriteEndPoint.swift */; };
		DF0AD84F2BD769570098FDAF /* MarketChartCandlesEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD7F62BD769570098FDAF /* MarketChartCandlesEndPoint.swift */; };
		DF0AD8502BD769570098FDAF /* MarketProfileFundamentalsEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD7F72BD769570098FDAF /* MarketProfileFundamentalsEndPoint.swift */; };
		DF0AD8512BD769570098FDAF /* MarketSimpleChartEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD7F82BD769570098FDAF /* MarketSimpleChartEndPoint.swift */; };
		DF0AD8522BD769570098FDAF /* MarketOrderBookEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD7F92BD769570098FDAF /* MarketOrderBookEndPoint.swift */; };
		DF0AD8532BD769570098FDAF /* MarketIntradayChartEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD7FA2BD769570098FDAF /* MarketIntradayChartEndPoint.swift */; };
		DF0AD8542BD769570098FDAF /* MarketQuoteActivityEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD7FB2BD769570098FDAF /* MarketQuoteActivityEndPoint.swift */; };
		DF0AD8552BD769570098FDAF /* MarketAdvanceSearchEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD7FC2BD769570098FDAF /* MarketAdvanceSearchEndPoint.swift */; };
		DF0AD8562BD769570098FDAF /* MarketInstrumentTagListEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD7FD2BD769570098FDAF /* MarketInstrumentTagListEndPoint.swift */; };
		DF0AD8572BD769570098FDAF /* RecentlyViewedSaveEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD7FE2BD769570098FDAF /* RecentlyViewedSaveEndPoint.swift */; };
		DF0AD8582BD769570098FDAF /* MarketInstrumentBaseEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD7FF2BD769570098FDAF /* MarketInstrumentBaseEndPoint.swift */; };
		DF0AD8592BD769570098FDAF /* MarketInstrumentSearchEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8002BD769570098FDAF /* MarketInstrumentSearchEndPoint.swift */; };
		DF0AD85A2BD769570098FDAF /* MarketListEndpoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8012BD769570098FDAF /* MarketListEndpoint.swift */; };
		DF0AD85B2BD769570098FDAF /* RecentlyViewedListEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8022BD769570098FDAF /* RecentlyViewedListEndPoint.swift */; };
		DF0AD85C2BD769570098FDAF /* MarketLastMatchesEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8032BD769570098FDAF /* MarketLastMatchesEndPoint.swift */; };
		DF0AD85D2BD769570098FDAF /* MarketProfileOverviewEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8042BD769570098FDAF /* MarketProfileOverviewEndPoint.swift */; };
		DF0AD85E2BD769570098FDAF /* MarketProfileFinancialsEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8052BD769570098FDAF /* MarketProfileFinancialsEndPoint.swift */; };
		DF0AD85F2BD769570098FDAF /* ModifyOrderThaiEquityEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8072BD769570098FDAF /* ModifyOrderThaiEquityEndPoint.swift */; };
		DF0AD8602BD769570098FDAF /* QuickOrderSaveEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8082BD769570098FDAF /* QuickOrderSaveEndPoint.swift */; };
		DF0AD8612BD769570098FDAF /* SaveOrderSettingEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8092BD769570098FDAF /* SaveOrderSettingEndPoint.swift */; };
		DF0AD8622BD769570098FDAF /* ModifyOrderGlobalEquityEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD80A2BD769570098FDAF /* ModifyOrderGlobalEquityEndPoint.swift */; };
		DF0AD8632BD769570098FDAF /* ListAutoOrderEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD80C2BD769570098FDAF /* ListAutoOrderEndPoint.swift */; };
		DF0AD8642BD769570098FDAF /* CreateAutoOrderEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD80D2BD769570098FDAF /* CreateAutoOrderEndPoint.swift */; };
		DF0AD8652BD769570098FDAF /* ModifyAutoOrderEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD80E2BD769570098FDAF /* ModifyAutoOrderEndPoint.swift */; };
		DF0AD8662BD769570098FDAF /* DeleteAutoOrderEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD80F2BD769570098FDAF /* DeleteAutoOrderEndPoint.swift */; };
		DF0AD8672BD769570098FDAF /* CreateUpdateAutoOrderBase.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8102BD769570098FDAF /* CreateUpdateAutoOrderBase.swift */; };
		DF0AD8682BD769570098FDAF /* PlaceOrderThaiEquityEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8112BD769570098FDAF /* PlaceOrderThaiEquityEndPoint.swift */; };
		DF0AD8692BD769570098FDAF /* CancelOrderThaiEquityEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8122BD769570098FDAF /* CancelOrderThaiEquityEndPoint.swift */; };
		DF0AD86A2BD769570098FDAF /* OfflineOrderListEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8132BD769570098FDAF /* OfflineOrderListEndPoint.swift */; };
		DF0AD86B2BD769570098FDAF /* QuickOrderDeleteEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8142BD769570098FDAF /* QuickOrderDeleteEndPoint.swift */; };
		DF0AD86C2BD769570098FDAF /* CancelOfflineOrderEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8152BD769570098FDAF /* CancelOfflineOrderEndPoint.swift */; };
		DF0AD86D2BD769570098FDAF /* CancelOrderGlobalEquityEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8162BD769570098FDAF /* CancelOrderGlobalEquityEndPoint.swift */; };
		DF0AD86E2BD769570098FDAF /* QuickOrderSettingsEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8172BD769570098FDAF /* QuickOrderSettingsEndPoint.swift */; };
		DF0AD86F2BD769570098FDAF /* PlaceOrderGlobalEquityEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8182BD769570098FDAF /* PlaceOrderGlobalEquityEndPoint.swift */; };
		DF0AD8702BD769570098FDAF /* ModifyOfflineOrderEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8192BD769570098FDAF /* ModifyOfflineOrderEndPoint.swift */; };
		DF0AD8712BD769570098FDAF /* CreateOfflineOrderEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD81A2BD769570098FDAF /* CreateOfflineOrderEndPoint.swift */; };
		DF0AD8722BD769570098FDAF /* OrderSettingQueryEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD81B2BD769570098FDAF /* OrderSettingQueryEndPoint.swift */; };
		DF0AD8732BD769570098FDAF /* GetMarketNewsEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD81D2BD769570098FDAF /* GetMarketNewsEndPoint.swift */; };
		DF0AD8742BD769570098FDAF /* AddressInfoEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD81F2BD769570098FDAF /* AddressInfoEndPoint.swift */; };
		DF0AD8752BD769570098FDAF /* FAQListEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8202BD769570098FDAF /* FAQListEndPoint.swift */; };
		DF0AD8762BD769570098FDAF /* SuitTestQuestionEndpoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8222BD769570098FDAF /* SuitTestQuestionEndpoint.swift */; };
		DF0AD8772BD769570098FDAF /* BanksNDIDEndpoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8232BD769570098FDAF /* BanksNDIDEndpoint.swift */; };
		DF0AD8782BD769570098FDAF /* TradeAccountTypesEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8242BD769570098FDAF /* TradeAccountTypesEndPoint.swift */; };
		DF0AD8792BD769570098FDAF /* CustomerFileEndpoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8252BD769570098FDAF /* CustomerFileEndpoint.swift */; };
		DF0AD87A2BD769570098FDAF /* BanksATSEndpoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8262BD769570098FDAF /* BanksATSEndpoint.swift */; };
		DF0AD87B2BD769570098FDAF /* TradingAccountOpeningEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8272BD769570098FDAF /* TradingAccountOpeningEndPoint.swift */; };
		DF0AD87C2BD769570098FDAF /* SuitTestAnswerEndpoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8282BD769570098FDAF /* SuitTestAnswerEndpoint.swift */; };
		DF0AD87D2BD769570098FDAF /* DepositEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD82A2BD769570098FDAF /* DepositEndPoint.swift */; };
		DF0AD87E2BD769570098FDAF /* WalletPositionDetailEndpoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD82B2BD769570098FDAF /* WalletPositionDetailEndpoint.swift */; };
		DF0AD87F2BD769570098FDAF /* WalletBalanceHistoryEndpoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD82C2BD769570098FDAF /* WalletBalanceHistoryEndpoint.swift */; };
		DF0AD8802BD769570098FDAF /* CashWithdrawEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD82D2BD769570098FDAF /* CashWithdrawEndPoint.swift */; };
		DF0AD8812BD769570098FDAF /* WalletSummaryEndpoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD82E2BD769570098FDAF /* WalletSummaryEndpoint.swift */; };
		DF0AD8832BD769570098FDAF /* MessageDeleteEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8312BD769570098FDAF /* MessageDeleteEndPoint.swift */; };
		DF0AD8842BD769570098FDAF /* MessageEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8322BD769570098FDAF /* MessageEndPoint.swift */; };
		DF0AD8852BD769570098FDAF /* MessageEditEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8332BD769570098FDAF /* MessageEditEndPoint.swift */; };
		DF0AD8882BD769570098FDAF /* EditCustomerProfileEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8372BD769570098FDAF /* EditCustomerProfileEndPoint.swift */; };
		DF0AD88A2BD769570098FDAF /* VerifyDuplicationEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8392BD769570098FDAF /* VerifyDuplicationEndPoint.swift */; };
		DF0AD88B2BD769570098FDAF /* UserInfoEndpoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD83A2BD769570098FDAF /* UserInfoEndpoint.swift */; };
		DF0AD88C2BD769570098FDAF /* BaseResponse.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD83C2BD769570098FDAF /* BaseResponse.swift */; };
		DF0AD88D2BD769570098FDAF /* URLEnvironment.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD83D2BD769570098FDAF /* URLEnvironment.swift */; };
		DF0AD88E2BD769570098FDAF /* Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD83E2BD769570098FDAF /* Extensions.swift */; };
		DF0AD88F2BD769570098FDAF /* DownloadService.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD83F2BD769570098FDAF /* DownloadService.swift */; };
		DF0AD8902BD769570098FDAF /* APILayer.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8402BD769570098FDAF /* APILayer.swift */; };
		DF0AD8912BD769570098FDAF /* MyOrderListEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8422BD769570098FDAF /* MyOrderListEndPoint.swift */; };
		DF0AD8922BD769570098FDAF /* TransactionListEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8432BD769570098FDAF /* TransactionListEndPoint.swift */; };
		DF0AD8932BD769570098FDAF /* MonthlyReportGenerationEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8452BD769570098FDAF /* MonthlyReportGenerationEndPoint.swift */; };
		DF0AD8942BD769570098FDAF /* ChangePasswordEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8472BD769570098FDAF /* ChangePasswordEndPoint.swift */; };
		DF0AD8952BD769570098FDAF /* PasswordVerificationEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AD8482BD769570098FDAF /* PasswordVerificationEndPoint.swift */; };
		DF0AE02E2BD76C770098FDAF /* SharedData.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = DF0AE02D2BD76C770098FDAF /* SharedData.framework */; };
		DF0AE0322BD76C820098FDAF /* Storage.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = DF0AE0312BD76C820098FDAF /* Storage.framework */; };
		DF22EEFF2C2BDF5A00279645 /* UserFileUploadEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF22EEFE2C2BDF5A00279645 /* UserFileUploadEndPoint.swift */; };
		DF22EF012C2BE08100279645 /* UserDocumentUpdateEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF22EF002C2BE08100279645 /* UserDocumentUpdateEndPoint.swift */; };
		DF273CF52BD76797003F9464 /* APILayer.h in Headers */ = {isa = PBXBuildFile; fileRef = DF273CF42BD76797003F9464 /* APILayer.h */; settings = {ATTRIBUTES = (Public, ); }; };
		DF2B67E42C5B604E00A5DE18 /* BenchmarkListEndpoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF2B67E32C5B604E00A5DE18 /* BenchmarkListEndpoint.swift */; };
		DF2B67E62C5B653A00A5DE18 /* BenchmarkDataEndpoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF2B67E52C5B653A00A5DE18 /* BenchmarkDataEndpoint.swift */; };
		DF4CDC5D2C451C730095BFC7 /* WalletStatisticEndpoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF4CDC5C2C451C730095BFC7 /* WalletStatisticEndpoint.swift */; };
		DF70B63C2C2D144D00FDDA40 /* UserLivenessEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF70B63B2C2D144D00FDDA40 /* UserLivenessEndPoint.swift */; };
		DF70B63E2C2D1A4700FDDA40 /* OTPRequestEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF70B63D2C2D1A4700FDDA40 /* OTPRequestEndPoint.swift */; };
		DF70B6402C2D38D900FDDA40 /* OCRRequestEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF70B63F2C2D38D900FDDA40 /* OCRRequestEndPoint.swift */; };
		DF70B6422C2D4EE500FDDA40 /* UserIdentityEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = ************************ /* UserIdentityEndPoint.swift */; };
		DF742C582C32F239004C1EE9 /* UserTwoFAEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF742C572C32F239004C1EE9 /* UserTwoFAEndPoint.swift */; };
		DF742C5A2C32FCDD004C1EE9 /* UserAuthEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF742C592C32FCDD004C1EE9 /* UserAuthEndPoint.swift */; };
		DF742C5C2C33CE35004C1EE9 /* UserAuthResetVerificationEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF742C5B2C33CE35004C1EE9 /* UserAuthResetVerificationEndPoint.swift */; };
		************************ /* UserAuthResetEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF742C5D2C33D05B004C1EE9 /* UserAuthResetEndPoint.swift */; };
		************************ /* UserRegisterEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = ************************ /* UserRegisterEndPoint.swift */; };
		DF75AD652C2E6E67006B86B1 /* UserLoginEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF75AD642C2E6E67006B86B1 /* UserLoginEndPoint.swift */; };
		DF7B9DC52C36AC5100600024 /* UserLivenessFetchEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF7B9DC42C36AC5100600024 /* UserLivenessFetchEndPoint.swift */; };
		DFA0657E2C3CE484002D768C /* DeclarationCRSFetchEndpoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = DFA0657D2C3CE484002D768C /* DeclarationCRSFetchEndpoint.swift */; };
		DFA065802C3CE85C002D768C /* DeclarationCRSUpdateEndpoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = DFA0657F2C3CE85C002D768C /* DeclarationCRSUpdateEndpoint.swift */; };
		DFB136B32C328E9D00A251B2 /* UserRegisterStepsEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = DFB136B22C328E9D00A251B2 /* UserRegisterStepsEndPoint.swift */; };
		DFB136B92C329E4900A251B2 /* UserInformationEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = DFB136B82C329E4900A251B2 /* UserInformationEndPoint.swift */; };
		DFC0B5452C23DDC600C739A8 /* UserAgreementFetchEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = DFC0B5442C23DDC600C739A8 /* UserAgreementFetchEndPoint.swift */; };
		DFC0B5492C23FF1200C739A8 /* UserAgreementUpdateEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = DFC0B5482C23FF1200C739A8 /* UserAgreementUpdateEndPoint.swift */; };
		DFC0B54B2C240D0F00C739A8 /* QuestionarieUpdateEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = DFC0B54A2C240D0F00C739A8 /* QuestionarieUpdateEndPoint.swift */; };
		DFE669F12C351D28000BB0FE /* UserFetchDocumentFetchEndPoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = DFE669F02C351D28000BB0FE /* UserFetchDocumentFetchEndPoint.swift */; };
		DFEF861D2C3F9291009E2C19 /* CurrencyEndpoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = DFEF861C2C3F9291009E2C19 /* CurrencyEndpoint.swift */; };
		DFF847912C216F5800A639E6 /* QuestionarieFetchEndpoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = DFF847902C216F5800A639E6 /* QuestionarieFetchEndpoint.swift */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		0C8F52A22E5C2102003DAAE0 /* MarketProductCategoryListEndPoint.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MarketProductCategoryListEndPoint.swift; sourceTree = "<group>"; };
		66A4B1A513C47457A139C6BC /* Pods-APILayer.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-APILayer.release.xcconfig"; path = "Target Support Files/Pods-APILayer/Pods-APILayer.release.xcconfig"; sourceTree = "<group>"; };
		6F95A0885E483161EB303ACF /* Pods_APILayer.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_APILayer.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		830D0F352D54ADBC00B5A130 /* ContentBannerListEndPoint.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ContentBannerListEndPoint.swift; sourceTree = "<group>"; };
		831943CC2CCCD88F00A46862 /* MarketInstrumentDetailEndPoint.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MarketInstrumentDetailEndPoint.swift; sourceTree = "<group>"; };
		831C07DF2C2A7D9700A1BB94 /* EnumerationEndPoint.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = EnumerationEndPoint.swift; sourceTree = "<group>"; };
		832D80022C35A25C00C37931 /* UserProfileEditEndPoint.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserProfileEditEndPoint.swift; sourceTree = "<group>"; };
		832D80042C35BD7A00C37931 /* UserEmploymentEditEndPoint.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserEmploymentEditEndPoint.swift; sourceTree = "<group>"; };
		832D80062C35C36300C37931 /* UserBankAccountEditEndPoint.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserBankAccountEditEndPoint.swift; sourceTree = "<group>"; };
		832D800E2C3658EE00C37931 /* QueryUserBankListEndPoint.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = QueryUserBankListEndPoint.swift; sourceTree = "<group>"; };
		83319F342CE342A000F78916 /* WealthPlanListEndPoint.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WealthPlanListEndPoint.swift; sourceTree = "<group>"; };
		83319F372CE342DE00F78916 /* WealthPlanDetailEndPoint.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WealthPlanDetailEndPoint.swift; sourceTree = "<group>"; };
		83319F392CE342F500F78916 /* UpdateWealthPlanEndPoint.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UpdateWealthPlanEndPoint.swift; sourceTree = "<group>"; };
		83319F3B2CE3430D00F78916 /* WealthPlanPerformanceEndPoint.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WealthPlanPerformanceEndPoint.swift; sourceTree = "<group>"; };
		833B79112C3FAE4600EAEE0D /* UserDocuSignEndPoint.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserDocuSignEndPoint.swift; sourceTree = "<group>"; };
		833DB8ED2D410025002FCCC7 /* OnboardingCheckEndPoint.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = OnboardingCheckEndPoint.swift; sourceTree = "<group>"; };
		83443CA42D03330C00BDC625 /* UpdateCustomerStatusEndPoint.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UpdateCustomerStatusEndPoint.swift; sourceTree = "<group>"; };
		8379B3FC2D15E17D004D3006 /* QueryUserProfileEndPoint.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = QueryUserProfileEndPoint.swift; sourceTree = "<group>"; };
		83A64E542D3A8F7700E9F3BE /* GenerationRegistrationInfoPdfEndPoint.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GenerationRegistrationInfoPdfEndPoint.swift; sourceTree = "<group>"; };
		83D977AF2CEF32BE00A6BC4D /* UserNotificationEndPoint.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserNotificationEndPoint.swift; sourceTree = "<group>"; };
		83D977B22CEF39B900A6BC4D /* UserNotificationReadEndPoint.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserNotificationReadEndPoint.swift; sourceTree = "<group>"; };
		83D977B42CF1C9C900A6BC4D /* MarketWatchListEndPoint.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MarketWatchListEndPoint.swift; sourceTree = "<group>"; };
		83D977C92CF64D5700A6BC4D /* UserStatementGenerateEndPoint.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserStatementGenerateEndPoint.swift; sourceTree = "<group>"; };
		83D977CB2CF75B3700A6BC4D /* FileDownloadEndPoint.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FileDownloadEndPoint.swift; sourceTree = "<group>"; };
		83D977D42CF8C75E00A6BC4D /* OrderHistoryListEndPoint.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = OrderHistoryListEndPoint.swift; sourceTree = "<group>"; };
		83D977D62CF8C79900A6BC4D /* OrderHistoryDetailEndPoint.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = OrderHistoryDetailEndPoint.swift; sourceTree = "<group>"; };
		83D977D82CF8C7BD00A6BC4D /* TransactionDetailEndPoint.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TransactionDetailEndPoint.swift; sourceTree = "<group>"; };
		83D977DA2CF8C7FB00A6BC4D /* TransactionHistoryListEndPoint.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TransactionHistoryListEndPoint.swift; sourceTree = "<group>"; };
		83DA72C12D102C6B00CA4BD7 /* HistoricalGainLostEndPoint.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HistoricalGainLostEndPoint.swift; sourceTree = "<group>"; };
		83DA72C32D10798700CA4BD7 /* UserLogoutEndPoint.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserLogoutEndPoint.swift; sourceTree = "<group>"; };
		83DA72C82D1345AE00CA4BD7 /* GetUserInfoEndPoint.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GetUserInfoEndPoint.swift; sourceTree = "<group>"; };
		83DA72CA2D13467D00CA4BD7 /* UpdateUserInfoEndPoint.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UpdateUserInfoEndPoint.swift; sourceTree = "<group>"; };
		83DA72CC2D13475000CA4BD7 /* CheckUserInfoEndPoint.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CheckUserInfoEndPoint.swift; sourceTree = "<group>"; };
		83F6C6462D0875BA001158C0 /* DeleteNotificationEndPoint.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DeleteNotificationEndPoint.swift; sourceTree = "<group>"; };
		83F6C6482D087676001158C0 /* SaveDeviceEndPoint.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SaveDeviceEndPoint.swift; sourceTree = "<group>"; };
		83FE18C72D6C2E780022819E /* CommonConfigEndPoint.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CommonConfigEndPoint.swift; sourceTree = "<group>"; };
		A8AB14A50903C1729F87C2EA /* Pods-APILayer.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-APILayer.debug.xcconfig"; path = "Target Support Files/Pods-APILayer/Pods-APILayer.debug.xcconfig"; sourceTree = "<group>"; };
		DF0AD7EF2BD769570098FDAF /* WSSubscriber.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = WSSubscriber.swift; sourceTree = "<group>"; };
		DF0AD7F02BD769570098FDAF /* WSModels.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = WSModels.swift; sourceTree = "<group>"; };
		DF0AD7F12BD769570098FDAF /* WebSocketController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = WebSocketController.swift; sourceTree = "<group>"; };
		DF0AD7F32BD769570098FDAF /* MarketUpdateFavoriteEndpoint.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MarketUpdateFavoriteEndpoint.swift; sourceTree = "<group>"; };
		DF0AD7F42BD769570098FDAF /* MarketListDetailEndPoint.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MarketListDetailEndPoint.swift; sourceTree = "<group>"; };
		DF0AD7F52BD769570098FDAF /* MarketGetFavoriteEndPoint.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MarketGetFavoriteEndPoint.swift; sourceTree = "<group>"; };
		DF0AD7F62BD769570098FDAF /* MarketChartCandlesEndPoint.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MarketChartCandlesEndPoint.swift; sourceTree = "<group>"; };
		DF0AD7F72BD769570098FDAF /* MarketProfileFundamentalsEndPoint.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MarketProfileFundamentalsEndPoint.swift; sourceTree = "<group>"; };
		DF0AD7F82BD769570098FDAF /* MarketSimpleChartEndPoint.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MarketSimpleChartEndPoint.swift; sourceTree = "<group>"; };
		DF0AD7F92BD769570098FDAF /* MarketOrderBookEndPoint.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MarketOrderBookEndPoint.swift; sourceTree = "<group>"; };
		DF0AD7FA2BD769570098FDAF /* MarketIntradayChartEndPoint.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MarketIntradayChartEndPoint.swift; sourceTree = "<group>"; };
		DF0AD7FB2BD769570098FDAF /* MarketQuoteActivityEndPoint.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MarketQuoteActivityEndPoint.swift; sourceTree = "<group>"; };
		DF0AD7FC2BD769570098FDAF /* MarketAdvanceSearchEndPoint.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MarketAdvanceSearchEndPoint.swift; sourceTree = "<group>"; };
		DF0AD7FD2BD769570098FDAF /* MarketInstrumentTagListEndPoint.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MarketInstrumentTagListEndPoint.swift; sourceTree = "<group>"; };
		DF0AD7FE2BD769570098FDAF /* RecentlyViewedSaveEndPoint.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = RecentlyViewedSaveEndPoint.swift; sourceTree = "<group>"; };
		DF0AD7FF2BD769570098FDAF /* MarketInstrumentBaseEndPoint.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MarketInstrumentBaseEndPoint.swift; sourceTree = "<group>"; };
		DF0AD8002BD769570098FDAF /* MarketInstrumentSearchEndPoint.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MarketInstrumentSearchEndPoint.swift; sourceTree = "<group>"; };
		DF0AD8012BD769570098FDAF /* MarketListEndpoint.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MarketListEndpoint.swift; sourceTree = "<group>"; };
		DF0AD8022BD769570098FDAF /* RecentlyViewedListEndPoint.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = RecentlyViewedListEndPoint.swift; sourceTree = "<group>"; };
		DF0AD8032BD769570098FDAF /* MarketLastMatchesEndPoint.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MarketLastMatchesEndPoint.swift; sourceTree = "<group>"; };
		DF0AD8042BD769570098FDAF /* MarketProfileOverviewEndPoint.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MarketProfileOverviewEndPoint.swift; sourceTree = "<group>"; };
		DF0AD8052BD769570098FDAF /* MarketProfileFinancialsEndPoint.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MarketProfileFinancialsEndPoint.swift; sourceTree = "<group>"; };
		DF0AD8072BD769570098FDAF /* ModifyOrderThaiEquityEndPoint.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ModifyOrderThaiEquityEndPoint.swift; sourceTree = "<group>"; };
		DF0AD8082BD769570098FDAF /* QuickOrderSaveEndPoint.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = QuickOrderSaveEndPoint.swift; sourceTree = "<group>"; };
		DF0AD8092BD769570098FDAF /* SaveOrderSettingEndPoint.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SaveOrderSettingEndPoint.swift; sourceTree = "<group>"; };
		DF0AD80A2BD769570098FDAF /* ModifyOrderGlobalEquityEndPoint.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ModifyOrderGlobalEquityEndPoint.swift; sourceTree = "<group>"; };
		DF0AD80C2BD769570098FDAF /* ListAutoOrderEndPoint.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ListAutoOrderEndPoint.swift; sourceTree = "<group>"; };
		DF0AD80D2BD769570098FDAF /* CreateAutoOrderEndPoint.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CreateAutoOrderEndPoint.swift; sourceTree = "<group>"; };
		DF0AD80E2BD769570098FDAF /* ModifyAutoOrderEndPoint.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ModifyAutoOrderEndPoint.swift; sourceTree = "<group>"; };
		DF0AD80F2BD769570098FDAF /* DeleteAutoOrderEndPoint.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = DeleteAutoOrderEndPoint.swift; sourceTree = "<group>"; };
		DF0AD8102BD769570098FDAF /* CreateUpdateAutoOrderBase.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CreateUpdateAutoOrderBase.swift; sourceTree = "<group>"; };
		DF0AD8112BD769570098FDAF /* PlaceOrderThaiEquityEndPoint.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = PlaceOrderThaiEquityEndPoint.swift; sourceTree = "<group>"; };
		DF0AD8122BD769570098FDAF /* CancelOrderThaiEquityEndPoint.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CancelOrderThaiEquityEndPoint.swift; sourceTree = "<group>"; };
		DF0AD8132BD769570098FDAF /* OfflineOrderListEndPoint.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = OfflineOrderListEndPoint.swift; sourceTree = "<group>"; };
		DF0AD8142BD769570098FDAF /* QuickOrderDeleteEndPoint.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = QuickOrderDeleteEndPoint.swift; sourceTree = "<group>"; };
		DF0AD8152BD769570098FDAF /* CancelOfflineOrderEndPoint.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CancelOfflineOrderEndPoint.swift; sourceTree = "<group>"; };
		DF0AD8162BD769570098FDAF /* CancelOrderGlobalEquityEndPoint.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CancelOrderGlobalEquityEndPoint.swift; sourceTree = "<group>"; };
		DF0AD8172BD769570098FDAF /* QuickOrderSettingsEndPoint.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = QuickOrderSettingsEndPoint.swift; sourceTree = "<group>"; };
		DF0AD8182BD769570098FDAF /* PlaceOrderGlobalEquityEndPoint.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = PlaceOrderGlobalEquityEndPoint.swift; sourceTree = "<group>"; };
		DF0AD8192BD769570098FDAF /* ModifyOfflineOrderEndPoint.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ModifyOfflineOrderEndPoint.swift; sourceTree = "<group>"; };
		DF0AD81A2BD769570098FDAF /* CreateOfflineOrderEndPoint.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CreateOfflineOrderEndPoint.swift; sourceTree = "<group>"; };
		DF0AD81B2BD769570098FDAF /* OrderSettingQueryEndPoint.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = OrderSettingQueryEndPoint.swift; sourceTree = "<group>"; };
		DF0AD81D2BD769570098FDAF /* GetMarketNewsEndPoint.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = GetMarketNewsEndPoint.swift; sourceTree = "<group>"; };
		DF0AD81F2BD769570098FDAF /* AddressInfoEndPoint.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AddressInfoEndPoint.swift; sourceTree = "<group>"; };
		DF0AD8202BD769570098FDAF /* FAQListEndPoint.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = FAQListEndPoint.swift; sourceTree = "<group>"; };
		DF0AD8222BD769570098FDAF /* SuitTestQuestionEndpoint.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SuitTestQuestionEndpoint.swift; sourceTree = "<group>"; };
		DF0AD8232BD769570098FDAF /* BanksNDIDEndpoint.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = BanksNDIDEndpoint.swift; sourceTree = "<group>"; };
		DF0AD8242BD769570098FDAF /* TradeAccountTypesEndPoint.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = TradeAccountTypesEndPoint.swift; sourceTree = "<group>"; };
		DF0AD8252BD769570098FDAF /* CustomerFileEndpoint.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CustomerFileEndpoint.swift; sourceTree = "<group>"; };
		DF0AD8262BD769570098FDAF /* BanksATSEndpoint.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = BanksATSEndpoint.swift; sourceTree = "<group>"; };
		DF0AD8272BD769570098FDAF /* TradingAccountOpeningEndPoint.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = TradingAccountOpeningEndPoint.swift; sourceTree = "<group>"; };
		DF0AD8282BD769570098FDAF /* SuitTestAnswerEndpoint.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SuitTestAnswerEndpoint.swift; sourceTree = "<group>"; };
		DF0AD82A2BD769570098FDAF /* DepositEndPoint.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = DepositEndPoint.swift; sourceTree = "<group>"; };
		DF0AD82B2BD769570098FDAF /* WalletPositionDetailEndpoint.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = WalletPositionDetailEndpoint.swift; sourceTree = "<group>"; };
		DF0AD82C2BD769570098FDAF /* WalletBalanceHistoryEndpoint.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = WalletBalanceHistoryEndpoint.swift; sourceTree = "<group>"; };
		DF0AD82D2BD769570098FDAF /* CashWithdrawEndPoint.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CashWithdrawEndPoint.swift; sourceTree = "<group>"; };
		DF0AD82E2BD769570098FDAF /* WalletSummaryEndpoint.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = WalletSummaryEndpoint.swift; sourceTree = "<group>"; };
		DF0AD8312BD769570098FDAF /* MessageDeleteEndPoint.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MessageDeleteEndPoint.swift; sourceTree = "<group>"; };
		DF0AD8322BD769570098FDAF /* MessageEndPoint.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MessageEndPoint.swift; sourceTree = "<group>"; };
		DF0AD8332BD769570098FDAF /* MessageEditEndPoint.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MessageEditEndPoint.swift; sourceTree = "<group>"; };
		DF0AD8372BD769570098FDAF /* EditCustomerProfileEndPoint.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = EditCustomerProfileEndPoint.swift; sourceTree = "<group>"; };
		DF0AD8392BD769570098FDAF /* VerifyDuplicationEndPoint.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = VerifyDuplicationEndPoint.swift; sourceTree = "<group>"; };
		DF0AD83A2BD769570098FDAF /* UserInfoEndpoint.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = UserInfoEndpoint.swift; sourceTree = "<group>"; };
		DF0AD83C2BD769570098FDAF /* BaseResponse.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = BaseResponse.swift; sourceTree = "<group>"; };
		DF0AD83D2BD769570098FDAF /* URLEnvironment.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = URLEnvironment.swift; sourceTree = "<group>"; };
		DF0AD83E2BD769570098FDAF /* Extensions.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Extensions.swift; sourceTree = "<group>"; };
		DF0AD83F2BD769570098FDAF /* DownloadService.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = DownloadService.swift; sourceTree = "<group>"; };
		DF0AD8402BD769570098FDAF /* APILayer.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = APILayer.swift; sourceTree = "<group>"; };
		DF0AD8422BD769570098FDAF /* MyOrderListEndPoint.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MyOrderListEndPoint.swift; sourceTree = "<group>"; };
		DF0AD8432BD769570098FDAF /* TransactionListEndPoint.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = TransactionListEndPoint.swift; sourceTree = "<group>"; };
		DF0AD8452BD769570098FDAF /* MonthlyReportGenerationEndPoint.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MonthlyReportGenerationEndPoint.swift; sourceTree = "<group>"; };
		DF0AD8472BD769570098FDAF /* ChangePasswordEndPoint.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ChangePasswordEndPoint.swift; sourceTree = "<group>"; };
		DF0AD8482BD769570098FDAF /* PasswordVerificationEndPoint.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = PasswordVerificationEndPoint.swift; sourceTree = "<group>"; };
		DF0AE02D2BD76C770098FDAF /* SharedData.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = SharedData.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		DF0AE0312BD76C820098FDAF /* Storage.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = Storage.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		DF22EEFE2C2BDF5A00279645 /* UserFileUploadEndPoint.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserFileUploadEndPoint.swift; sourceTree = "<group>"; };
		DF22EF002C2BE08100279645 /* UserDocumentUpdateEndPoint.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserDocumentUpdateEndPoint.swift; sourceTree = "<group>"; };
		DF273CF12BD76797003F9464 /* APILayer.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = APILayer.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		DF273CF42BD76797003F9464 /* APILayer.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = APILayer.h; sourceTree = "<group>"; };
		DF2B67E32C5B604E00A5DE18 /* BenchmarkListEndpoint.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BenchmarkListEndpoint.swift; sourceTree = "<group>"; };
		DF2B67E52C5B653A00A5DE18 /* BenchmarkDataEndpoint.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BenchmarkDataEndpoint.swift; sourceTree = "<group>"; };
		DF4CDC5C2C451C730095BFC7 /* WalletStatisticEndpoint.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WalletStatisticEndpoint.swift; sourceTree = "<group>"; };
		DF70B63B2C2D144D00FDDA40 /* UserLivenessEndPoint.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserLivenessEndPoint.swift; sourceTree = "<group>"; };
		DF70B63D2C2D1A4700FDDA40 /* OTPRequestEndPoint.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = OTPRequestEndPoint.swift; sourceTree = "<group>"; };
		DF70B63F2C2D38D900FDDA40 /* OCRRequestEndPoint.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = OCRRequestEndPoint.swift; sourceTree = "<group>"; };
		************************ /* UserIdentityEndPoint.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserIdentityEndPoint.swift; sourceTree = "<group>"; };
		DF742C572C32F239004C1EE9 /* UserTwoFAEndPoint.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserTwoFAEndPoint.swift; sourceTree = "<group>"; };
		DF742C592C32FCDD004C1EE9 /* UserAuthEndPoint.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserAuthEndPoint.swift; sourceTree = "<group>"; };
		DF742C5B2C33CE35004C1EE9 /* UserAuthResetVerificationEndPoint.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserAuthResetVerificationEndPoint.swift; sourceTree = "<group>"; };
		DF742C5D2C33D05B004C1EE9 /* UserAuthResetEndPoint.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserAuthResetEndPoint.swift; sourceTree = "<group>"; };
		************************ /* UserRegisterEndPoint.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserRegisterEndPoint.swift; sourceTree = "<group>"; };
		DF75AD642C2E6E67006B86B1 /* UserLoginEndPoint.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserLoginEndPoint.swift; sourceTree = "<group>"; };
		DF7B9DC42C36AC5100600024 /* UserLivenessFetchEndPoint.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserLivenessFetchEndPoint.swift; sourceTree = "<group>"; };
		DFA0657D2C3CE484002D768C /* DeclarationCRSFetchEndpoint.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DeclarationCRSFetchEndpoint.swift; sourceTree = "<group>"; };
		DFA0657F2C3CE85C002D768C /* DeclarationCRSUpdateEndpoint.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DeclarationCRSUpdateEndpoint.swift; sourceTree = "<group>"; };
		DFB136B22C328E9D00A251B2 /* UserRegisterStepsEndPoint.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserRegisterStepsEndPoint.swift; sourceTree = "<group>"; };
		DFB136B82C329E4900A251B2 /* UserInformationEndPoint.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserInformationEndPoint.swift; sourceTree = "<group>"; };
		DFC0B5442C23DDC600C739A8 /* UserAgreementFetchEndPoint.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserAgreementFetchEndPoint.swift; sourceTree = "<group>"; };
		DFC0B5482C23FF1200C739A8 /* UserAgreementUpdateEndPoint.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserAgreementUpdateEndPoint.swift; sourceTree = "<group>"; };
		DFC0B54A2C240D0F00C739A8 /* QuestionarieUpdateEndPoint.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = QuestionarieUpdateEndPoint.swift; sourceTree = "<group>"; };
		DFE669F02C351D28000BB0FE /* UserFetchDocumentFetchEndPoint.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserFetchDocumentFetchEndPoint.swift; sourceTree = "<group>"; };
		DFEF861C2C3F9291009E2C19 /* CurrencyEndpoint.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CurrencyEndpoint.swift; sourceTree = "<group>"; };
		DFF847902C216F5800A639E6 /* QuestionarieFetchEndpoint.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = QuestionarieFetchEndpoint.swift; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		DF273CEE2BD76797003F9464 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				1C60BDEBB9F9D8ED5DA46D25 /* Pods_APILayer.framework in Frameworks */,
				DF0AE02E2BD76C770098FDAF /* SharedData.framework in Frameworks */,
				DF0AE0322BD76C820098FDAF /* Storage.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		0A33421152BD4BDAA03D0A21 /* Pods */ = {
			isa = PBXGroup;
			children = (
				A8AB14A50903C1729F87C2EA /* Pods-APILayer.debug.xcconfig */,
				66A4B1A513C47457A139C6BC /* Pods-APILayer.release.xcconfig */,
			);
			name = Pods;
			path = ../Pods;
			sourceTree = "<group>";
		};
		83319F362CE342BF00F78916 /* WealthPlan */ = {
			isa = PBXGroup;
			children = (
				83319F342CE342A000F78916 /* WealthPlanListEndPoint.swift */,
				83319F372CE342DE00F78916 /* WealthPlanDetailEndPoint.swift */,
				83319F392CE342F500F78916 /* UpdateWealthPlanEndPoint.swift */,
				83319F3B2CE3430D00F78916 /* WealthPlanPerformanceEndPoint.swift */,
			);
			path = WealthPlan;
			sourceTree = "<group>";
		};
		DF0AD7EE2BD769570098FDAF /* WebSocket */ = {
			isa = PBXGroup;
			children = (
				DF0AD7EF2BD769570098FDAF /* WSSubscriber.swift */,
				DF0AD7F02BD769570098FDAF /* WSModels.swift */,
				DF0AD7F12BD769570098FDAF /* WebSocketController.swift */,
			);
			path = WebSocket;
			sourceTree = "<group>";
		};
		DF0AD7F22BD769570098FDAF /* Market */ = {
			isa = PBXGroup;
			children = (
				0C8F52A22E5C2102003DAAE0 /* MarketProductCategoryListEndPoint.swift */,
				DF0AD7F32BD769570098FDAF /* MarketUpdateFavoriteEndpoint.swift */,
				DF0AD7F42BD769570098FDAF /* MarketListDetailEndPoint.swift */,
				DF0AD7F52BD769570098FDAF /* MarketGetFavoriteEndPoint.swift */,
				DF0AD7F62BD769570098FDAF /* MarketChartCandlesEndPoint.swift */,
				DF0AD7F72BD769570098FDAF /* MarketProfileFundamentalsEndPoint.swift */,
				DF0AD7F82BD769570098FDAF /* MarketSimpleChartEndPoint.swift */,
				DF0AD7F92BD769570098FDAF /* MarketOrderBookEndPoint.swift */,
				DF0AD7FA2BD769570098FDAF /* MarketIntradayChartEndPoint.swift */,
				DF0AD7FB2BD769570098FDAF /* MarketQuoteActivityEndPoint.swift */,
				DF0AD7FC2BD769570098FDAF /* MarketAdvanceSearchEndPoint.swift */,
				DF0AD7FD2BD769570098FDAF /* MarketInstrumentTagListEndPoint.swift */,
				DF0AD7FE2BD769570098FDAF /* RecentlyViewedSaveEndPoint.swift */,
				DF0AD7FF2BD769570098FDAF /* MarketInstrumentBaseEndPoint.swift */,
				DF0AD8002BD769570098FDAF /* MarketInstrumentSearchEndPoint.swift */,
				831943CC2CCCD88F00A46862 /* MarketInstrumentDetailEndPoint.swift */,
				DF0AD8012BD769570098FDAF /* MarketListEndpoint.swift */,
				DF0AD8022BD769570098FDAF /* RecentlyViewedListEndPoint.swift */,
				DF0AD8032BD769570098FDAF /* MarketLastMatchesEndPoint.swift */,
				DF0AD8042BD769570098FDAF /* MarketProfileOverviewEndPoint.swift */,
				DF0AD8052BD769570098FDAF /* MarketProfileFinancialsEndPoint.swift */,
				83D977B42CF1C9C900A6BC4D /* MarketWatchListEndPoint.swift */,
			);
			path = Market;
			sourceTree = "<group>";
		};
		DF0AD8062BD769570098FDAF /* Order */ = {
			isa = PBXGroup;
			children = (
				DF0AD8072BD769570098FDAF /* ModifyOrderThaiEquityEndPoint.swift */,
				DF0AD8082BD769570098FDAF /* QuickOrderSaveEndPoint.swift */,
				DF0AD8092BD769570098FDAF /* SaveOrderSettingEndPoint.swift */,
				DF0AD80A2BD769570098FDAF /* ModifyOrderGlobalEquityEndPoint.swift */,
				DF0AD80B2BD769570098FDAF /* AutoOrder */,
				DF0AD8112BD769570098FDAF /* PlaceOrderThaiEquityEndPoint.swift */,
				DF0AD8122BD769570098FDAF /* CancelOrderThaiEquityEndPoint.swift */,
				DF0AD8132BD769570098FDAF /* OfflineOrderListEndPoint.swift */,
				DF0AD8142BD769570098FDAF /* QuickOrderDeleteEndPoint.swift */,
				DF0AD8152BD769570098FDAF /* CancelOfflineOrderEndPoint.swift */,
				DF0AD8162BD769570098FDAF /* CancelOrderGlobalEquityEndPoint.swift */,
				DF0AD8172BD769570098FDAF /* QuickOrderSettingsEndPoint.swift */,
				DF0AD8182BD769570098FDAF /* PlaceOrderGlobalEquityEndPoint.swift */,
				DF0AD8192BD769570098FDAF /* ModifyOfflineOrderEndPoint.swift */,
				DF0AD81A2BD769570098FDAF /* CreateOfflineOrderEndPoint.swift */,
				DF0AD81B2BD769570098FDAF /* OrderSettingQueryEndPoint.swift */,
			);
			path = Order;
			sourceTree = "<group>";
		};
		DF0AD80B2BD769570098FDAF /* AutoOrder */ = {
			isa = PBXGroup;
			children = (
				DF0AD80C2BD769570098FDAF /* ListAutoOrderEndPoint.swift */,
				DF0AD80D2BD769570098FDAF /* CreateAutoOrderEndPoint.swift */,
				DF0AD80E2BD769570098FDAF /* ModifyAutoOrderEndPoint.swift */,
				DF0AD80F2BD769570098FDAF /* DeleteAutoOrderEndPoint.swift */,
				DF0AD8102BD769570098FDAF /* CreateUpdateAutoOrderBase.swift */,
			);
			path = AutoOrder;
			sourceTree = "<group>";
		};
		DF0AD81C2BD769570098FDAF /* Home */ = {
			isa = PBXGroup;
			children = (
				DF0AD81D2BD769570098FDAF /* GetMarketNewsEndPoint.swift */,
				830D0F352D54ADBC00B5A130 /* ContentBannerListEndPoint.swift */,
			);
			path = Home;
			sourceTree = "<group>";
		};
		DF0AD81E2BD769570098FDAF /* Common */ = {
			isa = PBXGroup;
			children = (
				DF0AD81F2BD769570098FDAF /* AddressInfoEndPoint.swift */,
				DF0AD8202BD769570098FDAF /* FAQListEndPoint.swift */,
				831C07DF2C2A7D9700A1BB94 /* EnumerationEndPoint.swift */,
				83D977CB2CF75B3700A6BC4D /* FileDownloadEndPoint.swift */,
				83FE18C72D6C2E780022819E /* CommonConfigEndPoint.swift */,
			);
			path = Common;
			sourceTree = "<group>";
		};
		DF0AD8212BD769570098FDAF /* TradingRegister */ = {
			isa = PBXGroup;
			children = (
				DF0AD8222BD769570098FDAF /* SuitTestQuestionEndpoint.swift */,
				DF0AD8232BD769570098FDAF /* BanksNDIDEndpoint.swift */,
				DF0AD8242BD769570098FDAF /* TradeAccountTypesEndPoint.swift */,
				DF0AD8252BD769570098FDAF /* CustomerFileEndpoint.swift */,
				DF0AD8262BD769570098FDAF /* BanksATSEndpoint.swift */,
				DF0AD8272BD769570098FDAF /* TradingAccountOpeningEndPoint.swift */,
				DF0AD8282BD769570098FDAF /* SuitTestAnswerEndpoint.swift */,
			);
			path = TradingRegister;
			sourceTree = "<group>";
		};
		DF0AD8292BD769570098FDAF /* Wallet */ = {
			isa = PBXGroup;
			children = (
				DF0AD82A2BD769570098FDAF /* DepositEndPoint.swift */,
				DF0AD82B2BD769570098FDAF /* WalletPositionDetailEndpoint.swift */,
				DF0AD82C2BD769570098FDAF /* WalletBalanceHistoryEndpoint.swift */,
				DF0AD82D2BD769570098FDAF /* CashWithdrawEndPoint.swift */,
				DF0AD82E2BD769570098FDAF /* WalletSummaryEndpoint.swift */,
				DF4CDC5C2C451C730095BFC7 /* WalletStatisticEndpoint.swift */,
				DFEF861C2C3F9291009E2C19 /* CurrencyEndpoint.swift */,
				DF2B67E32C5B604E00A5DE18 /* BenchmarkListEndpoint.swift */,
				DF2B67E52C5B653A00A5DE18 /* BenchmarkDataEndpoint.swift */,
				83DA72C12D102C6B00CA4BD7 /* HistoricalGainLostEndPoint.swift */,
			);
			path = Wallet;
			sourceTree = "<group>";
		};
		DF0AD8302BD769570098FDAF /* Notification */ = {
			isa = PBXGroup;
			children = (
				83D977AF2CEF32BE00A6BC4D /* UserNotificationEndPoint.swift */,
				83D977B22CEF39B900A6BC4D /* UserNotificationReadEndPoint.swift */,
				DF0AD8312BD769570098FDAF /* MessageDeleteEndPoint.swift */,
				DF0AD8322BD769570098FDAF /* MessageEndPoint.swift */,
				DF0AD8332BD769570098FDAF /* MessageEditEndPoint.swift */,
				83F6C6462D0875BA001158C0 /* DeleteNotificationEndPoint.swift */,
				83F6C6482D087676001158C0 /* SaveDeviceEndPoint.swift */,
			);
			path = Notification;
			sourceTree = "<group>";
		};
		DF0AD8352BD769570098FDAF /* Login */ = {
			isa = PBXGroup;
			children = (
				DF0AD8372BD769570098FDAF /* EditCustomerProfileEndPoint.swift */,
				DF0AD8392BD769570098FDAF /* VerifyDuplicationEndPoint.swift */,
				DF0AD83A2BD769570098FDAF /* UserInfoEndpoint.swift */,
				DF75AD642C2E6E67006B86B1 /* UserLoginEndPoint.swift */,
				83DA72C32D10798700CA4BD7 /* UserLogoutEndPoint.swift */,
				83DA72C82D1345AE00CA4BD7 /* GetUserInfoEndPoint.swift */,
				83DA72CA2D13467D00CA4BD7 /* UpdateUserInfoEndPoint.swift */,
				83DA72CC2D13475000CA4BD7 /* CheckUserInfoEndPoint.swift */,
				8379B3FC2D15E17D004D3006 /* QueryUserProfileEndPoint.swift */,
			);
			path = Login;
			sourceTree = "<group>";
		};
		DF0AD83B2BD769570098FDAF /* Bases */ = {
			isa = PBXGroup;
			children = (
				DF0AD83C2BD769570098FDAF /* BaseResponse.swift */,
				DF0AD83D2BD769570098FDAF /* URLEnvironment.swift */,
				DF0AD83E2BD769570098FDAF /* Extensions.swift */,
				DF0AD83F2BD769570098FDAF /* DownloadService.swift */,
				DF0AD8402BD769570098FDAF /* APILayer.swift */,
			);
			path = Bases;
			sourceTree = "<group>";
		};
		DF0AD8412BD769570098FDAF /* History */ = {
			isa = PBXGroup;
			children = (
				DF0AD8422BD769570098FDAF /* MyOrderListEndPoint.swift */,
				DF0AD8432BD769570098FDAF /* TransactionListEndPoint.swift */,
				83D977D42CF8C75E00A6BC4D /* OrderHistoryListEndPoint.swift */,
				83D977D62CF8C79900A6BC4D /* OrderHistoryDetailEndPoint.swift */,
				83D977DA2CF8C7FB00A6BC4D /* TransactionHistoryListEndPoint.swift */,
				83D977D82CF8C7BD00A6BC4D /* TransactionDetailEndPoint.swift */,
			);
			path = History;
			sourceTree = "<group>";
		};
		DF0AD8442BD769570098FDAF /* Report */ = {
			isa = PBXGroup;
			children = (
				DF0AD8452BD769570098FDAF /* MonthlyReportGenerationEndPoint.swift */,
				83D977C92CF64D5700A6BC4D /* UserStatementGenerateEndPoint.swift */,
			);
			path = Report;
			sourceTree = "<group>";
		};
		DF0AD8462BD769570098FDAF /* Authentication */ = {
			isa = PBXGroup;
			children = (
				DF0AD8472BD769570098FDAF /* ChangePasswordEndPoint.swift */,
				DF0AD8482BD769570098FDAF /* PasswordVerificationEndPoint.swift */,
				DF70B63D2C2D1A4700FDDA40 /* OTPRequestEndPoint.swift */,
				DF70B63F2C2D38D900FDDA40 /* OCRRequestEndPoint.swift */,
				DF742C572C32F239004C1EE9 /* UserTwoFAEndPoint.swift */,
				DF742C592C32FCDD004C1EE9 /* UserAuthEndPoint.swift */,
				DF742C5B2C33CE35004C1EE9 /* UserAuthResetVerificationEndPoint.swift */,
				DF742C5D2C33D05B004C1EE9 /* UserAuthResetEndPoint.swift */,
			);
			path = Authentication;
			sourceTree = "<group>";
		};
		DF273CE72BD76797003F9464 = {
			isa = PBXGroup;
			children = (
				DF273CF32BD76797003F9464 /* APILayer */,
				DF273CF22BD76797003F9464 /* Products */,
				0A33421152BD4BDAA03D0A21 /* Pods */,
				E4F8A392E8E8419841128301 /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		DF273CF22BD76797003F9464 /* Products */ = {
			isa = PBXGroup;
			children = (
				DF273CF12BD76797003F9464 /* APILayer.framework */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		DF273CF32BD76797003F9464 /* APILayer */ = {
			isa = PBXGroup;
			children = (
				DF0AD83B2BD769570098FDAF /* Bases */,
				DFF8478F2C216F3800A639E6 /* Onboarding */,
				DF0AD8462BD769570098FDAF /* Authentication */,
				DF0AD81E2BD769570098FDAF /* Common */,
				DF0AD8412BD769570098FDAF /* History */,
				DF0AD81C2BD769570098FDAF /* Home */,
				DF0AD8352BD769570098FDAF /* Login */,
				DF0AD7F22BD769570098FDAF /* Market */,
				DF0AD8302BD769570098FDAF /* Notification */,
				DF0AD8062BD769570098FDAF /* Order */,
				DF0AD8442BD769570098FDAF /* Report */,
				DF0AD8212BD769570098FDAF /* TradingRegister */,
				DF0AD8292BD769570098FDAF /* Wallet */,
				83319F362CE342BF00F78916 /* WealthPlan */,
				DF0AD7EE2BD769570098FDAF /* WebSocket */,
				DF273CF42BD76797003F9464 /* APILayer.h */,
			);
			path = APILayer;
			sourceTree = "<group>";
		};
		DFF8478F2C216F3800A639E6 /* Onboarding */ = {
			isa = PBXGroup;
			children = (
				DFF847902C216F5800A639E6 /* QuestionarieFetchEndpoint.swift */,
				DFC0B54A2C240D0F00C739A8 /* QuestionarieUpdateEndPoint.swift */,
				DFC0B5442C23DDC600C739A8 /* UserAgreementFetchEndPoint.swift */,
				DFC0B5482C23FF1200C739A8 /* UserAgreementUpdateEndPoint.swift */,
				DF22EEFE2C2BDF5A00279645 /* UserFileUploadEndPoint.swift */,
				DF22EF002C2BE08100279645 /* UserDocumentUpdateEndPoint.swift */,
				DFE669F02C351D28000BB0FE /* UserFetchDocumentFetchEndPoint.swift */,
				DF70B63B2C2D144D00FDDA40 /* UserLivenessEndPoint.swift */,
				DF7B9DC42C36AC5100600024 /* UserLivenessFetchEndPoint.swift */,
				************************ /* UserIdentityEndPoint.swift */,
				************************ /* UserRegisterEndPoint.swift */,
				DFB136B22C328E9D00A251B2 /* UserRegisterStepsEndPoint.swift */,
				DFB136B82C329E4900A251B2 /* UserInformationEndPoint.swift */,
				832D80022C35A25C00C37931 /* UserProfileEditEndPoint.swift */,
				832D80042C35BD7A00C37931 /* UserEmploymentEditEndPoint.swift */,
				832D80062C35C36300C37931 /* UserBankAccountEditEndPoint.swift */,
				832D800E2C3658EE00C37931 /* QueryUserBankListEndPoint.swift */,
				DFA0657D2C3CE484002D768C /* DeclarationCRSFetchEndpoint.swift */,
				DFA0657F2C3CE85C002D768C /* DeclarationCRSUpdateEndpoint.swift */,
				833B79112C3FAE4600EAEE0D /* UserDocuSignEndPoint.swift */,
				83A64E542D3A8F7700E9F3BE /* GenerationRegistrationInfoPdfEndPoint.swift */,
				83443CA42D03330C00BDC625 /* UpdateCustomerStatusEndPoint.swift */,
				833DB8ED2D410025002FCCC7 /* OnboardingCheckEndPoint.swift */,
			);
			path = Onboarding;
			sourceTree = "<group>";
		};
		E4F8A392E8E8419841128301 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				DF0AE0312BD76C820098FDAF /* Storage.framework */,
				DF0AE02D2BD76C770098FDAF /* SharedData.framework */,
				6F95A0885E483161EB303ACF /* Pods_APILayer.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		************************ /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = **********;
			files = (
				DF273CF52BD76797003F9464 /* APILayer.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		DF273CF02BD76797003F9464 /* APILayer */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = DF273CF82BD76797003F9464 /* Build configuration list for PBXNativeTarget "APILayer" */;
			buildPhases = (
				D906CE1CA74F2D37BBD0F6CA /* [CP] Check Pods Manifest.lock */,
				************************ /* Headers */,
				DF273CED2BD76797003F9464 /* Sources */,
				DF273CEE2BD76797003F9464 /* Frameworks */,
				DF273CEF2BD76797003F9464 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = APILayer;
			productName = APILayer;
			productReference = DF273CF12BD76797003F9464 /* APILayer.framework */;
			productType = "com.apple.product-type.framework";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		DF273CE82BD76797003F9464 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastUpgradeCheck = 1500;
				TargetAttributes = {
					DF273CF02BD76797003F9464 = {
						CreatedOnToolsVersion = 15.0;
					};
				};
			};
			buildConfigurationList = DF273CEB2BD76797003F9464 /* Build configuration list for PBXProject "APILayer" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = DF273CE72BD76797003F9464;
			productRefGroup = DF273CF22BD76797003F9464 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				DF273CF02BD76797003F9464 /* APILayer */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		DF273CEF2BD76797003F9464 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		D906CE1CA74F2D37BBD0F6CA /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-APILayer-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		DF273CED2BD76797003F9464 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				DF70B63E2C2D1A4700FDDA40 /* OTPRequestEndPoint.swift in Sources */,
				DF0AD84B2BD769570098FDAF /* WebSocketController.swift in Sources */,
				DF0AD88D2BD769570098FDAF /* URLEnvironment.swift in Sources */,
				DF0AD8792BD769570098FDAF /* CustomerFileEndpoint.swift in Sources */,
				DF0AD84C2BD769570098FDAF /* MarketUpdateFavoriteEndpoint.swift in Sources */,
				83DA72C92D1345AE00CA4BD7 /* GetUserInfoEndPoint.swift in Sources */,
				DF0AD88A2BD769570098FDAF /* VerifyDuplicationEndPoint.swift in Sources */,
				83D977CA2CF64D5700A6BC4D /* UserStatementGenerateEndPoint.swift in Sources */,
				DF0AD8642BD769570098FDAF /* CreateAutoOrderEndPoint.swift in Sources */,
				DF0AD85E2BD769570098FDAF /* MarketProfileFinancialsEndPoint.swift in Sources */,
				83D977CC2CF75B3700A6BC4D /* FileDownloadEndPoint.swift in Sources */,
				DF0AD87A2BD769570098FDAF /* BanksATSEndpoint.swift in Sources */,
				DF0AD8942BD769570098FDAF /* ChangePasswordEndPoint.swift in Sources */,
				DF0AD8732BD769570098FDAF /* GetMarketNewsEndPoint.swift in Sources */,
				DF0AD88B2BD769570098FDAF /* UserInfoEndpoint.swift in Sources */,
				83A64E552D3A8F7700E9F3BE /* GenerationRegistrationInfoPdfEndPoint.swift in Sources */,
				DF0AD8762BD769570098FDAF /* SuitTestQuestionEndpoint.swift in Sources */,
				8379B3FD2D15E17D004D3006 /* QueryUserProfileEndPoint.swift in Sources */,
				83D977D72CF8C79900A6BC4D /* OrderHistoryDetailEndPoint.swift in Sources */,
				DF0AD87B2BD769570098FDAF /* TradingAccountOpeningEndPoint.swift in Sources */,
				833DB8EE2D410025002FCCC7 /* OnboardingCheckEndPoint.swift in Sources */,
				83319F382CE342DE00F78916 /* WealthPlanDetailEndPoint.swift in Sources */,
				DF0AD8562BD769570098FDAF /* MarketInstrumentTagListEndPoint.swift in Sources */,
				DF0AD8812BD769570098FDAF /* WalletSummaryEndpoint.swift in Sources */,
				830D0F362D54ADBC00B5A130 /* ContentBannerListEndPoint.swift in Sources */,
				DF0AD8512BD769570098FDAF /* MarketSimpleChartEndPoint.swift in Sources */,
				DF0AD8752BD769570098FDAF /* FAQListEndPoint.swift in Sources */,
				DFEF861D2C3F9291009E2C19 /* CurrencyEndpoint.swift in Sources */,
				DF0AD85C2BD769570098FDAF /* MarketLastMatchesEndPoint.swift in Sources */,
				DF4CDC5D2C451C730095BFC7 /* WalletStatisticEndpoint.swift in Sources */,
				0C8F52A32E5C2102003DAAE0 /* MarketProductCategoryListEndPoint.swift in Sources */,
				DF0AD85A2BD769570098FDAF /* MarketListEndpoint.swift in Sources */,
				83F6C6472D0875BA001158C0 /* DeleteNotificationEndPoint.swift in Sources */,
				DF0AD8652BD769570098FDAF /* ModifyAutoOrderEndPoint.swift in Sources */,
				83D977D92CF8C7BD00A6BC4D /* TransactionDetailEndPoint.swift in Sources */,
				DF70B63C2C2D144D00FDDA40 /* UserLivenessEndPoint.swift in Sources */,
				DF0AD8882BD769570098FDAF /* EditCustomerProfileEndPoint.swift in Sources */,
				83D977B02CEF32BE00A6BC4D /* UserNotificationEndPoint.swift in Sources */,
				DF70B6402C2D38D900FDDA40 /* OCRRequestEndPoint.swift in Sources */,
				DFF847912C216F5800A639E6 /* QuestionarieFetchEndpoint.swift in Sources */,
				83F6C6492D087676001158C0 /* SaveDeviceEndPoint.swift in Sources */,
				DF0AD8502BD769570098FDAF /* MarketProfileFundamentalsEndPoint.swift in Sources */,
				83DA72CB2D13467D00CA4BD7 /* UpdateUserInfoEndPoint.swift in Sources */,
				DF0AD8922BD769570098FDAF /* TransactionListEndPoint.swift in Sources */,
				DF0AD86C2BD769570098FDAF /* CancelOfflineOrderEndPoint.swift in Sources */,
				DF742C582C32F239004C1EE9 /* UserTwoFAEndPoint.swift in Sources */,
				DF0AD8902BD769570098FDAF /* APILayer.swift in Sources */,
				DF0AD8692BD769570098FDAF /* CancelOrderThaiEquityEndPoint.swift in Sources */,
				DF0AD8772BD769570098FDAF /* BanksNDIDEndpoint.swift in Sources */,
				831C07E02C2A7D9700A1BB94 /* EnumerationEndPoint.swift in Sources */,
				DF0AD86B2BD769570098FDAF /* QuickOrderDeleteEndPoint.swift in Sources */,
				DF0AD87D2BD769570098FDAF /* DepositEndPoint.swift in Sources */,
				DF0AD8722BD769570098FDAF /* OrderSettingQueryEndPoint.swift in Sources */,
				83D977DB2CF8C7FB00A6BC4D /* TransactionHistoryListEndPoint.swift in Sources */,
				DF0AD8602BD769570098FDAF /* QuickOrderSaveEndPoint.swift in Sources */,
				83D977B32CEF39B900A6BC4D /* UserNotificationReadEndPoint.swift in Sources */,
				DF0AD86E2BD769570098FDAF /* QuickOrderSettingsEndPoint.swift in Sources */,
				DF742C5A2C32FCDD004C1EE9 /* UserAuthEndPoint.swift in Sources */,
				DF742C5C2C33CE35004C1EE9 /* UserAuthResetVerificationEndPoint.swift in Sources */,
				DF0AD8622BD769570098FDAF /* ModifyOrderGlobalEquityEndPoint.swift in Sources */,
				83DA72CD2D13475000CA4BD7 /* CheckUserInfoEndPoint.swift in Sources */,
				DF0AD84D2BD769570098FDAF /* MarketListDetailEndPoint.swift in Sources */,
				83FE18C82D6C2E780022819E /* CommonConfigEndPoint.swift in Sources */,
				832D80052C35BD7A00C37931 /* UserEmploymentEditEndPoint.swift in Sources */,
				DF0AD87C2BD769570098FDAF /* SuitTestAnswerEndpoint.swift in Sources */,
				83319F352CE342A000F78916 /* WealthPlanListEndPoint.swift in Sources */,
				DF0AD8712BD769570098FDAF /* CreateOfflineOrderEndPoint.swift in Sources */,
				DF7B9DC52C36AC5100600024 /* UserLivenessFetchEndPoint.swift in Sources */,
				DF0AD88F2BD769570098FDAF /* DownloadService.swift in Sources */,
				83443CA52D03330C00BDC625 /* UpdateCustomerStatusEndPoint.swift in Sources */,
				DF0AD84E2BD769570098FDAF /* MarketGetFavoriteEndPoint.swift in Sources */,
				DF0AD8522BD769570098FDAF /* MarketOrderBookEndPoint.swift in Sources */,
				83319F3A2CE342F500F78916 /* UpdateWealthPlanEndPoint.swift in Sources */,
				DF0AD8492BD769570098FDAF /* WSSubscriber.swift in Sources */,
				DF22EF012C2BE08100279645 /* UserDocumentUpdateEndPoint.swift in Sources */,
				83D977B52CF1C9C900A6BC4D /* MarketWatchListEndPoint.swift in Sources */,
				832D800F2C3658EE00C37931 /* QueryUserBankListEndPoint.swift in Sources */,
				DF0AD85D2BD769570098FDAF /* MarketProfileOverviewEndPoint.swift in Sources */,
				DF0AD85F2BD769570098FDAF /* ModifyOrderThaiEquityEndPoint.swift in Sources */,
				DF0AD8702BD769570098FDAF /* ModifyOfflineOrderEndPoint.swift in Sources */,
				DF0AD8782BD769570098FDAF /* TradeAccountTypesEndPoint.swift in Sources */,
				DF0AD8542BD769570098FDAF /* MarketQuoteActivityEndPoint.swift in Sources */,
				DF0AD8842BD769570098FDAF /* MessageEndPoint.swift in Sources */,
				833B79122C3FAE4600EAEE0D /* UserDocuSignEndPoint.swift in Sources */,
				DF0AD84F2BD769570098FDAF /* MarketChartCandlesEndPoint.swift in Sources */,
				DF0AD85B2BD769570098FDAF /* RecentlyViewedListEndPoint.swift in Sources */,
				DFB136B32C328E9D00A251B2 /* UserRegisterStepsEndPoint.swift in Sources */,
				DFC0B5452C23DDC600C739A8 /* UserAgreementFetchEndPoint.swift in Sources */,
				DF0AD84A2BD769570098FDAF /* WSModels.swift in Sources */,
				DF0AD8802BD769570098FDAF /* CashWithdrawEndPoint.swift in Sources */,
				DF0AD8612BD769570098FDAF /* SaveOrderSettingEndPoint.swift in Sources */,
				DF0AD8742BD769570098FDAF /* AddressInfoEndPoint.swift in Sources */,
				DF0AD8672BD769570098FDAF /* CreateUpdateAutoOrderBase.swift in Sources */,
				DF0AD86A2BD769570098FDAF /* OfflineOrderListEndPoint.swift in Sources */,
				DF0AD86D2BD769570098FDAF /* CancelOrderGlobalEquityEndPoint.swift in Sources */,
				DF0AD8592BD769570098FDAF /* MarketInstrumentSearchEndPoint.swift in Sources */,
				DF0AD88E2BD769570098FDAF /* Extensions.swift in Sources */,
				************************ /* UserRegisterEndPoint.swift in Sources */,
				DFC0B5492C23FF1200C739A8 /* UserAgreementUpdateEndPoint.swift in Sources */,
				DF0AD87E2BD769570098FDAF /* WalletPositionDetailEndpoint.swift in Sources */,
				DF0AD8832BD769570098FDAF /* MessageDeleteEndPoint.swift in Sources */,
				DF2B67E62C5B653A00A5DE18 /* BenchmarkDataEndpoint.swift in Sources */,
				83DA72C42D10798700CA4BD7 /* UserLogoutEndPoint.swift in Sources */,
				832D80072C35C36400C37931 /* UserBankAccountEditEndPoint.swift in Sources */,
				DF0AD8552BD769570098FDAF /* MarketAdvanceSearchEndPoint.swift in Sources */,
				************************ /* UserAuthResetEndPoint.swift in Sources */,
				83D977D52CF8C75E00A6BC4D /* OrderHistoryListEndPoint.swift in Sources */,
				DF0AD8932BD769570098FDAF /* MonthlyReportGenerationEndPoint.swift in Sources */,
				DFA065802C3CE85C002D768C /* DeclarationCRSUpdateEndpoint.swift in Sources */,
				83319F3C2CE3430D00F78916 /* WealthPlanPerformanceEndPoint.swift in Sources */,
				DFE669F12C351D28000BB0FE /* UserFetchDocumentFetchEndPoint.swift in Sources */,
				DFA0657E2C3CE484002D768C /* DeclarationCRSFetchEndpoint.swift in Sources */,
				DFC0B54B2C240D0F00C739A8 /* QuestionarieUpdateEndPoint.swift in Sources */,
				832D80032C35A25C00C37931 /* UserProfileEditEndPoint.swift in Sources */,
				DF0AD8572BD769570098FDAF /* RecentlyViewedSaveEndPoint.swift in Sources */,
				DF0AD8952BD769570098FDAF /* PasswordVerificationEndPoint.swift in Sources */,
				DF75AD652C2E6E67006B86B1 /* UserLoginEndPoint.swift in Sources */,
				DF0AD8582BD769570098FDAF /* MarketInstrumentBaseEndPoint.swift in Sources */,
				DFB136B92C329E4900A251B2 /* UserInformationEndPoint.swift in Sources */,
				DF0AD88C2BD769570098FDAF /* BaseResponse.swift in Sources */,
				DF22EEFF2C2BDF5A00279645 /* UserFileUploadEndPoint.swift in Sources */,
				DF0AD87F2BD769570098FDAF /* WalletBalanceHistoryEndpoint.swift in Sources */,
				83DA72C22D102C6B00CA4BD7 /* HistoricalGainLostEndPoint.swift in Sources */,
				DF70B6422C2D4EE500FDDA40 /* UserIdentityEndPoint.swift in Sources */,
				DF0AD8662BD769570098FDAF /* DeleteAutoOrderEndPoint.swift in Sources */,
				831943CD2CCCD88F00A46862 /* MarketInstrumentDetailEndPoint.swift in Sources */,
				DF0AD8532BD769570098FDAF /* MarketIntradayChartEndPoint.swift in Sources */,
				DF0AD8912BD769570098FDAF /* MyOrderListEndPoint.swift in Sources */,
				DF0AD86F2BD769570098FDAF /* PlaceOrderGlobalEquityEndPoint.swift in Sources */,
				DF2B67E42C5B604E00A5DE18 /* BenchmarkListEndpoint.swift in Sources */,
				DF0AD8682BD769570098FDAF /* PlaceOrderThaiEquityEndPoint.swift in Sources */,
				DF0AD8852BD769570098FDAF /* MessageEditEndPoint.swift in Sources */,
				DF0AD8632BD769570098FDAF /* ListAutoOrderEndPoint.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		DF273CF62BD76797003F9464 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		DF273CF72BD76797003F9464 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		DF273CF92BD76797003F9464 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = A8AB14A50903C1729F87C2EA /* Pods-APILayer.debug.xcconfig */;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				MODULE_VERIFIER_SUPPORTED_LANGUAGES = "objective-c objective-c++";
				MODULE_VERIFIER_SUPPORTED_LANGUAGE_STANDARDS = "gnu17 gnu++20";
				PRODUCT_BUNDLE_IDENTIFIER = com.sirius.APILayer;
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		DF273CFA2BD76797003F9464 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 66A4B1A513C47457A139C6BC /* Pods-APILayer.release.xcconfig */;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				MODULE_VERIFIER_SUPPORTED_LANGUAGES = "objective-c objective-c++";
				MODULE_VERIFIER_SUPPORTED_LANGUAGE_STANDARDS = "gnu17 gnu++20";
				PRODUCT_BUNDLE_IDENTIFIER = com.sirius.APILayer;
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		DF273CEB2BD76797003F9464 /* Build configuration list for PBXProject "APILayer" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				DF273CF62BD76797003F9464 /* Debug */,
				DF273CF72BD76797003F9464 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		DF273CF82BD76797003F9464 /* Build configuration list for PBXNativeTarget "APILayer" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				DF273CF92BD76797003F9464 /* Debug */,
				DF273CFA2BD76797003F9464 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = DF273CE82BD76797003F9464 /* Project object */;
}
